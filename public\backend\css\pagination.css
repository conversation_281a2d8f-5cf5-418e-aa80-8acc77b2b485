nav .pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}

nav .pagination .page-item:first-child .page-link {
  border-radius: 50%;
}

nav .pagination .page-item:last-child .page-link {
  border-radius: 50%;
}

nav .pagination .page-item .page-link {
  line-height: 31px;
  min-width: 35px;
  width: 100%;
  height: 35px;
  position: relative;
  display: block;
  text-align: center;
  font-size: 15px;
  font-weight: 700;
  text-decoration: none;
  border-radius: 50%;
  padding: unset;
  -webkit-transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
  transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out,-webkit-box-shadow .15s ease-in-out;
}
/*# sourceMappingURL=pagination.css.map */