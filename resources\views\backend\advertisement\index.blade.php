@extends('backend.layouts.master')
@section('content')
<style>
.select2-container {
    z-index: 9999;
}
</style>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4>{{ $page_title ?? '' }}</h4>
            <div class="btn-group">
                <div>
                    <a href="{{ route('advertisement.create') }}" class="eg-btn btn--primary back-btn"><img
                        src="{{ asset('backend/images/icons/add-new.svg') }}" alt="{{ translate('Add New') }}">
                    {{ translate('Add New') }}</a>
                   
                </div>
            </div>
        </div>
    </div>
    @php
        $locale = get_setting('DEFAULT_LANGUAGE', 'en');
    @endphp
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table category-table">
                    <thead>
                        <tr>
                            <th>{{ translate('S.N') }}</th>
                            <th>{{ translate('Attributes') }}</th>
                            <th>{{ translate('Text') }}</th>
                            <th>{{ translate('Phone') }}</th>
                            <th>{{ translate('Image') }}</th>
                            <th>{{ translate('Option') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if($advertisement->count() > 0)
                        @foreach ($advertisement as $key => $advertisement)
                            <tr>
                                <td data-label="S.N">
                                    {{   $key + 1 }}
                                </td>
                                <td data-label="Name">{{ $advertisement->title }}</td>
                                <td data-label="Slug">{{ $advertisement->text }}</td>
                                 <td data-label="Slug">{{ $advertisement->phone }}</td>
                                <td data-label="Date"><img src="{{asset('uploads/advertisement/'.$advertisement->image)}}" alt="{{ $advertisement->image }}"></td>
                               
                                <td data-label="Option">
                                    <div
                                        class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                        <a class="eg-btn add--btn"
                                            href="{{ route('advertisement.edit', ['id' => $advertisement->id, 'lang' => get_setting('DEFAULT_LANGUAGE', 'en')]) }}"><i
                                                class="bi bi-pencil-square"></i></a>
                                        <form method="POST" action="{{ route('advertisement.delete', $advertisement->id) }}">
                                            @csrf
                                            @method('DELETE')
                                            <input name="_method" type="hidden" value="DELETE">
                                            <button type="submit" class="eg-btn delete--btn show_confirm"
                                                data-toggle="tooltip" title='Delete'><i class="bi bi-trash"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="7" class="text-center">{{translate('No Data Found')}}</td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>

  

    @include('backend.tours.category.modal')
@endsection
