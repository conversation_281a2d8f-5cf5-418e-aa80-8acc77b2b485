@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100;0,9..40,200;0,9..40,300;0,9..40,400;0,9..40,500;0,9..40,600;0,9..40,700;0,9..40,800;0,9..40,900;0,9..40,1000;1,9..40,100;1,9..40,200;1,9..40,300;1,9..40,400;1,9..40,500;1,9..40,600;1,9..40,700;1,9..40,800;1,9..40,900;1,9..40,1000&family=Lexend:wght@100;200;300;400;500;600;700;800;900&display=swap");
* {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

:root {
  --title-color: #000;
  --primary-color: #8E44EC;
  --text-color: #595959;
  --white:#FFFFFF;
  --font-lexend: "Lexend", sans-serif;
  --font-dm-sans: "DM Sans", sans-serif;
}

/*================================================
1. Mixins Css
=================================================*/
/*================================================
2. Global Css
=================================================*/
html {
  font-size: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  color: var(--title-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  box-sizing: border-box;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--title-color);
  font-family: var(--font-lexend);
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pt-70 {
  padding-top: 70px;
}

.mb-140 {
  margin-bottom: 140px;
}
@media (max-width: 991px) {
  .mb-140 {
    margin-bottom: 30px;
  }
}

.pt-50 {
  padding-top: 50px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-70 {
  padding-bottom: 70px;
}

.mb-100 {
  margin-bottom: 100px;
}
@media (max-width: 767px) {
  .mb-100 {
    margin-bottom: 40px;
  }
}

.mb-60 {
  margin-bottom: 60px;
}
@media (max-width: 767px) {
  .mb-60 {
    margin-bottom: 40px;
  }
}

.mb-65 {
  margin-bottom: 65px;
}
@media (max-width: 767px) {
  .mb-65 {
    margin-bottom: 40px;
  }
}

.mt-120 {
  margin-top: 120px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .mt-120 {
    margin-top: 100px;
  }
}
@media (max-width: 991px) {
  .mt-120 {
    margin-top: 90px;
  }
}

.mb-120 {
  margin-bottom: 120px;
}

.mb-100 {
  margin-bottom: 100px;
}
@media (max-width: 767px) {
  .mb-100 {
    margin-bottom: 40px;
  }
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-70 {
  margin-bottom: 70px;
}
@media (max-width: 767px) {
  .mb-70 {
    margin-bottom: 40px;
  }
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-44 {
  margin-bottom: 44px;
}
@media (max-width: 991px) {
  .mb-44 {
    margin-bottom: 0px;
  }
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-30 {
  margin-top: 30px !important;
}

.mt-40 {
  margin-top: 40px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mt-60 {
  margin-top: 60px;
}
@media (max-width: 767px) {
  .mt-60 {
    margin-top: 40px;
  }
}

.mt-65 {
  margin-top: 65px;
}
@media (max-width: 767px) {
  .mt-65 {
    margin-top: 45px;
  }
}

.mt-70 {
  margin-top: 70px;
}
@media (max-width: 767px) {
  .mt-70 {
    margin-top: 40px;
  }
}

.padding-left-right {
  padding: 0 8%;
}
@media (max-width: 767px) {
  .padding-left-right {
    padding: 0;
  }
}

.step-btn-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding-top: 50px;
  z-index:999;
}
@media (max-width: 1199px) {
  .step-btn-group {
    padding-top: 30px;
  }
}
.step-btn-group .previous-btn {
  border-radius: 10px;
  background-color: var(--white);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  font-family: var(--font-dm-sans);
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 38px;
  cursor: pointer;
}
.step-btn-group .previous-btn svg {
  fill: var(--primary-color);
}
.step-btn-group .next-btn {
  border-radius: 10px;
  background: #151515;
  color: var(--white);
  font-family: var(--font-dm-sans);
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 16px 38px;
  cursor: pointer;
}
.step-btn-group .next-btn svg {
  fill: var(--white);
}

.installment-section {
  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.25) 100%), url(../images/bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.installment-section .installment-wrap {
  max-width: 1170px;
  width: 100%;
  background-color: #F7F7F7;
  border-radius: 10px;
  padding: 35px;
  display: flex;
  gap: 30px;
}
@media (max-width: 1199px) {
  .installment-section .installment-wrap {
    padding: 10px;
  }
}
@media (max-width: 991px) {
  .installment-section .installment-wrap {
    flex-wrap: wrap;
  }
}
.installment-section .installment-wrap .installment-form-step {
  margin: 0;
  padding: 0;
  list-style: none;
  width: 290px;
  background-color: var(--white);
  border-radius: 5px;
  padding: 50px 30px;
}
@media (max-width: 1199px) {
  .installment-section .installment-wrap .installment-form-step {
    padding: 30px 20px;
  }
}
.installment-section .installment-wrap .installment-form-step li {
  line-height: 1;
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
  margin-bottom: 40px;
}
.installment-section .installment-wrap .installment-form-step li:last-child {
  margin-bottom: 0;
}
.installment-section .installment-wrap .installment-form-step li:last-child .icon::after {
  display: none;
  visibility: hidden;
}
.installment-section .installment-wrap .installment-form-step li .icon {
  height: 40px;
  min-width: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  justify-content: center;
  background-color: #F5E6FF;
  position: relative;
}
.installment-section .installment-wrap .installment-form-step li .icon svg {
  fill: #151515;
}
.installment-section .installment-wrap .installment-form-step li .icon::after {
  content: "";
  height: 40px;
  width: 2px;
  background-color: #eee;
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
}
.installment-section .installment-wrap .installment-form-step li p {
  margin-bottom: 0;
  color: var(--text-color);
  font-family: var(--font-lexend);
  font-size: 16px;
  font-weight: 500;
  width: 100%;
}
.installment-section .installment-wrap .installment-form-step li .check {
  min-width: 8px;
  max-width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 5px solid #BDBDBD;
}
.installment-section .installment-wrap .installment-form-step li .check svg {
  opacity: 0;
}
.installment-section .installment-wrap .installment-form-step li.processing .icon {
  background-color: var(--title-color);
}
.installment-section .installment-wrap .installment-form-step li.processing .icon svg {
  fill: var(--white);
}
.installment-section .installment-wrap .installment-form-step li.processing .icon::after {
  background-color: var(--title-color);
}
.installment-section .installment-wrap .installment-form-step li.active .icon {
  background-color: var(--primary-color);
}
.installment-section .installment-wrap .installment-form-step li.active .icon svg {
  fill: var(--white);
}
.installment-section .installment-wrap .installment-form-step li.active .icon::after {
  background-color: var(--primary-color);
}
.installment-section .installment-wrap .installment-form-step li.active p {
  color: var(--title-color);
}
.installment-section .installment-wrap .installment-form-step li.active .check {
  min-width: 18px;
  max-width: 18px;
  height: 18px;
  border: 1px solid #63AB45;
  background-color: #63AB45;
  display: flex;
  align-items: center;
  justify-content: center;
}
.installment-section .installment-wrap .installment-form-step li.active .check svg {
  opacity: 1;
  fill: var(--white);
}
.installment-section .installment-wrap .installment-form-step li.active .check.red {
  background-color: red;
  border-color: red;
}
.installment-section .installment-wrap .installment-form {
  height: 100%;
  width: 100%;
  border-radius: 5px;
  background: var(--white);
  padding: 50px 50px;
  min-height: 540px;
}
@media (max-width: 1199px) {
  .installment-section .installment-wrap .installment-form {
    padding: 30px 25px;
  }
}
.installment-section .installment-wrap fieldset {
  opacity: 0;
  display: none;
  border: none;
  height: 100%;
  width: 100%;
  flex-direction: column;
  justify-content: space-between;
}
.installment-section .installment-wrap fieldset.active {
  opacity: 1;
  display: flex;
}
.installment-section .installment-wrap fieldset .step-title h3 {
  color: var(--title-color);
  font-family: var(--font-lexend);
  font-size: 37px;
  font-weight: 700;
  margin-bottom: 0;
}
.installment-section .installment-wrap fieldset .step-title p {
  color: var(--text-color);
  font-family: var(--font-dm-sans);
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  margin-bottom: 0;
  padding-top: 10px;
}
.installment-section .installment-wrap fieldset .installment-table {
  border-radius: 10px;
  border: 1px solid #EEE;
  padding: 5px;
  border-spacing: 0;
  width: 100%;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr {
  background: #FFF6FF;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td {
  color: var(--text-color);
  font-family: var(--font-lexend);
  font-size: 15px;
  font-weight: 400;
  border: unset;
  padding: 16px 25px;
  border-bottom: 5px solid #fff;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td strong {
  color: #151515;
  font-weight: 500;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td span {
  color: #151515;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td .vertion-and-stutas {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 10px;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td .vertion-and-stutas .check {
  height: 14px;
  width: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #63AB45;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td .vertion-and-stutas .check svg {
  fill: var(--white);
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td .vertion-and-stutas .check.red {
  background-color: red;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td:first-child {
  border-radius: 5px 0 0 5px;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr td :last-child {
  border-radius: 0 5px 5px 0;
}
.installment-section .installment-wrap fieldset .installment-table tbody tr:last-child td {
  border-bottom: none;
}
.installment-section .installment-wrap fieldset .enviroment-wrap {
  border-radius: 10px;
  border: 1px solid #EEE;
  padding: 20px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 35px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug h6 {
  margin-bottom: 0;
  color: #151515;
  font-family: var(--font-lexend);
  font-size: 15px;
  font-weight: 400;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap {
  display: flex;
  align-items: center;
  gap: 15px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap .form-check {
  position: relative;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap .form-check .form-check-input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap .form-check .form-check-label {
  color: #151515;
  font-family: var(--font-lexend);
  font-size: 14px;
  font-weight: 500;
  border-radius: 22px;
  background: #F7F7F7;
  padding: 6px 16px 6px 40px;
  line-height: 1;
  position: relative;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap .form-check .form-check-label::before {
  content: "";
  height: 14px;
  width: 14px;
  border-radius: 50%;
  border: 1px solid var(--primary-color);
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap .form-check .form-check-input:checked[type=radio] ~ .form-check-label {
  background-color: var(--primary-color);
  color: var(--white);
  cursor: pointer;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .app-debug .form-check-wrap .form-check .form-check-input:checked[type=radio] ~ .form-check-label::before {
  content: url(../images/check.svg);
  border: 1px solid #fff;
  background-color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 2px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 30px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner {
  border: 1px solid #eee;
  position: relative;
  border-radius: 5px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner label {
  color: #151515;
  font-family: var(--font-lexend);
  font-size: 15px;
  font-weight: 400;
  background-color: #fff;
  line-height: 1;
  position: absolute;
  left: 15px;
  top: -7.5px;
  padding: 0 15px;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner label::before {
  content: "";
  -webkit-clip-path: polygon(0 0, 0% 100%, 100% 50%);
          clip-path: polygon(0 0, 0% 100%, 100% 50%);
  background-color: #eee;
  width: 10px;
  height: 10px;
  display: inline-block;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner label:after {
  content: "";
  -webkit-clip-path: polygon(100% 0, 100% 100%, 0 50%);
          clip-path: polygon(100% 0, 100% 100%, 0 50%);
  background-color: #eee;
  width: 10px;
  height: 10px;
  display: inline-block;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner input {
  border: none;
  outline: none;
  height: 50px;
  padding: 0px 20px;
  background-color: transparent;
  width: 100%;
  color: var(--text-color);
  font-family: var(--font-dm-sans);
  font-size: 14px;
  font-weight: 500;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner input::-moz-placeholder {
  color: #a09c9c;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner input::placeholder {
  color: #a09c9c;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner textarea {
  border: none;
  outline: none;
  min-height: 150px;
  padding: 20px 20px;
  background-color: transparent;
  width: 100%;
  color: var(--text-color);
  font-family: var(--font-dm-sans);
  font-size: 14px;
  font-weight: 500;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner textarea::-moz-placeholder {
  color: #a09c9c;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner textarea::placeholder {
  color: #a09c9c;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .form-inner select {
  border: none;
  outline: none;
  height: 50px;
  padding: 0px 20px;
  background-color: transparent;
  width: 100%;
  color: var(--text-color);
  font-family: var(--font-dm-sans);
  font-size: 14px;
  font-weight: 500;
  position: relative;
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .w-50 {
  width: 47%;
}
@media (max-width: 1199px) {
  .installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .w-50 {
    width: 46%;
  }
}
@media (max-width: 991px) {
  .installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .w-50 {
    width: 100%;
  }
}
.installment-section .installment-wrap fieldset .enviroment-wrap .form-input-area .w-100 {
  width: 100%;
}


pre {
  overflow: auto;
  background: rgb(34, 34, 34);
  line-height: 1;
  color: rgb(255, 255, 0);
  border-radius: 3px;
  font-family: monospace;
  font-size: 1em;
  margin: 0px 0px 1.75em;
  padding: 22px 8px;
}
code {
    font-size: 1em;
    margin: 0px;
    font-family: "Courier New", Courier, "Lucida Sans Typewriter", "Lucida Typewriter", monospace;
    padding: 0.0875em 0.2625em;
    line-height: 0;
}


pre code {
    padding: 0px;
}



.text-danger.error-text{

    color: red;;
}

.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem;
    display: none;

}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}



  fieldset.Finished.active {
    position: relative;
}

.installment-section .installment-wrap fieldset.active {

    text-align: center;
}



fieldset.Finished .info {
    margin-bottom:50px;
}


  canvas#custom_canvas {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.d-none{
  display:none !important;
}

.Finished .info h2
{
    margin-top: 15px;

}
.Finished .congulation p
{
    font-family: var(--font-lexend);
    font-weight: 300;
    line-height: 30px;
    font-size: 19px;
    z-index: 9999999;
}



.Finished .access-user{
    display: flex;
    justify-content: space-between;
    text-align: left !important;
    font-family: var(--font-lexend);
    font-size: 15px;
    z-index: 9999999;
}

.access-user {
    margin-top: 30px;
}

.access-user h2 {
    font-size: 18px;
}


.loader-installer {
    width: 100%;
    height: 5px;
    background-color: #e1e1e1;
    margin: 0 auto;
    margin-top: 30px;
    z-index: 2;
  }



  .bar1 {
    width: 0px;
    height: 5px;
    background-color: #1ec4ff;
    z-index: 1;
    animation: bar1 5s ease-out infinite;
  }

  @keyframes bar1 {
    from {width: 0px;}
    to {width: 100%;}
  }

.installer-hide{
  display: none;
}



.installment-section .installment-wrap fieldset .installment-table tbody tr td{
    padding: 10px 25px;
}

.demo-content .form-input-area .form-check{
    position: relative;
}
.demo-content .form-input-area .form-check .form-check-input{
    appearance: none;
}
.demo-content .form-input-area .form-check .form-check-label{
    color: #151515;
    font-family: var(--font-lexend);
    font-size: 16px;
    font-weight: 500;
    border-radius: 22px;
    line-height: 1;
    position: relative;
    cursor: pointer;
}
.demo-content .form-input-area .form-check .form-check-label::before{
    content: "";
    height: 20px;
    width: 20px;
    border-radius: 5px;
    border: 1px solid var(--primary-color);
    position: absolute;
    left: -28px;
    top: 50%;
    transform: translateY(-50%);

}
.demo-content .form-input-area .form-check .form-check-input:checked[type=checkbox] ~ .form-check-label::before {
    content: url(../images/check.svg);
    background-color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 2px;
}

.step-btn-group-import {
    text-align: center;
    margin-top: 50px;
    display: flex;
    justify-content: center;
}

.btn-process{

    font-size: 1.5rem;
    background: #151515;
    border:none;
    outline:none;
    display: flex;
    align-items:center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 3rem;
    border-radius:1rem;
    color:#fff;
    cursor:pointer;
  }
  .btn-ring{
    display: none;
  }
  .btn-ring:after {
    content: "";
    display: block;
    width: 25px;
    height: 25px;
    margin: 8px;
    border-radius: 50%;
    border: 3px solid #fff;
    border-color: #fff transparent #fff transparent;
    animation: ring 1.2s linear infinite;
  }
  @keyframes ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
