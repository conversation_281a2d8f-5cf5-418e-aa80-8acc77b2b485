@extends('backend.layouts.master')
@section('content')
<style>
.select2-container {
    z-index: 9999;
}
</style>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4>{{ $page_title ?? '' }}</h4>
            <div class="btn-group">
            <button type="button" class="eg-btn btn--primary back-btn me-1" data-bs-toggle="modal"
                data-bs-target="#staticBackdrop"><img src="{{ asset('backend/images/icons/add-icon.svg') }}" alt="Add New">
                {{ translate('Add New') }}</button>
                <a href="{{route('transports.attribute.list')}}" class="eg-btn btn--primary back-btn"> <img src="{{asset('backend/images/icons/back.svg')}}" alt="{{ translate('Go Back') }}"> {{ translate('Go Back') }}</a>
            </div>
        </div>
    </div>
    @php
        $locale = get_setting('DEFAULT_LANGUAGE', 'en');
    @endphp
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table category-table">
                    <thead>
                        <tr>
                            <th>{{ translate('S.N') }}</th>
                            <th>{{ translate('Term Name') }}</th>
                            <th>{{ translate('Date') }}</th>
                            <th>
                                @foreach (\App\Models\Language::all() as $key => $language)
                                    <img src="{{ asset('assets/img/flags/' . $language->code . '.png') }}" class="mr-2">
                                @endforeach
                            </th>
                            <th>{{ translate('Option') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if($attribute_terms->count() > 0)
                        @foreach ($attribute_terms as $key => $term)
                            <tr>
                                <td data-label="S.N">
                                    {{ ($attribute_terms->currentpage() - 1) * $attribute_terms->perpage() + $key + 1 }}</td>
                                <td data-label="Terms Name">{{ $term->getTranslation('name') }}</td>
                                <td data-label="Date">{{ dateFormat($term->created_at) }}</td>
                                <td data-label="Language">
                                    @foreach (\App\Models\Language::all() as $key => $language)
                                        @if ($locale == $language->code)
                                            <i class="text-success bi bi-check-lg"></i>
                                        @else
                                            <a
                                                href="{{ route('transports.attribute.terms.edit', ['attribute_id' => $attribute_id,'id' => $term->id, 'lang' => $language->code]) }}"><i
                                                    class="text--primary bi bi-pencil-square"></i></a>
                                        @endif
                                    @endforeach
                                </td>
                                <td data-label="Option">
                                    <div
                                        class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                        <a class="eg-btn add--btn"
                                            href="{{ route('transports.attribute.terms.edit', ['attribute_id' => $attribute_id,'id' => $term->id, 'lang' => get_setting('DEFAULT_LANGUAGE', 'en')]) }}"><i
                                                class="bi bi-pencil-square"></i></a>
                                        <form method="POST" action="{{ route('transports.attribute.terms.delete', ['attribute_id' => $attribute_id,'id' => $term->id]) }}">
                                            @csrf
                                            <input name="_method" type="hidden" value="DELETE">
                                            <button type="submit" class="eg-btn delete--btn show_confirm"
                                                data-toggle="tooltip" title='Delete'><i class="bi bi-trash"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="6" class="text-center">{{translate('No Data Found')}}</td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('footer')
        <div class="d-flex justify-content-center custom-pagination">
            {!! $attribute_terms->links() !!}
        </div>
    @endpush

    @include('backend.transports.attribute_terms.modal')
@endsection
