@extends('frontend.template-' . $templateId . '.partials.master')
@section('content')
    @include('frontend.template-' . $templateId . '.breadcrumb.breadcrumb')

    <div class="destination-details-wrap mb-120 pt-120">
        <div class="container">
            <div class="row g-lg-4 gy-5">
                <div class="col-lg-8">
                    <h2>{{ $destinations->getTranslation('title') }}</h2>
                    <p>{{ $destinations->short_desc }}</p>
                    <div class="destination-gallery mb-40 mt-40">
                        <div class="row g-4">
                            <div class="col-lg-4 col-sm-6">
                                <div class="gallery-img-wrap">
                                    @if (fileExists('uploads/destination/features/', $destinations->features_image) != false &&
                                            $destinations->features_image != null)
                                        <img src="{{ asset('uploads/destination/features/' . $destinations->features_image) }}"
                                            alt="{{ $destinations->title }}">
                                        <a data-fancybox="gallery-01"
                                            href="{{ asset('uploads/destination/features/' . $destinations->features_image) }}"><i
                                                class="bi bi-eye"></i>{{ $destinations->destination }}</a>
                                    @else
                                        <img src="{{ asset('uploads/author-cover-placeholder.webp') }}" alt="">
                                        <a data-fancybox="gallery-01"
                                            href="{{ asset('uploads/author-cover-placeholder.webp') }}"><i
                                                class="bi bi-eye"></i> {{ $destinations->destination }}</a>
                                    @endif

                                </div>
                            </div>
                            @foreach ($galleries->take(5) as $index => $images)
                                @php
                                    $colClassLg = '';
                                    $colClassSm = '';

                                    if ($index == 0) {
                                        $colClassLg = 'col-lg-5';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 1) {
                                        $colClassLg = 'col-lg-3';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 2) {
                                        $colClassLg = 'col-lg-3';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 3) {
                                        $colClassLg = 'col-lg-4';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 4) {
                                        $colClassLg = 'col-lg-5';
                                        $colClassSm = 'col-sm-6';
                                    } else 
                                @endphp 
                                <div class="{{ $colClassLg }} {{ $colClassSm }}">
                                    <div class="gallery-img-wrap">
                                        <img src="{{ asset('uploads/destination/gallery/' . $images->image) }}" alt="">
                                        <a data-fancybox="gallery-01" href="{{ asset('uploads/destination/gallery/' . $images->image) }}"><i
                                                class="bi bi-eye"></i></a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <p>{!! $destinations->getTranslation('content') !!}</p>
                </div>
                <div class="col-lg-4">
                    <div class="destination-sidebar">
                        <div class="destination-info mb-30">
                            <div class="single-info">
                                <span>{{ translate('Destination') }}:</span>
                                <h5>{{ $destinations->destination }}</h5>
                            </div>
                            <div class="single-info">
                                <span>{{ translate('Population') }}:</span>
                                <h5>{{ $destinations->population }}</h5>
                            </div>
                            <div class="single-info">
                                <span>{{ translate('Capital City') }}:</span>
                                <h5>{{ $destinations->city }}</h5>
                            </div>
                            <div class="single-info">
                                <span>{{ translate('Language') }}:</span>
                                <h5>{{ $destinations->language }}</h5>
                            </div>
                            <div class="single-info">
                                <span>{{ translate('Currency') }}:</span>
                                <h5>{{ $destinations->currency }}</h5>
                            </div>
                        </div>
                       
                    </div>
                </div>
            </div>
            
            <!-- Tours Section -->
            @if($tours->count() > 0)
            <div class="row mt-80">
                <div class="col-md-12">
                    <div class="section-title mb-50">
                        <h3>{{ translate('Available Tours in') }} {{ $destinations->destination }}</h3>
                        <p>{{ translate('Explore the best tours available for this destination') }}</p>
                    </div>
                </div>
            </div>
            <div class="row g-4">
                @foreach($tours as $tour)
                <div class="col-md-4">
                    <div class="tour-card h-100">
                        <div class="tour-thumb">
                           
                                <img src="{{ asset('uploads/tour/features/' . $tour->feature_img) }}" alt="{{ $tour->getTranslation('title') }}" class="img-fluid">
                            
                            <div class="tour-price">
                                <span>${{ number_format($tour->price, 2) }}</span>
                            </div>
                        </div>
                        <div class="tour-content p-3">
                            <div class="tour-meta mb-2">
                                <span class="duration">
                                    <i class="bi bi-clock"></i> {{ $tour->duration }} {{ translate('Days') }}
                                </span>
                                @if($tour->location)
                                <span class="location">
                                    <i class="bi bi-geo-alt"></i> {{ $tour->location }}
                                </span>
                                @endif
                            </div>
                            <h4 class="tour-title">
                                <a href="{{ route('tour.details', $tour->slug) }}">{{ $tour->getTranslation('title') }}</a>
                            </h4>
                            <p class="tour-description">{{ Str::limit(strip_tags($tour->getTranslation('content')), 100) }}</p>
                            <div class="tour-footer d-flex justify-content-between align-items-center mt-3">
                                <div class="tour-rating">
                                    @php
                                        $reviews = App\Models\Review::where('product_type', 'tour')->where('product_id', $tour->id)->count();
                                        $totalRating = App\Models\Review::where('product_type', 'tour')->where('product_id', $tour->id)->sum('rating');
                                        $avgRating = $reviews > 0 ? $totalRating / $reviews : 0;
                                    @endphp
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="bi bi-star{{ $i <= $avgRating ? '-fill' : '' }}"></i>
                                    @endfor
                                    <span class="rating-count">({{ $reviews }})</span>
                                </div>
                                <a href="{{ route('tour.details', $tour->slug) }}" class="btn btn-primary btn-sm">
                                    {{ translate('View Details') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <div class="row mt-80">
                <div class="col-md-12">
                    <div class="no-tours-found text-center py-5">
                        <h4>{{ translate('No Tours Available') }}</h4>
                        <p>{{ translate('There are currently no tours available for this destination.') }}</p>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>

    <style>
    .tour-card {
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background: #fff;
    }
    
    .tour-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    
    .tour-thumb {
        position: relative;
        overflow: hidden;
        height: 200px;
    }
    
    .tour-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .tour-card:hover .tour-thumb img {
        transform: scale(1.05);
    }
    
    .tour-price {
        position: absolute;
        top: 15px;
        right: 15px;
        background: #6C2EB9;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
    }
    
    .tour-meta {
        display: flex;
        gap: 15px;
        font-size: 13px;
        color: #666;
    }
    
    .tour-meta span {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .tour-title {
        margin: 15px 0 10px 0;
        font-size: 18px;
        line-height: 1.4;
    }
    
    .tour-title a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .tour-title a:hover {
        color: #6C2EB9;
    }
    
    .tour-description {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 0;
    }
    
    .tour-rating {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
    }
    
    .tour-rating .bi-star-fill {
        color: #ffc107;
    }
    
    .tour-rating .bi-star {
        color: #e9ecef;
    }
    
    .rating-count {
        color: #666;
        font-size: 12px;
        margin-left: 5px;
    }
    
    .section-title h3 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .section-title p {
        color: #666;
        margin-bottom: 0;
    }
    
    .no-tours-found {
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px dashed #dee2e6;
    }
    
    .no-tours-found h4 {
        color: #666;
        margin-bottom: 10px;
    }
    
    .no-tours-found p {
        color: #888;
        margin-bottom: 0;
    }
    </style>

@endsection
