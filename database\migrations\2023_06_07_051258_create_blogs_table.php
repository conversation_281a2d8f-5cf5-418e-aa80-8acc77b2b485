<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blogs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('category_id');
            $table->string('title');
            $table->string('meta_title')->nullable();
            $table->longText('meta_keyward')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('slug');
            $table->longText('description')->nullable();
            $table->string('image')->nullable();
            $table->longText('tags')->nullable();
            $table->integer('status')->default(1)->comment('Active=1, Inactive=2');
            $table->string('enable_seo')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('category_id')->references('id')->on('blog_categories')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blogs');
    }
};
