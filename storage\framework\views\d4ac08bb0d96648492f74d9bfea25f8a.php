<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('frontend.template-' . $templateId . '.breadcrumb.breadcrumb', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="destination-details-wrap mb-120 pt-120">
        <div class="container">
            <div class="row g-lg-4 gy-5">
                <div class="col-lg-8">
                    <h2><?php echo e($destinations->getTranslation('title')); ?></h2>
                    <p><?php echo e($destinations->short_desc); ?></p>
                    <div class="destination-gallery mb-40 mt-40">
                        <div class="row g-4">
                            <div class="col-lg-4 col-sm-6">
                                <div class="gallery-img-wrap">
                                    <?php if(fileExists('uploads/destination/features/', $destinations->features_image) != false &&
                                            $destinations->features_image != null): ?>
                                        <img src="<?php echo e(asset('uploads/destination/features/' . $destinations->features_image)); ?>"
                                            alt="<?php echo e($destinations->title); ?>">
                                        <a data-fancybox="gallery-01"
                                            href="<?php echo e(asset('uploads/destination/features/' . $destinations->features_image)); ?>"><i
                                                class="bi bi-eye"></i><?php echo e($destinations->destination); ?></a>
                                    <?php else: ?>
                                        <img src="<?php echo e(asset('uploads/author-cover-placeholder.webp')); ?>" alt="">
                                        <a data-fancybox="gallery-01"
                                            href="<?php echo e(asset('uploads/author-cover-placeholder.webp')); ?>"><i
                                                class="bi bi-eye"></i> <?php echo e($destinations->destination); ?></a>
                                    <?php endif; ?>

                                </div>
                            </div>
                            <?php $__currentLoopData = $galleries->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $images): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $colClassLg = '';
                                    $colClassSm = '';

                                    if ($index == 0) {
                                        $colClassLg = 'col-lg-5';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 1) {
                                        $colClassLg = 'col-lg-3';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 2) {
                                        $colClassLg = 'col-lg-3';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 3) {
                                        $colClassLg = 'col-lg-4';
                                        $colClassSm = 'col-sm-6';
                                    } elseif ($index == 4) {
                                        $colClassLg = 'col-lg-5';
                                        $colClassSm = 'col-sm-6';
                                    } else 
                                ?> 
                                <div class="<?php echo e($colClassLg); ?> <?php echo e($colClassSm); ?>">
                                    <div class="gallery-img-wrap">
                                        <img src="<?php echo e(asset('uploads/destination/gallery/' . $images->image)); ?>" alt="">
                                        <a data-fancybox="gallery-01" href="<?php echo e(asset('uploads/destination/gallery/' . $images->image)); ?>"><i
                                                class="bi bi-eye"></i></a>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <p><?php echo $destinations->getTranslation('content'); ?></p>
                </div>
                <div class="col-lg-4">
                    <div class="destination-sidebar">
                        <div class="destination-info mb-30">
                            <div class="single-info">
                                <span><?php echo e(translate('Destination')); ?>:</span>
                                <h5><?php echo e($destinations->destination); ?></h5>
                            </div>
                            <div class="single-info">
                                <span><?php echo e(translate('Population')); ?>:</span>
                                <h5><?php echo e($destinations->population); ?></h5>
                            </div>
                            <div class="single-info">
                                <span><?php echo e(translate('Capital City')); ?>:</span>
                                <h5><?php echo e($destinations->city); ?></h5>
                            </div>
                            <div class="single-info">
                                <span><?php echo e(translate('Language')); ?>:</span>
                                <h5><?php echo e($destinations->language); ?></h5>
                            </div>
                            <div class="single-info">
                                <span><?php echo e(translate('Currency')); ?>:</span>
                                <h5><?php echo e($destinations->currency); ?></h5>
                            </div>
                        </div>
                       
                    </div>
                </div>
            </div>
            
            <!-- Tours Section -->
            <?php if($tours->count() > 0): ?>
            <div class="row mt-80">
                <div class="col-md-12">
                    <div class="section-title mb-50">
                        <h3><?php echo e(translate('Available Tours in')); ?> <?php echo e($destinations->destination); ?></h3>
                        <p><?php echo e(translate('Explore the best tours available for this destination')); ?></p>
                    </div>
                </div>
            </div>
            <div class="row g-4">
                <?php $__currentLoopData = $tours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4">
                    <div class="tour-card h-100">
                        <div class="tour-thumb">
                           
                                <img src="<?php echo e(asset('uploads/tour/features/' . $tour->feature_img)); ?>" alt="<?php echo e($tour->getTranslation('title')); ?>" class="img-fluid">
                            
                            <div class="tour-price">
                                <span>$<?php echo e(number_format($tour->price, 2)); ?></span>
                            </div>
                        </div>
                        <div class="tour-content p-3">
                            <div class="tour-meta mb-2">
                                <span class="duration">
                                    <i class="bi bi-clock"></i> <?php echo e($tour->duration); ?> <?php echo e(translate('Days')); ?>

                                </span>
                                <?php if($tour->location): ?>
                                <span class="location">
                                    <i class="bi bi-geo-alt"></i> <?php echo e($tour->location); ?>

                                </span>
                                <?php endif; ?>
                            </div>
                            <h4 class="tour-title">
                                <a href="<?php echo e(route('tour.details', $tour->slug)); ?>"><?php echo e($tour->getTranslation('title')); ?></a>
                            </h4>
                            <p class="tour-description"><?php echo e(Str::limit(strip_tags($tour->getTranslation('content')), 100)); ?></p>
                            <div class="tour-footer d-flex justify-content-between align-items-center mt-3">
                                <div class="tour-rating">
                                    <?php
                                        $reviews = App\Models\Review::where('product_type', 'tour')->where('product_id', $tour->id)->count();
                                        $totalRating = App\Models\Review::where('product_type', 'tour')->where('product_id', $tour->id)->sum('rating');
                                        $avgRating = $reviews > 0 ? $totalRating / $reviews : 0;
                                    ?>
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <i class="bi bi-star<?php echo e($i <= $avgRating ? '-fill' : ''); ?>"></i>
                                    <?php endfor; ?>
                                    <span class="rating-count">(<?php echo e($reviews); ?>)</span>
                                </div>
                                <a href="<?php echo e(route('tour.details', $tour->slug)); ?>" class="btn btn-primary btn-sm">
                                    <?php echo e(translate('View Details')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php else: ?>
            <div class="row mt-80">
                <div class="col-md-12">
                    <div class="no-tours-found text-center py-5">
                        <h4><?php echo e(translate('No Tours Available')); ?></h4>
                        <p><?php echo e(translate('There are currently no tours available for this destination.')); ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <style>
    .tour-card {
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background: #fff;
    }
    
    .tour-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }
    
    .tour-thumb {
        position: relative;
        overflow: hidden;
        height: 200px;
    }
    
    .tour-thumb img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .tour-card:hover .tour-thumb img {
        transform: scale(1.05);
    }
    
    .tour-price {
        position: absolute;
        top: 15px;
        right: 15px;
        background: #6C2EB9;
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
    }
    
    .tour-meta {
        display: flex;
        gap: 15px;
        font-size: 13px;
        color: #666;
    }
    
    .tour-meta span {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .tour-title {
        margin: 15px 0 10px 0;
        font-size: 18px;
        line-height: 1.4;
    }
    
    .tour-title a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .tour-title a:hover {
        color: #6C2EB9;
    }
    
    .tour-description {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 0;
    }
    
    .tour-rating {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
    }
    
    .tour-rating .bi-star-fill {
        color: #ffc107;
    }
    
    .tour-rating .bi-star {
        color: #e9ecef;
    }
    
    .rating-count {
        color: #666;
        font-size: 12px;
        margin-left: 5px;
    }
    
    .section-title h3 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .section-title p {
        color: #666;
        margin-bottom: 0;
    }
    
    .no-tours-found {
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px dashed #dee2e6;
    }
    
    .no-tours-found h4 {
        color: #666;
        margin-bottom: 10px;
    }
    
    .no-tours-found p {
        color: #888;
        margin-bottom: 0;
    }
    </style>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.template-' . $templateId . '.partials.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/frontend/template-1/destination-details.blade.php ENDPATH**/ ?>