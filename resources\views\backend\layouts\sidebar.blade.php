<div class="sidebar-wrapper">
    <div class="sidebar">
        <div class="sidebar-menu-wrapper pb-110">
            <ul class="side-menu-list gap-3">
                <li @if (Illuminate\Support\Facades\Route::is('backend.dashboard')) class="active" @endif>
                    <a href="{{ route('backend.dashboard') }}">
                        <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M14.0449 0.140627C13.8574 0.216799 13.6055 0.351564 13.4883 0.433596C13.3652 0.515627 10.4062 3.45117 6.90233 6.95508C1.76952 12.0996 0.509752 13.3887 0.386705 13.6348C-0.175796 14.7715 0.0117046 16.0137 0.873033 16.8809C1.4121 17.4141 2.09764 17.6953 2.8828 17.6953H3.28124V22.3828C3.28124 25.5527 3.30467 27.1641 3.34569 27.375C3.56835 28.4414 4.42967 29.4082 5.49608 29.7773C5.8828 29.9121 5.9121 29.9121 8.74218 29.9121C11.5312 29.9121 11.5957 29.9121 11.7539 29.7891C11.8418 29.7246 11.9707 29.5957 12.0351 29.5078C12.1582 29.3496 12.1582 29.2734 12.1875 25.4355L12.2168 21.5215L12.3926 21.2344C12.6152 20.877 13.0664 20.5898 13.5 20.5371C13.6641 20.5137 14.4844 20.5078 15.3223 20.5195L16.8457 20.5371L17.1562 20.707C17.4492 20.8711 17.6367 21.0645 17.8183 21.3867C17.8828 21.5098 17.9062 22.2305 17.9297 25.4414C17.959 29.2734 17.959 29.3496 18.082 29.5078C18.1465 29.5957 18.2754 29.7246 18.3633 29.7891C18.5215 29.9121 18.5859 29.9121 21.375 29.9121C24.2051 29.9121 24.2344 29.9121 24.6211 29.7773C25.6875 29.4082 26.5488 28.4414 26.7715 27.375C26.8125 27.1641 26.8359 25.5527 26.8359 22.3828V17.6953H27.2344C28.3359 17.6953 29.2969 17.1094 29.7715 16.1426C29.9707 15.7383 29.9707 15.7266 29.9707 14.9414C29.9707 13.6055 30.6445 14.3965 23.1211 6.87891C15.6445 -0.603514 16.3945 0.0410175 15.1172 0.0117207C14.4551 1.90735e-06 14.3555 0.0117207 14.0449 0.140627ZM15.4922 1.81641C15.8379 1.98047 28.084 14.2441 28.2129 14.543C28.3359 14.8418 28.3242 15.0938 28.1777 15.3867C27.9609 15.8379 27.7793 15.9023 26.6484 15.9375C25.5879 15.9668 25.5117 15.9902 25.2305 16.3711C25.1074 16.5293 25.1074 16.5938 25.0781 21.8438L25.0488 27.1582L24.8848 27.4336C24.7969 27.5859 24.6387 27.7793 24.5332 27.8555C24.123 28.1719 24 28.1836 21.7617 28.1836H19.6875V24.9023C19.6875 22.752 19.6641 21.5215 19.623 21.3164C19.3945 20.2148 18.5098 19.2656 17.373 18.9082C16.9863 18.7852 16.8574 18.7793 15.0586 18.7793C12.9609 18.7793 12.8555 18.791 12.1055 19.1719C11.3613 19.5469 10.6699 20.4727 10.4941 21.3164C10.4531 21.5215 10.4297 22.752 10.4297 24.9023V28.1836H8.35546C6.11718 28.1836 5.99413 28.1719 5.58397 27.8555C5.4785 27.7793 5.3203 27.5859 5.23241 27.4336L5.06835 27.1582L5.03905 21.8438C5.00975 16.5938 5.00975 16.5293 4.88671 16.3711C4.60546 15.9902 4.52928 15.9668 3.46874 15.9375C2.33788 15.9023 2.15624 15.8379 1.93944 15.3867C1.79296 15.0938 1.78124 14.8418 1.90428 14.543C2.02733 14.25 14.2793 1.98633 14.6191 1.81641C14.918 1.66992 15.1933 1.66992 15.4922 1.81641Z" />
                        </svg>
                        <h6>{{ translate('Dashboard') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('hotels.list') ||
                        request()->is('dashboard/hotels/*') ||
                        Illuminate\Support\Facades\Route::is('hotel.attribute.list') ||
                        request()->is('dashboard/hotel/attribute/*') ||
                        Illuminate\Support\Facades\Route::is('hotel.attribute.term.list')) class="active" @endif>
                    <a href="{{ route('hotels.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor" class="bi bi-bank" viewBox="0 0 16 16">
                            <path d="m8 0 6.61 3h.89a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5H15v7a.5.5 0 0 1 .485.38l.5 2a.498.498 0 0 1-.485.62H.5a.498.498 0 0 1-.485-.62l.5-2A.5.5 0 0 1 1 13V6H.5a.5.5 0 0 1-.5-.5v-2A.5.5 0 0 1 .5 3h.89zM3.777 3h8.447L8 1zM2 6v7h1V6zm2 0v7h2.5V6zm3.5 0v7h1V6zm2 0v7H12V6zM13 6v7h1V6zm2-1V4H1v1zm-.39 9H1.39l-.25 1h13.72z"/>
                          </svg>
                        <h6>{{ translate('Hotel') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('destination.list')) class="active" @endif>
                    <a href="{{ route('destination.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-umbrella" viewBox="0 0 576 512">
                            <path
                                d="M408 120c0 54.6-73.1 151.9-105.2 192c-7.7 9.6-22 9.6-29.6 0C241.1 271.9 168 174.6 168 120C168 53.7 221.7 0 288 0s120 53.7 120 120zm8 80.4c3.5-6.9 6.7-13.8 9.6-20.6c.5-1.2 1-2.5 1.5-3.7l116-46.4C558.9 123.4 576 135 576 152l0 270.8c0 9.8-6 18.6-15.1 22.3L416 503l0-302.6zM137.6 138.3c2.4 14.1 7.2 28.3 12.8 41.5c2.9 6.8 6.1 13.7 9.6 20.6l0 251.4L32.9 502.7C17.1 509 0 497.4 0 480.4L0 209.6c0-9.8 6-18.6 15.1-22.3l122.6-49zM327.8 332c13.9-17.4 35.7-45.7 56.2-77l0 249.3L192 449.4 192 255c20.5 31.3 42.3 59.6 56.2 77c20.5 25.6 59.1 25.6 79.6 0zM288 152a40 40 0 1 0 0-80 40 40 0 1 0 0 80z" />
                        </svg>
                        <h6>{{ translate('Destination') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('tours.list') ||
                        request()->is('dashboard/tours/*') ||
                        Illuminate\Support\Facades\Route::is('tour.attribute.list') ||
                        request()->is('dashboard/tour/attribute/*') ||
                        Illuminate\Support\Facades\Route::is('tour.attribute.term.list')) class="active" @endif>
                    <a href="{{ route('tours.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-umbrella" viewBox="0 0 16 16">
                            <path
                                d="M8 0a.5.5 0 0 1 .5.5v.514C12.625 1.238 16 4.22 16 8c0 0 0 .5-.5.5-.149 0-.352-.145-.352-.145l-.004-.004-.025-.023a3.5 3.5 0 0 0-.555-.394A3.17 3.17 0 0 0 13 7.5c-.638 0-1.178.213-1.564.434a3.5 3.5 0 0 0-.555.394l-.025.023-.003.003s-.204.146-.353.146-.352-.145-.352-.145l-.004-.004-.025-.023a3.5 3.5 0 0 0-.555-.394 3.3 3.3 0 0 0-1.064-.39V13.5H8h.5v.039l-.005.083a3 3 0 0 1-.298 1.102 2.26 2.26 0 0 1-.763.88C7.06 15.851 6.587 16 6 16s-1.061-.148-1.434-.396a2.26 2.26 0 0 1-.763-.88 3 3 0 0 1-.302-1.185v-.025l-.001-.009v-.003s0-.002.5-.002h-.5V13a.5.5 0 0 1 1 0v.506l.003.044a2 2 0 0 0 .195.726c.095.191.23.367.423.495.19.127.466.229.879.229s.689-.102.879-.229c.193-.128.328-.304.424-.495a2 2 0 0 0 .197-.77V7.544a3.3 3.3 0 0 0-1.064.39 3.5 3.5 0 0 0-.58.417l-.004.004S5.65 8.5 5.5 8.5s-.352-.145-.352-.145l-.004-.004a3.5 3.5 0 0 0-.58-.417A3.17 3.17 0 0 0 3 7.5c-.638 0-1.177.213-1.564.434a3.5 3.5 0 0 0-.58.417l-.004.004S.65 8.5.5 8.5C0 8.5 0 8 0 8c0-3.78 3.375-6.762 7.5-6.986V.5A.5.5 0 0 1 8 0M6.577 2.123c-2.833.5-4.99 2.458-5.474 4.854A4.1 4.1 0 0 1 3 6.5c.806 0 1.48.25 1.962.511a9.7 9.7 0 0 1 .344-2.358c.242-.868.64-1.765 1.271-2.53m-.615 4.93A4.16 4.16 0 0 1 8 6.5a4.16 4.16 0 0 1 2.038.553 8.7 8.7 0 0 0-.307-2.13C9.434 3.858 8.898 2.83 8 2.117c-.898.712-1.434 1.74-1.731 2.804a8.7 8.7 0 0 0-.307 2.131zm3.46-4.93c.631.765 1.03 1.662 1.272 2.53.233.833.328 1.66.344 2.358A4.14 4.14 0 0 1 13 6.5c.77 0 1.42.23 1.897.477-.484-2.396-2.641-4.355-5.474-4.854z" />
                        </svg>
                        <h6>{{ translate('Tour') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('activities.list') ||
                        request()->is('dashboard/spaces/*') ||
                        Illuminate\Support\Facades\Route::is('activities.attribute.list') ||
                        request()->is('dashboard/activities/attribute/*') ||
                        Illuminate\Support\Facades\Route::is('activities.attribute.term.list')) class="active" @endif>
                    <a href="{{ route('activities.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-activity" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M6 2a.5.5 0 0 1 .47.33L10 12.036l1.53-4.208A.5.5 0 0 1 12 7.5h3.5a.5.5 0 0 1 0 1h-3.15l-1.88 5.17a.5.5 0 0 1-.94 0L6 3.964 4.47 8.171A.5.5 0 0 1 4 8.5H.5a.5.5 0 0 1 0-1h3.15l1.88-5.17A.5.5 0 0 1 6 2" />
                        </svg>
                        <h6>{{ translate('Activities') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('transports.list') ||
                        request()->is('dashboard/transports/*') ||
                        Illuminate\Support\Facades\Route::is('transports.attribute.list') ||
                        request()->is('dashboard/transports/attribute/*') ||
                        Illuminate\Support\Facades\Route::is('transports.attribute.term.list')) class="active" @endif>
                    <a href="{{ route('transports.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-bus-front" viewBox="0 0 16 16">
                            <path
                                d="M5 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0m8 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0m-6-1a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2zm1-6c-1.876 0-3.426.109-4.552.226A.5.5 0 0 0 3 4.723v3.554a.5.5 0 0 0 .448.497C4.574 8.891 6.124 9 8 9s3.426-.109 4.552-.226A.5.5 0 0 0 13 8.277V4.723a.5.5 0 0 0-.448-.497A44 44 0 0 0 8 4m0-1c-1.837 0-3.353.107-4.448.22a.5.5 0 1 1-.104-.994A44 44 0 0 1 8 2c1.876 0 3.426.109 4.552.226a.5.5 0 1 1-.104.994A43 43 0 0 0 8 3" />
                            <path
                                d="M15 8a1 1 0 0 0 1-1V5a1 1 0 0 0-1-1V2.64c0-1.188-.845-2.232-2.064-2.372A44 44 0 0 0 8 0C5.9 0 4.208.136 3.064.268 1.845.408 1 1.452 1 2.64V4a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1v3.5c0 .818.393 1.544 1 2v2a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5V14h6v1.5a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-2c.607-.456 1-1.182 1-2zM8 1c2.056 0 3.71.134 4.822.261.676.078 1.178.66 1.178 1.379v8.86a1.5 1.5 0 0 1-1.5 1.5h-9A1.5 1.5 0 0 1 2 11.5V2.64c0-.72.502-1.301 1.178-1.379A43 43 0 0 1 8 1" />
                        </svg>
                        <h6>{{ translate('Transports') }}</h6>
                    </a>
                </li>

                <li @if (Illuminate\Support\Facades\Route::is('visa.list')) class="active" @endif>
                    <a href="{{ route('visa.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor" class="bi bi-globe-americas" viewBox="0 0 16 16">
                            <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0M2.04 4.326c.325 1.329 2.532 2.54 3.717 3.19.48.263.793.434.743.484q-.121.12-.242.234c-.416.396-.787.749-.758 1.266.035.634.618.824 1.214 1.017.577.188 1.168.38 1.286.983.082.417-.075.988-.22 1.52-.215.782-.406 1.48.22 1.48 1.5-.5 3.798-3.186 4-5 .138-1.243-2-2-3.5-2.5-.478-.16-.755.081-.99.284-.172.15-.322.279-.51.216-.445-.148-2.5-2-1.5-2.5.78-.39.952-.171 1.227.182.078.099.163.208.273.318.609.304.662-.132.723-.633.039-.322.081-.671.277-.867.434-.434 1.265-.791 2.028-1.12.712-.306 1.365-.587 1.579-.88A7 7 0 1 1 2.04 4.327Z"/>
                          </svg>
                        <h6>{{ translate('Visa') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('backend.order.info')) class="active" @endif>
                    <a href="{{ route('backend.order.info') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-x-diamond" viewBox="0 0 16 16">
                            <path
                                d="M7.987 16a1.53 1.53 0 0 1-1.07-.448L.45 9.082a1.53 1.53 0 0 1 0-2.165L6.917.45a1.53 1.53 0 0 1 2.166 0l6.469 6.468A1.53 1.53 0 0 1 16 8.013a1.53 1.53 0 0 1-.448 1.07l-6.47 6.469A1.53 1.53 0 0 1 7.988 16zM7.639 1.17 4.766 4.044 8 7.278l3.234-3.234L8.361 1.17a.51.51 0 0 0-.722 0M8.722 8l3.234 3.234 2.873-2.873c.2-.2.2-.523 0-.722l-2.873-2.873zM8 8.722l-3.234 3.234 2.873 2.873c.2.2.523.2.722 0l2.873-2.873zM7.278 8 4.044 4.766 1.17 7.639a.51.51 0 0 0 0 .722l2.874 2.873z" />
                        </svg>
                        <h6>{{ translate('Booking') }}</h6>
                    </a>
                </li>
                <li @if (Illuminate\Support\Facades\Route::is('backend.transaction')) class="active" @endif>
                    <a href="{{ route('backend.transaction') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-arrow-left-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd"
                                d="M1 11.5a.5.5 0 0 0 .5.5h11.793l-3.147 3.146a.5.5 0 0 0 .708.708l4-4a.5.5 0 0 0 0-.708l-4-4a.5.5 0 0 0-.708.708L13.293 11H1.5a.5.5 0 0 0-.5.5zm14-7a.5.5 0 0 1-.5.5H2.707l3.147 3.146a.5.5 0 1 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 1 1 .708.708L2.707 4H14.5a.5.5 0 0 1 .5.5z" />
                        </svg>
                        <h6>{{ translate('Transaction') }}</h6>
                    </a>
                </li>
                @admin

                   <li @if (Illuminate\Support\Facades\Route::is('admin.list') || request()->is('dashboard/admin/*')) class="active" @endif>
                        <a href="{{ route('admin.list') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30"xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.209 0.0820312C10.6172 0.199219 10.2422 0.328125 9.63868 0.632812C8.94141 0.996094 8.33789 1.45898 7.69336 2.13281C7.08399 2.76562 6.80274 3.25781 6.64453 3.9668C6.46875 4.75195 6.52735 5.05078 7.3418 7.82227C8.05664 10.2598 8.06836 10.2949 8.16797 11.3086C8.27344 12.3809 8.36719 12.9023 8.52539 13.3945C8.79493 14.1914 9.41016 15.1641 10.0137 15.7207L10.3125 16.002L10.3008 16.9805L10.2832 17.959L7.64649 18.8848C6.19922 19.3887 4.82227 19.8984 4.58789 20.0039C2.88281 20.8125 1.64063 22.3359 1.125 24.2461C0.978516 24.8086 0.972656 24.8438 0.949219 27.2578L0.925781 29.7012L1.07813 29.8535L1.22461 30H8.4375H15.6504L15.791 29.8535C15.8848 29.7656 15.9375 29.6426 15.9375 29.5312C15.9375 29.4199 15.8848 29.2969 15.791 29.209C15.6621 29.0742 15.6035 29.0625 15.0879 29.0625H14.5313V26.5488V24.0293L15.3516 22.623C15.8379 21.791 16.1719 21.1582 16.1719 21.0645C16.1719 20.748 15.7617 20.5254 15.4688 20.6836C15.4102 20.7129 15.082 21.2285 14.7363 21.8262C14.3848 22.4238 14.0801 22.9043 14.0566 22.8984C14.0274 22.8867 13.3828 21.8203 12.627 20.5195L11.25 18.1582V17.3906C11.25 16.9746 11.2676 16.6406 11.2969 16.6523C12.9609 17.4902 14.8301 17.5605 16.4004 16.8398C16.6406 16.7285 16.8399 16.6406 16.8516 16.6406C16.8633 16.6406 16.875 16.7344 16.875 16.8457C16.875 17.2852 17.3672 17.502 17.666 17.1973C17.8008 17.0684 17.8125 17.0098 17.8125 16.5059V15.9492L18.0117 15.7969C18.334 15.5566 18.9609 14.7246 19.2481 14.1738C19.6289 13.4355 19.7871 12.8145 19.9453 11.3965L20.0801 10.166L20.8067 7.73438L21.5274 5.30273L21.5332 4.6582C21.5332 3.60938 21.2578 2.98242 20.4024 2.0918C19.7578 1.42969 19.1133 0.949219 18.3691 0.580078C17.4961 0.152344 17.0215 0.0410156 15.9961 0.0351562C15.1816 0.0292969 15.1231 0.0410156 14.6074 0.222656L14.0625 0.416016L13.5234 0.228516C13.0606 0.0703125 12.8789 0.0410156 12.3047 0.0234375C11.8945 0.0117188 11.4668 0.0351562 11.209 0.0820312ZM12.832 0.996094C12.9785 1.03125 13.2305 1.11328 13.3887 1.18359C13.7813 1.34766 14.3496 1.34766 14.7246 1.18359C15.627 0.785156 16.8926 0.884766 18.0176 1.44727C18.9785 1.92773 20.0098 2.87695 20.3789 3.62695C20.584 4.04297 20.5957 4.0957 20.5957 4.62305C20.5899 5.16211 20.5664 5.26758 19.9863 7.20703C19.6524 8.32031 19.3652 9.26367 19.3418 9.30469C19.2832 9.41016 18.8672 8.94141 18.6914 8.57227C18.5156 8.20312 18.3984 7.61719 18.3984 7.11328C18.3984 6.54492 18.3164 6.47461 17.6074 6.42188C16.2422 6.32227 15.2344 5.82422 14.4727 4.86914C14.2969 4.65234 14.2324 4.61133 14.0625 4.61133C13.8926 4.61133 13.8281 4.65234 13.6523 4.86914C12.8789 5.83594 11.8887 6.32812 10.5117 6.42188C10.0371 6.45703 9.94922 6.48047 9.83204 6.60352C9.72657 6.7207 9.70313 6.80273 9.71485 7.06641C9.73829 7.58203 9.6211 8.13867 9.4043 8.58398C9.1875 9.0293 8.8125 9.45703 8.76563 9.31055C8.74805 9.26367 8.4668 8.32031 8.13868 7.20703C7.46485 4.91602 7.41797 4.63477 7.60547 4.00195C7.76953 3.47461 7.99805 3.12305 8.52539 2.60742C9.64454 1.5 10.8106 0.960938 12.1289 0.943359C12.3691 0.943359 12.6856 0.966797 12.832 0.996094ZM14.4141 6.16992C15.0293 6.72656 16.002 7.1543 16.957 7.28906L17.4316 7.35938L17.5254 7.88086C17.7188 8.92383 18.0938 9.57422 18.8555 10.2012L19.1074 10.4062L19.0137 11.3086C18.8906 12.4746 18.7441 13.0898 18.457 13.6699C17.7422 15.0938 16.5 16.0195 14.9121 16.3184C13.0898 16.6641 11.1563 15.873 10.0488 14.3262C9.49805 13.5527 9.29297 12.9375 9.14063 11.584C9.07618 11.0332 9.02344 10.5352 9.02344 10.4824C9.02344 10.4355 9.15235 10.2832 9.31641 10.1543C10.0723 9.53906 10.4883 8.73047 10.6348 7.61133L10.6699 7.3418L11.0449 7.30664C11.959 7.21875 13.0781 6.74414 13.6875 6.18164C13.875 6.00586 14.0449 5.86523 14.0508 5.85938C14.0625 5.85938 14.2266 6 14.4141 6.16992ZM11.9473 21.2051C12.6973 22.4941 13.3066 23.5723 13.3008 23.5957C13.2832 23.6543 11.1973 24.252 11.168 24.2051C11.1563 24.1875 11.0625 23.6426 10.957 22.998L10.7637 21.8262L9.65039 20.7012C8.90039 19.9453 8.56055 19.5645 8.61914 19.541C9.41016 19.248 10.4707 18.8789 10.5176 18.873C10.5527 18.8672 11.1973 19.9219 11.9473 21.2051ZM8.70704 21.1055L9.88477 22.2949L10.1074 23.6426C10.2246 24.3809 10.3594 25.0488 10.4063 25.1191C10.5586 25.3594 10.7461 25.3418 12.1699 24.9316C12.9082 24.7207 13.5352 24.5508 13.5527 24.5508C13.5762 24.5508 13.5938 25.5645 13.5938 26.8066V29.0625H12.8906H12.1875V28.2715C12.1875 27.5039 12.1816 27.4688 12.041 27.334L11.9004 27.1875H9.375H6.84961L6.70899 27.334C6.56836 27.4688 6.5625 27.5039 6.5625 28.2715V29.0625H5.86524H5.16797L5.14453 27.1758C5.1211 25.4648 5.10352 25.2363 4.99219 24.8145C4.72852 23.8535 4.23633 22.875 3.65625 22.166L3.45117 21.9199L3.87891 21.5684C4.4707 21.082 5.06836 20.7832 6.35742 20.3262C6.9668 20.1094 7.48243 19.9277 7.5 19.9277C7.51758 19.9219 8.0625 20.4551 8.70704 21.1055ZM3.25195 23.2031C3.59766 23.7246 3.89649 24.375 4.05469 24.9492C4.16602 25.3535 4.1836 25.5938 4.20703 27.2344L4.23047 29.0625H3.05274H1.875V27.2227C1.875 25.2246 1.92188 24.7441 2.20313 23.9531C2.41992 23.3438 2.8125 22.6582 2.90625 22.7344C2.94141 22.7695 3.09961 22.9805 3.25195 23.2031ZM11.25 28.5938V29.0625H9.375H7.5V28.5938V28.125H9.375H11.25V28.5938Z" />
                                <path
                                    d="M11.3145 2.02154C10.5821 2.1856 9.76178 2.66021 9.19342 3.25786C8.54889 3.93755 8.39068 4.377 8.70709 4.6231C8.98834 4.84575 9.21099 4.76958 9.48638 4.37115C10.002 3.627 10.8926 3.04107 11.6895 2.92388C12.2754 2.83599 12.3692 2.80083 12.4981 2.62505C12.6329 2.44341 12.6036 2.23247 12.4102 2.05083C12.2579 1.91021 11.8829 1.89849 11.3145 2.02154Z" />
                                <path
                                    d="M11.6895 3.59766C11.1211 3.7793 10.5996 4.14844 10.2832 4.59961C10.0547 4.91602 10.0313 5.0625 10.1719 5.26172C10.3945 5.57813 10.7637 5.53125 11.0274 5.16211C11.3027 4.75781 11.7598 4.50586 12.375 4.42383C12.8379 4.36524 13.0254 3.9961 12.7676 3.66797C12.6621 3.53321 12.6094 3.51563 12.2871 3.52149C12.0879 3.52149 11.8184 3.55664 11.6895 3.59766Z" />
                                <path
                                    d="M12.1582 8.95898C11.8945 9.07617 11.748 9.31055 11.748 9.61523C11.748 9.86133 11.7773 9.92578 11.9414 10.0898C12.1055 10.2539 12.1699 10.2832 12.4219 10.2832C12.6738 10.2832 12.7383 10.2539 12.9023 10.0898C13.0664 9.92578 13.0957 9.86133 13.0957 9.60937C13.0957 9.36328 13.0664 9.29297 12.9141 9.13476C12.7207 8.9414 12.375 8.86523 12.1582 8.95898Z" />
                                <path
                                    d="M15.4395 8.95898C15.1758 9.07617 15.0293 9.31055 15.0293 9.61523C15.0293 9.86133 15.0586 9.92578 15.2227 10.0898C15.3867 10.2539 15.4512 10.2832 15.7031 10.2832C15.9551 10.2832 16.0195 10.2539 16.1836 10.0898C16.3477 9.92578 16.377 9.86133 16.377 9.60937C16.377 9.36328 16.3477 9.29297 16.1953 9.13476C16.002 8.9414 15.6562 8.86523 15.4395 8.95898Z" />
                                <path
                                    d="M13.7402 10.459C13.5996 10.5938 13.5938 10.623 13.5938 11.4844C13.5938 12.3457 13.5996 12.375 13.7402 12.5098C13.8281 12.6035 13.9512 12.6562 14.0625 12.6562C14.1738 12.6562 14.2969 12.6035 14.3848 12.5098C14.5254 12.375 14.5312 12.3457 14.5312 11.4844C14.5312 10.623 14.5254 10.5938 14.3848 10.459C14.2969 10.3652 14.1738 10.3125 14.0625 10.3125C13.9512 10.3125 13.8281 10.3652 13.7402 10.459Z" />
                                <path
                                    d="M12.3339 13.2715C11.6718 13.9278 13.371 14.7715 14.7246 14.4493C15.6796 14.2266 16.1835 13.7051 15.8203 13.3125C15.6503 13.1309 15.4218 13.0899 15.2402 13.2012C14.4843 13.6875 13.6054 13.711 13.0136 13.2715C12.7558 13.084 12.5156 13.084 12.3339 13.2715Z" />
                                <path
                                    d="M18.668 17.4082C18.3047 17.4961 17.7715 17.8066 17.5137 18.0879C17.3848 18.2285 17.1914 18.5215 17.0918 18.7383C16.916 19.1133 16.9043 19.166 16.8867 20.0098C16.8633 21.0176 16.9453 21.5332 17.2207 22.125C17.4141 22.541 17.7715 22.957 18.0762 23.1211L18.2812 23.2324V25.207V27.1875H17.7246C17.209 27.1875 17.1504 27.1992 17.0215 27.334C16.875 27.4746 16.875 27.4863 16.875 28.5938C16.875 29.7012 16.875 29.7129 17.0215 29.8535L17.1621 30H22.9687H28.7754L28.916 29.8535C29.0625 29.7129 29.0625 29.7012 29.0625 28.5938C29.0625 27.4863 29.0625 27.4746 28.916 27.334C28.7871 27.1992 28.7285 27.1875 28.2129 27.1875H27.6562V25.207V23.2324L27.8613 23.1211C28.166 22.957 28.5234 22.541 28.7168 22.125C28.9922 21.5332 29.0742 21.0176 29.0508 20.0039L29.0332 19.125L28.8164 18.6797C28.5527 18.1465 28.2129 17.8184 27.668 17.5605L27.2754 17.373L23.0859 17.3613C20.6777 17.3555 18.8027 17.3789 18.668 17.4082ZM20.625 19.2188V20.1562H19.2187H17.8125V19.8281C17.8125 19.6465 17.8418 19.3887 17.8828 19.2539C17.9707 18.9551 18.4629 18.4512 18.75 18.3574C18.8613 18.3223 19.3301 18.293 19.793 18.2871L20.625 18.2812V19.2188ZM24.375 19.2188V20.1562H22.9687H21.5625V19.2188V18.2812H22.9687H24.375V19.2188ZM27.1523 18.3516C27.4746 18.4453 27.9609 18.9316 28.0547 19.2539C28.0957 19.3887 28.125 19.6465 28.125 19.8281V20.1562H26.7187H25.3125V19.2188V18.2812H26.1094C26.5664 18.2812 27.0117 18.3105 27.1523 18.3516ZM20.5371 21.2754C20.2734 22.459 19.0312 22.9043 18.2988 22.0723C18.1406 21.8906 17.8711 21.3105 17.8711 21.1523C17.8711 21.1172 18.3984 21.0938 19.2246 21.0938H20.5781L20.5371 21.2754ZM24.2871 21.2754C24.0234 22.459 22.7812 22.9043 22.0488 22.0723C21.8906 21.8906 21.6211 21.3105 21.6211 21.1523C21.6211 21.1172 22.1484 21.0938 22.9746 21.0938H24.3281L24.2871 21.2754ZM28.0664 21.1523C28.0664 21.3105 27.7969 21.8906 27.6387 22.0723C27.1465 22.6348 26.291 22.6348 25.7988 22.0723C25.6406 21.8906 25.3711 21.3105 25.3711 21.1523C25.3711 21.1172 25.8926 21.0938 26.7187 21.0938C27.5449 21.0938 28.0664 21.1172 28.0664 21.1523ZM21.3867 22.752C21.5801 22.9629 21.7969 23.127 22.043 23.2383C22.3711 23.3906 22.4766 23.4082 22.9687 23.4082C23.4609 23.4082 23.5664 23.3906 23.8945 23.2383C24.1406 23.1211 24.3574 22.9629 24.5508 22.752L24.8437 22.4297L25.1367 22.752C25.4648 23.1094 25.9512 23.3613 26.4258 23.4199L26.7187 23.4551V25.3184V27.1875H25.7812H24.8437V25.9277C24.8437 24.6621 24.8437 24.6621 24.6973 24.5215L24.5566 24.375H22.9687H21.3809L21.2402 24.5215C21.0937 24.6621 21.0937 24.6621 21.0937 25.9277V27.1875H20.1562H19.2187V25.3184V23.4551L19.5176 23.4199C19.9863 23.3613 20.4961 23.0918 20.8008 22.7461C20.9473 22.582 21.0703 22.4473 21.082 22.4414C21.0937 22.4414 21.2285 22.582 21.3867 22.752ZM23.9062 26.25V27.1875H22.9687H22.0312V26.25V25.3125H22.9687H23.9062V26.25ZM28.125 28.5938V29.0625H22.9687H17.8125V28.5938V28.125H22.9687H28.125V28.5938Z" />
                            </svg>
                            <h6>{{ translate('Staff') }}</h6>
                        </a>
                    </li>


                    <li @if (Illuminate\Support\Facades\Route::is('advertisement') || request()->is('dashboard/bannerTwo/*')) class="active" @endif>
                        <a href="{{ route('advertisement') }}">
                           <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 11l22-6v16L3 15v-4z" />
                            <path d="M11.6 16.8a4 4 0 1 1-7.6-2.8" />
                            </svg>

                            <h6>{{ translate('Advertisement') }}</h6>
                        </a>
                    </li>


                    <li @if (Illuminate\Support\Facades\Route::is('merchant.list') || request()->is('dashboard/merchant/*')) class="active" @endif>
                        <a href="{{ route('merchant.list') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30"xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.209 0.0820312C10.6172 0.199219 10.2422 0.328125 9.63868 0.632812C8.94141 0.996094 8.33789 1.45898 7.69336 2.13281C7.08399 2.76562 6.80274 3.25781 6.64453 3.9668C6.46875 4.75195 6.52735 5.05078 7.3418 7.82227C8.05664 10.2598 8.06836 10.2949 8.16797 11.3086C8.27344 12.3809 8.36719 12.9023 8.52539 13.3945C8.79493 14.1914 9.41016 15.1641 10.0137 15.7207L10.3125 16.002L10.3008 16.9805L10.2832 17.959L7.64649 18.8848C6.19922 19.3887 4.82227 19.8984 4.58789 20.0039C2.88281 20.8125 1.64063 22.3359 1.125 24.2461C0.978516 24.8086 0.972656 24.8438 0.949219 27.2578L0.925781 29.7012L1.07813 29.8535L1.22461 30H8.4375H15.6504L15.791 29.8535C15.8848 29.7656 15.9375 29.6426 15.9375 29.5312C15.9375 29.4199 15.8848 29.2969 15.791 29.209C15.6621 29.0742 15.6035 29.0625 15.0879 29.0625H14.5313V26.5488V24.0293L15.3516 22.623C15.8379 21.791 16.1719 21.1582 16.1719 21.0645C16.1719 20.748 15.7617 20.5254 15.4688 20.6836C15.4102 20.7129 15.082 21.2285 14.7363 21.8262C14.3848 22.4238 14.0801 22.9043 14.0566 22.8984C14.0274 22.8867 13.3828 21.8203 12.627 20.5195L11.25 18.1582V17.3906C11.25 16.9746 11.2676 16.6406 11.2969 16.6523C12.9609 17.4902 14.8301 17.5605 16.4004 16.8398C16.6406 16.7285 16.8399 16.6406 16.8516 16.6406C16.8633 16.6406 16.875 16.7344 16.875 16.8457C16.875 17.2852 17.3672 17.502 17.666 17.1973C17.8008 17.0684 17.8125 17.0098 17.8125 16.5059V15.9492L18.0117 15.7969C18.334 15.5566 18.9609 14.7246 19.2481 14.1738C19.6289 13.4355 19.7871 12.8145 19.9453 11.3965L20.0801 10.166L20.8067 7.73438L21.5274 5.30273L21.5332 4.6582C21.5332 3.60938 21.2578 2.98242 20.4024 2.0918C19.7578 1.42969 19.1133 0.949219 18.3691 0.580078C17.4961 0.152344 17.0215 0.0410156 15.9961 0.0351562C15.1816 0.0292969 15.1231 0.0410156 14.6074 0.222656L14.0625 0.416016L13.5234 0.228516C13.0606 0.0703125 12.8789 0.0410156 12.3047 0.0234375C11.8945 0.0117188 11.4668 0.0351562 11.209 0.0820312ZM12.832 0.996094C12.9785 1.03125 13.2305 1.11328 13.3887 1.18359C13.7813 1.34766 14.3496 1.34766 14.7246 1.18359C15.627 0.785156 16.8926 0.884766 18.0176 1.44727C18.9785 1.92773 20.0098 2.87695 20.3789 3.62695C20.584 4.04297 20.5957 4.0957 20.5957 4.62305C20.5899 5.16211 20.5664 5.26758 19.9863 7.20703C19.6524 8.32031 19.3652 9.26367 19.3418 9.30469C19.2832 9.41016 18.8672 8.94141 18.6914 8.57227C18.5156 8.20312 18.3984 7.61719 18.3984 7.11328C18.3984 6.54492 18.3164 6.47461 17.6074 6.42188C16.2422 6.32227 15.2344 5.82422 14.4727 4.86914C14.2969 4.65234 14.2324 4.61133 14.0625 4.61133C13.8926 4.61133 13.8281 4.65234 13.6523 4.86914C12.8789 5.83594 11.8887 6.32812 10.5117 6.42188C10.0371 6.45703 9.94922 6.48047 9.83204 6.60352C9.72657 6.7207 9.70313 6.80273 9.71485 7.06641C9.73829 7.58203 9.6211 8.13867 9.4043 8.58398C9.1875 9.0293 8.8125 9.45703 8.76563 9.31055C8.74805 9.26367 8.4668 8.32031 8.13868 7.20703C7.46485 4.91602 7.41797 4.63477 7.60547 4.00195C7.76953 3.47461 7.99805 3.12305 8.52539 2.60742C9.64454 1.5 10.8106 0.960938 12.1289 0.943359C12.3691 0.943359 12.6856 0.966797 12.832 0.996094ZM14.4141 6.16992C15.0293 6.72656 16.002 7.1543 16.957 7.28906L17.4316 7.35938L17.5254 7.88086C17.7188 8.92383 18.0938 9.57422 18.8555 10.2012L19.1074 10.4062L19.0137 11.3086C18.8906 12.4746 18.7441 13.0898 18.457 13.6699C17.7422 15.0938 16.5 16.0195 14.9121 16.3184C13.0898 16.6641 11.1563 15.873 10.0488 14.3262C9.49805 13.5527 9.29297 12.9375 9.14063 11.584C9.07618 11.0332 9.02344 10.5352 9.02344 10.4824C9.02344 10.4355 9.15235 10.2832 9.31641 10.1543C10.0723 9.53906 10.4883 8.73047 10.6348 7.61133L10.6699 7.3418L11.0449 7.30664C11.959 7.21875 13.0781 6.74414 13.6875 6.18164C13.875 6.00586 14.0449 5.86523 14.0508 5.85938C14.0625 5.85938 14.2266 6 14.4141 6.16992ZM11.9473 21.2051C12.6973 22.4941 13.3066 23.5723 13.3008 23.5957C13.2832 23.6543 11.1973 24.252 11.168 24.2051C11.1563 24.1875 11.0625 23.6426 10.957 22.998L10.7637 21.8262L9.65039 20.7012C8.90039 19.9453 8.56055 19.5645 8.61914 19.541C9.41016 19.248 10.4707 18.8789 10.5176 18.873C10.5527 18.8672 11.1973 19.9219 11.9473 21.2051ZM8.70704 21.1055L9.88477 22.2949L10.1074 23.6426C10.2246 24.3809 10.3594 25.0488 10.4063 25.1191C10.5586 25.3594 10.7461 25.3418 12.1699 24.9316C12.9082 24.7207 13.5352 24.5508 13.5527 24.5508C13.5762 24.5508 13.5938 25.5645 13.5938 26.8066V29.0625H12.8906H12.1875V28.2715C12.1875 27.5039 12.1816 27.4688 12.041 27.334L11.9004 27.1875H9.375H6.84961L6.70899 27.334C6.56836 27.4688 6.5625 27.5039 6.5625 28.2715V29.0625H5.86524H5.16797L5.14453 27.1758C5.1211 25.4648 5.10352 25.2363 4.99219 24.8145C4.72852 23.8535 4.23633 22.875 3.65625 22.166L3.45117 21.9199L3.87891 21.5684C4.4707 21.082 5.06836 20.7832 6.35742 20.3262C6.9668 20.1094 7.48243 19.9277 7.5 19.9277C7.51758 19.9219 8.0625 20.4551 8.70704 21.1055ZM3.25195 23.2031C3.59766 23.7246 3.89649 24.375 4.05469 24.9492C4.16602 25.3535 4.1836 25.5938 4.20703 27.2344L4.23047 29.0625H3.05274H1.875V27.2227C1.875 25.2246 1.92188 24.7441 2.20313 23.9531C2.41992 23.3438 2.8125 22.6582 2.90625 22.7344C2.94141 22.7695 3.09961 22.9805 3.25195 23.2031ZM11.25 28.5938V29.0625H9.375H7.5V28.5938V28.125H9.375H11.25V28.5938Z" />
                                <path
                                    d="M11.3145 2.02154C10.5821 2.1856 9.76178 2.66021 9.19342 3.25786C8.54889 3.93755 8.39068 4.377 8.70709 4.6231C8.98834 4.84575 9.21099 4.76958 9.48638 4.37115C10.002 3.627 10.8926 3.04107 11.6895 2.92388C12.2754 2.83599 12.3692 2.80083 12.4981 2.62505C12.6329 2.44341 12.6036 2.23247 12.4102 2.05083C12.2579 1.91021 11.8829 1.89849 11.3145 2.02154Z" />
                                <path
                                    d="M11.6895 3.59766C11.1211 3.7793 10.5996 4.14844 10.2832 4.59961C10.0547 4.91602 10.0313 5.0625 10.1719 5.26172C10.3945 5.57813 10.7637 5.53125 11.0274 5.16211C11.3027 4.75781 11.7598 4.50586 12.375 4.42383C12.8379 4.36524 13.0254 3.9961 12.7676 3.66797C12.6621 3.53321 12.6094 3.51563 12.2871 3.52149C12.0879 3.52149 11.8184 3.55664 11.6895 3.59766Z" />
                                <path
                                    d="M12.1582 8.95898C11.8945 9.07617 11.748 9.31055 11.748 9.61523C11.748 9.86133 11.7773 9.92578 11.9414 10.0898C12.1055 10.2539 12.1699 10.2832 12.4219 10.2832C12.6738 10.2832 12.7383 10.2539 12.9023 10.0898C13.0664 9.92578 13.0957 9.86133 13.0957 9.60937C13.0957 9.36328 13.0664 9.29297 12.9141 9.13476C12.7207 8.9414 12.375 8.86523 12.1582 8.95898Z" />
                                <path
                                    d="M15.4395 8.95898C15.1758 9.07617 15.0293 9.31055 15.0293 9.61523C15.0293 9.86133 15.0586 9.92578 15.2227 10.0898C15.3867 10.2539 15.4512 10.2832 15.7031 10.2832C15.9551 10.2832 16.0195 10.2539 16.1836 10.0898C16.3477 9.92578 16.377 9.86133 16.377 9.60937C16.377 9.36328 16.3477 9.29297 16.1953 9.13476C16.002 8.9414 15.6562 8.86523 15.4395 8.95898Z" />
                                <path
                                    d="M13.7402 10.459C13.5996 10.5938 13.5938 10.623 13.5938 11.4844C13.5938 12.3457 13.5996 12.375 13.7402 12.5098C13.8281 12.6035 13.9512 12.6562 14.0625 12.6562C14.1738 12.6562 14.2969 12.6035 14.3848 12.5098C14.5254 12.375 14.5312 12.3457 14.5312 11.4844C14.5312 10.623 14.5254 10.5938 14.3848 10.459C14.2969 10.3652 14.1738 10.3125 14.0625 10.3125C13.9512 10.3125 13.8281 10.3652 13.7402 10.459Z" />
                                <path
                                    d="M12.3339 13.2715C11.6718 13.9278 13.371 14.7715 14.7246 14.4493C15.6796 14.2266 16.1835 13.7051 15.8203 13.3125C15.6503 13.1309 15.4218 13.0899 15.2402 13.2012C14.4843 13.6875 13.6054 13.711 13.0136 13.2715C12.7558 13.084 12.5156 13.084 12.3339 13.2715Z" />
                                <path
                                    d="M18.668 17.4082C18.3047 17.4961 17.7715 17.8066 17.5137 18.0879C17.3848 18.2285 17.1914 18.5215 17.0918 18.7383C16.916 19.1133 16.9043 19.166 16.8867 20.0098C16.8633 21.0176 16.9453 21.5332 17.2207 22.125C17.4141 22.541 17.7715 22.957 18.0762 23.1211L18.2812 23.2324V25.207V27.1875H17.7246C17.209 27.1875 17.1504 27.1992 17.0215 27.334C16.875 27.4746 16.875 27.4863 16.875 28.5938C16.875 29.7012 16.875 29.7129 17.0215 29.8535L17.1621 30H22.9687H28.7754L28.916 29.8535C29.0625 29.7129 29.0625 29.7012 29.0625 28.5938C29.0625 27.4863 29.0625 27.4746 28.916 27.334C28.7871 27.1992 28.7285 27.1875 28.2129 27.1875H27.6562V25.207V23.2324L27.8613 23.1211C28.166 22.957 28.5234 22.541 28.7168 22.125C28.9922 21.5332 29.0742 21.0176 29.0508 20.0039L29.0332 19.125L28.8164 18.6797C28.5527 18.1465 28.2129 17.8184 27.668 17.5605L27.2754 17.373L23.0859 17.3613C20.6777 17.3555 18.8027 17.3789 18.668 17.4082ZM20.625 19.2188V20.1562H19.2187H17.8125V19.8281C17.8125 19.6465 17.8418 19.3887 17.8828 19.2539C17.9707 18.9551 18.4629 18.4512 18.75 18.3574C18.8613 18.3223 19.3301 18.293 19.793 18.2871L20.625 18.2812V19.2188ZM24.375 19.2188V20.1562H22.9687H21.5625V19.2188V18.2812H22.9687H24.375V19.2188ZM27.1523 18.3516C27.4746 18.4453 27.9609 18.9316 28.0547 19.2539C28.0957 19.3887 28.125 19.6465 28.125 19.8281V20.1562H26.7187H25.3125V19.2188V18.2812H26.1094C26.5664 18.2812 27.0117 18.3105 27.1523 18.3516ZM20.5371 21.2754C20.2734 22.459 19.0312 22.9043 18.2988 22.0723C18.1406 21.8906 17.8711 21.3105 17.8711 21.1523C17.8711 21.1172 18.3984 21.0938 19.2246 21.0938H20.5781L20.5371 21.2754ZM24.2871 21.2754C24.0234 22.459 22.7812 22.9043 22.0488 22.0723C21.8906 21.8906 21.6211 21.3105 21.6211 21.1523C21.6211 21.1172 22.1484 21.0938 22.9746 21.0938H24.3281L24.2871 21.2754ZM28.0664 21.1523C28.0664 21.3105 27.7969 21.8906 27.6387 22.0723C27.1465 22.6348 26.291 22.6348 25.7988 22.0723C25.6406 21.8906 25.3711 21.3105 25.3711 21.1523C25.3711 21.1172 25.8926 21.0938 26.7187 21.0938C27.5449 21.0938 28.0664 21.1172 28.0664 21.1523ZM21.3867 22.752C21.5801 22.9629 21.7969 23.127 22.043 23.2383C22.3711 23.3906 22.4766 23.4082 22.9687 23.4082C23.4609 23.4082 23.5664 23.3906 23.8945 23.2383C24.1406 23.1211 24.3574 22.9629 24.5508 22.752L24.8437 22.4297L25.1367 22.752C25.4648 23.1094 25.9512 23.3613 26.4258 23.4199L26.7187 23.4551V25.3184V27.1875H25.7812H24.8437V25.9277C24.8437 24.6621 24.8437 24.6621 24.6973 24.5215L24.5566 24.375H22.9687H21.3809L21.2402 24.5215C21.0937 24.6621 21.0937 24.6621 21.0937 25.9277V27.1875H20.1562H19.2187V25.3184V23.4551L19.5176 23.4199C19.9863 23.3613 20.4961 23.0918 20.8008 22.7461C20.9473 22.582 21.0703 22.4473 21.082 22.4414C21.0937 22.4414 21.2285 22.582 21.3867 22.752ZM23.9062 26.25V27.1875H22.9687H22.0312V26.25V25.3125H22.9687H23.9062V26.25ZM28.125 28.5938V29.0625H22.9687H17.8125V28.5938V28.125H22.9687H28.125V28.5938Z" />
                            </svg>
                            <h6>{{ translate('Agents') }}</h6>
                        </a>
                    </li>
                    <li @if (Illuminate\Support\Facades\Route::is('customer.list') || request()->is('dashboard/customer/*')) class="active" @endif>
                        <a href="{{ route('customer.list') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.76367 0.527342C6.91406 0.662107 6.22852 0.966795 5.38477 1.58789C5.08594 1.81054 4.80469 1.99219 4.76953 1.99219C4.73438 1.99219 4.52344 2.08594 4.29492 2.19726C3.33984 2.67773 2.69531 3.70898 2.69531 4.76953C2.69531 5.36133 2.8125 5.74219 3.27539 6.67383L3.68555 7.48828L3.45703 7.52929C3.14062 7.58203 2.73047 7.85742 2.53711 8.15039C2.39062 8.37304 2.37305 8.44922 2.37305 8.91211C2.37305 9.38672 2.38477 9.43945 2.54883 9.67383C2.77734 10.002 3.17578 10.248 3.55078 10.2891L3.84375 10.3242L3.95508 10.7168C4.24219 11.7363 4.93359 12.6738 5.84766 13.2715L6.09375 13.4355V14.0625V14.6895L3.94336 15.5801C2.75391 16.0723 1.64648 16.5703 1.47656 16.6816C1.10742 16.9336 0.873047 17.2148 0.65625 17.6777L0.498047 18.0176L0.480469 21.5215L0.462891 25.0195L0.609375 25.166L0.755859 25.3125H2.49023H4.21875V27.2754V29.2441L4.36523 29.3848L4.50586 29.5312H15H25.4941L25.6348 29.3848L25.7812 29.2441V27.2754V25.3125H27.5098H29.2441L29.3848 25.166L29.5312 25.0254V21.668C29.5312 18.1582 29.5195 18.0117 29.2676 17.5195C29.0918 17.1797 28.8457 16.9043 28.5234 16.6816C28.3535 16.5703 27.2461 16.0723 26.0625 15.5801L23.9062 14.6836V14.0625V13.4355L24.1582 13.2715C25.0664 12.6738 25.7578 11.7363 26.0449 10.7168L26.1562 10.3242L26.4492 10.2891C27.1172 10.2129 27.6094 9.67383 27.6445 8.9707C27.6621 8.63672 27.6445 8.54297 27.498 8.26172C27.3047 7.89258 26.918 7.59375 26.543 7.52929C26.4141 7.50586 26.3086 7.48242 26.3086 7.4707C26.3086 7.45898 26.4316 7.16601 26.5781 6.81445C26.9121 6.04687 27 5.73047 27.0469 5.15625C27.123 4.14258 26.6836 3 25.9277 2.25586C25.4707 1.80469 25.1074 1.59375 23.8477 1.03711C22.8398 0.597654 22.4004 0.498045 21.5332 0.503904C20.373 0.521482 19.582 0.802732 18.5098 1.58789C18.2109 1.81054 17.9297 1.99219 17.8945 1.99219C17.6836 1.99219 16.9805 2.43164 16.6582 2.77148C16.043 3.41015 15.7793 4.11328 15.8379 4.98047C15.8555 5.25586 15.8906 5.53711 15.9141 5.61328C15.9492 5.73633 15.9375 5.74219 15.7852 5.70117C15.5449 5.64258 14.373 5.66015 14.0742 5.73047L13.8223 5.78906L13.8691 5.57226C13.9453 5.22656 13.9102 4.39453 13.7988 3.98437C13.5645 3.08203 12.8848 2.16211 12.123 1.71094C11.6074 1.40625 10.0312 0.726561 9.5918 0.621092C9.09375 0.498045 8.23242 0.457029 7.76367 0.527342ZM9.43359 1.55273C9.97852 1.69336 11.584 2.44922 11.9238 2.72461C12.7148 3.375 13.1133 4.37695 12.9551 5.33203C12.8379 6.04687 12.7676 6.1582 12.1523 6.60351C11.5488 7.03711 11.5371 7.04297 10.8926 7.34765C9.66211 7.92773 9 9.35156 9.3457 10.6816C9.39258 10.8633 9.61523 11.3848 9.84375 11.8359L10.2539 12.6562H9.95508C9.47461 12.6562 9.19922 12.7676 8.87695 13.0957C8.2793 13.6875 8.2793 14.4375 8.87695 15.0293C9.2168 15.375 9.47461 15.4687 10.043 15.4687C10.3418 15.4687 10.4297 15.4863 10.4297 15.5508C10.4297 15.75 10.8105 16.6641 11.0391 17.0273C11.3379 17.4844 11.9062 18.0762 12.3457 18.3809L12.6562 18.5918V19.1953V19.7988L12.3516 19.8867C12.1816 19.9336 10.6992 20.3437 9.05273 20.8008C7.41211 21.252 5.94727 21.668 5.80078 21.7148C4.86328 22.043 4.21875 22.9746 4.21875 24V24.375H2.8125H1.40039L1.41797 21.2871C1.43555 18.2168 1.43555 18.1934 1.56445 17.9473C1.63477 17.8184 1.79297 17.6309 1.9043 17.5312C2.0332 17.4258 3.02344 16.9805 4.48828 16.377C6.02344 15.7383 6.89062 15.3457 6.94922 15.2695C7.01367 15.1758 7.03125 14.918 7.03125 14.0918C7.03125 12.873 7.03711 12.8848 6.51562 12.5918C6.33398 12.4863 5.99414 12.2109 5.76562 11.9824C5.25586 11.4668 4.9043 10.8105 4.76953 10.1074C4.64648 9.47461 4.53516 9.375 3.97852 9.375C3.62109 9.375 3.55078 9.35742 3.42773 9.22851C3.23438 9.04101 3.23438 8.77148 3.42773 8.58398C3.55078 8.45508 3.62109 8.4375 3.98438 8.4375C4.34766 8.4375 4.41797 8.41992 4.54102 8.29101C4.66406 8.17383 4.6875 8.09179 4.6875 7.78125C4.6875 7.43554 4.6582 7.34765 4.17773 6.36328L3.66211 5.30859V4.78125C3.66211 4.2832 3.67383 4.21875 3.86133 3.8789C4.10156 3.43945 4.36523 3.19922 4.80469 3.00586C5.39062 2.74804 5.47266 2.70117 6.03516 2.28515C6.61523 1.85742 7.24219 1.57031 7.80469 1.46484C8.21484 1.39453 8.97656 1.43554 9.43359 1.55273ZM22.5586 1.55273C23.1035 1.69336 24.709 2.44922 25.0488 2.72461C25.6289 3.19922 25.957 3.77929 26.0859 4.55273C26.1797 5.10937 26.0742 5.625 25.6641 6.58008C25.3535 7.29492 25.3125 7.4414 25.3125 7.76953C25.3125 8.09765 25.3359 8.16797 25.459 8.29101C25.582 8.41992 25.6523 8.4375 26.0156 8.4375C26.3789 8.4375 26.4492 8.45508 26.5723 8.58398C26.7656 8.77148 26.7656 9.04101 26.5723 9.22851C26.4492 9.35742 26.3789 9.375 26.0215 9.375C25.4648 9.375 25.3535 9.47461 25.2305 10.1074C25.0957 10.8105 24.7441 11.4668 24.2344 11.9824C24.0059 12.2168 23.666 12.4922 23.4785 12.5977C22.9629 12.8848 22.9688 12.873 22.9688 14.0977C22.9688 15.0293 22.9805 15.1758 23.0742 15.2812C23.1328 15.3516 24.1699 15.8203 25.5293 16.3828C26.9824 16.9863 27.9609 17.4258 28.0957 17.5312C28.207 17.6309 28.3652 17.8184 28.4355 17.9473C28.5645 18.1934 28.5645 18.2168 28.582 21.2812L28.5996 24.375H27.1875H25.7812V24C25.7812 23.0859 25.2598 22.2246 24.4512 21.8027C24.3809 21.7676 22.7988 21.3164 20.9355 20.8008C19.0723 20.2793 17.502 19.8457 17.4492 19.8281C17.3555 19.7988 17.3438 19.7168 17.3438 19.1953V18.5918L17.666 18.375C18.1055 18.0703 18.7441 17.3848 19.0312 16.9043C19.1602 16.6816 19.3418 16.2715 19.4355 15.9902L19.5996 15.4687H19.9746C20.5312 15.4687 20.7891 15.3691 21.123 15.0293C21.7207 14.4375 21.7207 13.6875 21.123 13.0957C20.8008 12.7676 20.5254 12.6562 20.0449 12.6562C19.8809 12.6562 19.7461 12.6445 19.7461 12.6328C19.7461 12.6211 19.8691 12.3223 20.0156 11.9707C20.3496 11.2031 20.4375 10.8867 20.4844 10.3125C20.5605 9.29883 20.1328 8.17969 19.3652 7.41211C18.9727 7.01953 18.8438 6.9375 18.0527 6.55078L17.1738 6.12304L16.9805 5.71289C16.8047 5.34375 16.7871 5.25 16.7871 4.77539C16.7871 4.28906 16.7988 4.21875 16.9863 3.8789C17.2266 3.43945 17.4902 3.19922 17.9297 3.00586C18.5156 2.74804 18.5977 2.70117 19.1602 2.28515C19.7402 1.85742 20.3672 1.57031 20.9297 1.46484C21.3398 1.39453 22.1016 1.43554 22.5586 1.55273ZM16.0078 6.70898C16.5234 6.84375 18.1582 7.61719 18.4863 7.88086C19.4883 8.70117 19.834 10.0957 19.3125 11.2266L19.1777 11.5137L18.9082 10.9746C18.498 10.166 18.3691 10.1367 17.6074 10.6992C16.6289 11.4199 16.1367 11.6309 15.2695 11.6953C14.2852 11.7715 13.4766 11.502 12.4922 10.7754C11.6133 10.125 11.502 10.1426 11.0742 11.0098C10.9336 11.3027 10.7988 11.543 10.7812 11.543C10.7578 11.543 10.623 11.2969 10.4824 10.998C10.2363 10.4824 10.2246 10.4355 10.2246 9.93164C10.2246 9.44531 10.2363 9.375 10.4238 9.03515C10.6582 8.60156 10.916 8.36133 11.3672 8.15039C11.6367 8.02148 11.7715 7.99804 12.1875 7.99804C12.6211 7.99804 12.7383 8.02148 13.0664 8.17969C13.4531 8.36133 13.7402 8.5957 14.0098 8.95898L14.1562 9.1582L14.5195 8.91211C14.7188 8.77734 14.8828 8.64258 14.8828 8.61914C14.8828 8.53125 14.4434 7.99804 14.1855 7.78125C13.9102 7.54687 13.3066 7.20703 13.1602 7.20703C12.9141 7.20703 13.125 7.04297 13.6113 6.84961C14.3145 6.5625 15.252 6.50976 16.0078 6.70898ZM12.4219 11.8652C13.2656 12.4219 13.9512 12.6211 15 12.6211C16.0488 12.6211 16.7344 12.4219 17.5781 11.8652C17.8477 11.6895 18.082 11.543 18.1055 11.543C18.123 11.543 18.2812 11.8301 18.4512 12.1758L18.7617 12.8086L18.7383 14.0332C18.7148 15.1816 18.7031 15.293 18.5625 15.6914C18.3457 16.3008 18.0762 16.7227 17.6367 17.168C17.1914 17.6074 16.7695 17.877 16.1602 18.0937C15.791 18.2227 15.6387 18.2461 15.0293 18.252C14.4609 18.252 14.25 18.2285 13.916 18.123C12.9609 17.8184 12.1055 17.0977 11.6719 16.2305C11.3262 15.5391 11.25 15.123 11.25 13.8457V12.7734L11.5547 12.1582C11.7246 11.8184 11.877 11.543 11.8945 11.543C11.918 11.543 12.1523 11.6895 12.4219 11.8652ZM10.3125 14.0625V14.5312H9.99023C9.59766 14.5312 9.375 14.3613 9.375 14.0625C9.375 13.7637 9.59766 13.5937 9.99023 13.5937H10.3125V14.0625ZM20.4785 13.7402C20.5723 13.8281 20.625 13.9512 20.625 14.0625C20.625 14.3613 20.4023 14.5312 20.0098 14.5312H19.6875V14.0625V13.5937H20.0098C20.2793 13.5937 20.3613 13.6172 20.4785 13.7402ZM15 19.1777C15.5273 19.1777 15.8965 19.1484 16.1016 19.0898L16.4062 19.002V19.4766V19.9512L15.7031 20.6543L15 21.3574L14.2969 20.6543L13.5938 19.9512V19.4766V19.002L13.9043 19.0898C14.1035 19.1484 14.4727 19.1777 15 19.1777ZM13.8867 21.5918C14.707 22.4004 14.8242 22.5 15 22.5C15.1758 22.5 15.293 22.4004 16.1133 21.5918L17.0273 20.6836L20.5078 21.6504C24.2051 22.6758 24.3047 22.7168 24.5918 23.1387C24.668 23.25 24.75 23.4316 24.7852 23.5488C24.8203 23.6719 24.8438 24.7383 24.8438 26.1738V28.5937H23.4375H22.0312V26.7187V24.8437H21.5625H21.0938V26.7187V28.5937H15H8.90625V26.7187V24.8437H8.4375H7.96875V26.7187V28.5937H6.5625H5.15625V26.1738C5.15625 24.7383 5.17969 23.6719 5.21484 23.5488C5.25 23.4316 5.33203 23.25 5.4082 23.1387C5.68945 22.7168 5.80078 22.6758 9.46289 21.6562C11.3672 21.123 12.9316 20.6895 12.9492 20.6895C12.9609 20.6836 13.3828 21.0937 13.8867 21.5918Z" />
                            </svg>
                            <h6>{{ translate('Customer') }}</h6>
                        </a>
                    </li>
                @endadmin

                <li @if (Illuminate\Support\Facades\Route::is('inquiry.list')) class="active" @endif>
                    <a href="{{ route('inquiry.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-umbrella" viewBox="0 0 460 512">
                            <path
                                d="M220.6 130.3l-67.2 28.2V43.2L98.7 233.5l54.7-24.2v130.3l67.2-209.3zm-83.2-96.7l-1.3 4.7-15.2 52.9C80.6 106.7 52 145.8 52 191.5c0 52.3 34.3 95.9 83.4 105.5v53.6C57.5 340.1 0 272.4 0 191.6c0-80.5 59.8-147.2 137.4-158zm311.4 447.2c-11.2 11.2-23.1 12.3-28.6 10.5-5.4-1.8-27.1-19.9-60.4-44.4-33.3-24.6-33.6-35.7-43-56.7-9.4-20.9-30.4-42.6-57.5-52.4l-9.7-14.7c-24.7 16.9-53 26.9-81.3 28.7l2.1-6.6 15.9-49.5c46.5-11.9 80.9-54 80.9-104.2 0-54.5-38.4-102.1-96-107.1V32.3C254.4 37.4 320 106.8 320 191.6c0 33.6-11.2 64.7-29 90.4l14.6 9.6c9.8 27.1 31.5 48 52.4 57.4s32.2 9.7 56.8 43c24.6 33.2 42.7 54.9 44.5 60.3s.7 17.3-10.5 28.5zm-9.9-17.9c0-4.4-3.6-8-8-8s-8 3.6-8 8 3.6 8 8 8 8-3.6 8-8z" />
                        </svg>
                        <h6>{{ translate('Inquiry') }}</h6>
                    </a>
                </li>
                @admin
                    <li @if (Illuminate\Support\Facades\Route::is('deposits.list') || request()->is('dashboard/deposits/*')) class="active" @endif>
                        <a href="{{ route('deposits.list') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_692_2355)">
                                    <path
                                        d="M4.98884 0.0953236C4.28571 0.256681 3.75 0.635256 3.42857 1.20622L3.24777 1.51653L3.22768 8.20053L3.21429 14.8907H1.60714H0V17.8697V20.8486H15H30V17.8697V14.8907H28.3929H26.7857L26.7723 8.35569L26.7522 1.82683L26.5982 1.47308C26.404 1.03245 25.9688 0.573195 25.5335 0.349773C24.7902 -0.0225945 25.2991 -0.00397491 14.8996 0.0022316C6.87054 0.0084362 5.33036 0.0208492 4.98884 0.0953236ZM7.39955 1.21863C7.27902 1.82683 6.77679 2.47848 6.13393 2.86326C5.71875 3.1115 4.82143 3.4094 4.47991 3.4094H4.28571V2.62742C4.28571 1.55376 4.40625 1.33034 5.10268 1.10692C5.38393 1.02004 5.67188 0.995213 6.45536 0.989006H7.44643L7.39955 1.21863ZM21.4955 1.1876C21.4955 1.47308 21.8371 2.35435 22.1049 2.76396C22.808 3.83762 24.1808 4.7251 25.3661 4.87405L25.7143 4.91749V12.3835V19.8557H23.7054H21.6964L22.0714 19.3405C22.7277 18.4407 23.1629 17.4849 23.4174 16.3802C23.5848 15.6479 23.5781 14.0777 23.4107 13.3392C23.0424 11.7504 22.279 10.4472 21.0335 9.29902C19.7946 8.14468 18.3951 7.43718 16.6741 7.09584C15.8772 6.93448 14.1228 6.93448 13.3259 7.09584C11.5915 7.44339 10.2254 8.13227 8.96652 9.29902C7.70759 10.4658 6.96429 11.7318 6.58929 13.3392C6.42188 14.0777 6.41518 15.6479 6.58259 16.3802C6.83705 17.4849 7.27232 18.4407 7.92857 19.3405L8.30357 19.8557H6.29464H4.28571L4.29911 12.1414L4.3192 4.43341L4.52009 4.41479C5.34375 4.34652 6.46875 3.92451 7.07812 3.45905C7.77455 2.92532 8.30357 2.11852 8.45759 1.35517L8.53795 0.989006H15.0134H21.4955V1.1876ZM24.8438 1.13795C25.5469 1.47929 25.7143 1.85786 25.7143 3.08047C25.7143 4.03001 25.7478 3.99277 25.058 3.77556C23.8259 3.39078 22.7812 2.2985 22.5871 1.1876L22.5469 0.976593L23.5781 1.00142C24.4219 1.02004 24.6496 1.04486 24.8438 1.13795ZM16.1384 8.03297C17.7121 8.25018 19.1451 8.92665 20.2902 9.9879C23.0759 12.5697 23.2366 16.6223 20.6585 19.4212L20.2634 19.8557H15H9.7433L9.42857 19.5205C8.22321 18.2483 7.60714 16.8271 7.52009 15.139C7.39286 12.4828 8.97991 9.96307 11.5781 8.72185C13.0045 8.03917 14.5446 7.80334 16.1384 8.03297ZM3.21429 17.8697V19.8557H2.14286H1.07143V17.8697V15.8837H2.14286H3.21429V17.8697ZM28.9286 17.8697V19.8557H27.8571H26.7857V17.8697V15.8837H27.8571H28.9286V17.8697Z" />
                                    <path
                                        d="M12.2678 12.2284C11.2634 12.4083 10.433 13.1593 10.1719 14.1088L10.0982 14.3943H9.62945C9.22766 14.3943 9.1473 14.4129 9.00668 14.5495C8.7857 14.7481 8.7857 15.0335 9.00668 15.2321C9.1473 15.3687 9.22766 15.3873 9.62945 15.3873H10.0982L10.1719 15.6728C10.5134 16.9264 11.7589 17.7208 13.1786 17.5967C13.6942 17.5532 14.183 17.3236 14.5111 16.9699C14.712 16.7526 15.1004 16.0824 15.3281 15.5549L15.4018 15.3873H17.1094H18.8236L18.7835 15.5238C18.5826 16.1258 17.7857 16.6285 17.029 16.6285C16.5335 16.6285 16.2991 17.156 16.6473 17.4788C16.808 17.6277 16.8549 17.6339 17.2299 17.6029C17.8393 17.547 18.2879 17.4043 18.7634 17.1064C19.279 16.7899 19.7076 16.2375 19.8281 15.7286L19.9085 15.3873H20.3705C20.7723 15.3873 20.8527 15.3687 20.9933 15.2321C21.1004 15.1391 21.1607 15.0087 21.1607 14.8908C21.1607 14.7729 21.1004 14.6426 20.9933 14.5495C20.8527 14.4129 20.7723 14.3943 20.3705 14.3943H19.9085L19.8281 14.053C19.7076 13.5441 19.279 12.9917 18.7634 12.6752C18.2009 12.3277 17.7187 12.1911 17.0089 12.1911C16.5 12.1911 16.3995 12.2098 16.0781 12.3773C15.8839 12.4766 15.6161 12.6752 15.4888 12.8117C15.2812 13.0228 14.9263 13.6434 14.6786 14.2205L14.5982 14.3943H12.8906H11.183L11.25 14.2392C11.5312 13.5441 12.1473 13.1531 12.9375 13.1531C13.2656 13.1531 13.5268 12.9359 13.5268 12.6566C13.5268 12.5387 13.4665 12.4083 13.3594 12.3153C13.1853 12.1477 12.8504 12.1229 12.2678 12.2284ZM17.8527 13.3331C18.3214 13.5317 18.6495 13.8482 18.7835 14.2516L18.8236 14.3943H17.3169C16.3259 14.3943 15.8036 14.3695 15.8036 14.3323C15.8036 14.295 15.8906 14.1088 15.9978 13.9102C16.1853 13.5627 16.3728 13.3703 16.6406 13.2214C16.8348 13.1158 17.4643 13.1779 17.8527 13.3331ZM14.1964 15.4494C14.1964 15.5611 13.8549 16.1569 13.7009 16.3058C13.3794 16.6409 12.9643 16.7092 12.3348 16.5354C11.8125 16.3865 11.471 16.0762 11.25 15.5424L11.183 15.3873H12.6897C13.6808 15.3873 14.1964 15.4121 14.1964 15.4494Z" />
                                    <path
                                        d="M12.0267 21.972L11.8526 22.0961V23.4615V24.8206H10.9553C10.1517 24.8206 10.0445 24.833 9.88382 24.9509C9.7499 25.044 9.70972 25.1309 9.70972 25.3047C9.70972 25.5281 9.81686 25.6336 12.0066 27.663C13.982 29.4938 14.3303 29.7855 14.4977 29.7855C14.6718 29.7855 15.02 29.4876 17.0222 27.6258C18.7432 26.037 19.3526 25.435 19.3526 25.3295C19.3526 25.2488 19.2655 25.0999 19.1584 25.0006L18.9642 24.8206H18.0869H17.2097V23.5111V22.2016L17.0155 22.0216L16.8213 21.8417H14.5111C12.2811 21.8417 12.2008 21.8479 12.0267 21.972ZM16.1383 24.169C16.1383 25.5095 16.1383 25.5095 16.3057 25.6584C16.4463 25.795 16.5267 25.8136 16.9887 25.8136H17.5111L16.0378 27.1789C15.2276 27.9299 14.5378 28.5443 14.4977 28.5443C14.4575 28.5443 13.7678 27.9299 12.9575 27.1789L11.4843 25.8136H11.9999C12.4486 25.8136 12.5423 25.795 12.703 25.6646L12.8905 25.5157L12.9106 24.1752L12.9307 22.8347H14.5378H16.1383V24.169Z" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_692_2355">
                                        <rect width="30" height="30" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <h6>{{ translate('Deposits') }}</h6>
                        </a>
                    </li>
                @endadmin
                <li @if (Illuminate\Support\Facades\Route::is('withdraw.list') || request()->is('dashboard/withdraw/*')) class="active" @endif>
                    <a href="{{ route('withdraw.list') }}">
                        <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M2.98828 1.99219V3.98438H3.48633H3.98438L3.99609 5.98828L4.01367 7.99805L6.52148 8.01562L9.02344 8.02734V9.88477V11.7422L8.0332 12.7441C7.10742 13.6758 7.03711 13.7578 7.00195 14.0039C6.98438 14.1504 6.97266 15.7031 6.98438 17.4609L7.00195 20.6543L8.25 24.1055L9.49219 27.5625V28.6113V29.6602L9.66211 29.8301L9.83203 30H13.002H16.166L16.3301 29.8594L16.4941 29.7188L16.5117 25.8398L16.5293 21.9609L15.9082 20.5137C15.5684 19.7227 15.293 19.0488 15.293 19.0254C15.293 19.002 16.5059 18.9844 17.9883 18.9844H20.6895L20.8301 18.8379L20.9766 18.6973V13.3652V8.02734L23.4844 8.01562L25.9863 7.99805L26.0039 5.98828L26.0156 3.98438H26.5137H27.0117V1.99219V0H15H2.98828V1.99219ZM26.0156 1.99219V2.98828H15H3.98438V1.99219V0.996094H15H26.0156V1.99219ZM25.0078 5.49023L25.0195 6.97266H22.998H20.9707L20.9883 6.48633L21.0059 6.00586L21.4922 5.98828L21.9727 5.9707V5.47852V4.98047H15H8.02734V5.47852V5.9707L8.51367 5.98828L8.99414 6.00586L9.01172 6.48633L9.0293 6.97266H7.00195H4.98047V5.51953C4.98047 4.7168 4.99805 4.04297 5.02148 4.02539C5.03906 4.00195 9.54492 3.99023 15.0234 3.99609L24.9902 4.01367L25.0078 5.49023ZM11.0039 9.50391L11.0156 13.0078H11.5137H12.0117V10.5996V8.19141L12.2344 8.39062C13.875 9.87305 16.1836 9.85547 17.8008 8.35547L17.9883 8.17969V10.5938V13.0078H18.4863H18.9844L18.9961 9.50391L19.0137 6.00586L19.5 5.98828L19.9805 5.9707V11.9824V17.9883H17.4902H15V16.8867C15 15.6621 14.9531 15.416 14.6426 15.041C14.3438 14.6836 13.9512 14.5195 13.3594 14.5078C12.75 14.502 12.498 14.5488 12.2227 14.7363L12.0117 14.877V14.4434V14.0039H11.5137H11.0156V15.498V16.9922H11.25H11.4844V17.4902V17.9883H10.752H10.0195V11.9824V5.9707L10.5059 5.98828L10.9863 6.00586L11.0039 9.50391ZM17.9121 6.19336C17.8477 6.58594 17.4727 7.25391 17.1152 7.61133C15.668 9.05273 13.2363 8.66602 12.3398 6.84961C12.2227 6.60938 12.1113 6.31641 12.0879 6.19336L12.0527 5.97656H15H17.9473L17.9121 6.19336ZM9.02344 15.9668V18.6973L9.16992 18.8379C9.31055 18.9844 9.32227 18.9844 10.4004 18.9844H11.4844V19.9805V20.9766H11.9824H12.4805V18.4922C12.4805 16.9102 12.5039 15.9434 12.5391 15.8438C12.6387 15.5859 12.7617 15.5332 13.248 15.5273C13.6055 15.5273 13.7285 15.5508 13.834 15.6387C13.9746 15.75 13.9746 15.7617 14.0039 17.2383L14.0332 18.7207L14.7832 20.4434L15.5273 22.1602L15.5156 24.5742L15.498 26.9824H12.9199H10.3418L9.1875 23.7598L8.0332 20.5371V17.3613L8.02734 14.1797L8.48438 13.7109C8.73047 13.4531 8.95312 13.2422 8.98242 13.2422C9.00586 13.2422 9.02344 14.4668 9.02344 15.9668ZM15.5156 28.4883L15.498 28.9746L12.9961 28.9922L10.4883 29.0039V28.5059V28.0078H13.0078H15.5332L15.5156 28.4883Z" />
                            <path
                                d="M6.01758 5.02148C5.99414 5.03906 5.97656 5.26758 5.97656 5.51953V5.97656H6.47461H6.97266V5.47852V4.98047H6.51562C6.26367 4.98047 6.03516 4.99805 6.01758 5.02148Z" />
                            <path
                                d="M23.0273 5.47852V5.97656H23.5254H24.0293L24.0117 5.49024L23.9941 5.00977L23.5137 4.99219L23.0273 4.97461V5.47852Z" />
                            <path
                                d="M14.5313 10.5762C13.9395 10.7637 13.5234 11.3379 13.5176 11.9824C13.5176 12.4102 13.6406 12.7266 13.9336 13.0313C14.2383 13.3477 14.543 13.4766 15 13.4766C15.457 13.4766 15.7617 13.3477 16.0664 13.0313C16.3594 12.7207 16.4824 12.4102 16.4824 11.9766C16.4766 11.332 16.0488 10.7578 15.4453 10.5762C15.0938 10.4707 14.877 10.4707 14.5313 10.5762ZM15.2285 11.5723C15.416 11.666 15.4688 11.7656 15.4688 12.0293C15.4688 12.4336 14.9355 12.6445 14.666 12.3457C14.4434 12.1055 14.4844 11.7188 14.7539 11.5781C14.9531 11.4668 15.0234 11.4668 15.2285 11.5723Z" />
                            <path
                                d="M17.9883 15V15.9961H16.9922H15.9961V16.4941V16.9922H17.4902H18.9844V15.498V14.0039H18.4863H17.9883V15Z" />
                        </svg>
                        <h6>{{ translate('Withdrawal') }}</h6>
                    </a>
                </li>
                @admin
                    <li @if (Illuminate\Support\Facades\Route::is('page.list') || request()->is('dashboard/pages/*')) class="active" @endif>
                        <a href="{{ route('page.list') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M0.615234 0.0820312C0.369141 0.1875 0.0703125 0.544922 0.0292969 0.773438C0.0117187 0.878906 0.00585938 6.62695 0.0117187 13.541L0.0292969 26.1152L0.199219 26.332C0.427734 26.6367 0.755859 26.7773 1.24805 26.7773H1.64062V27.1582C1.64062 27.5742 1.74609 27.8965 1.95117 28.1133C2.16211 28.3359 2.28516 28.3828 2.8125 28.418L3.31055 28.4473L3.33984 28.916C3.375 29.4727 3.49805 29.6895 3.88476 29.877C4.13672 30 4.16016 30 11.1973 30H18.2519L20.4023 27.8438L22.5586 25.6934V23.4199V21.1523H22.957C23.7949 21.1523 24.9199 20.8828 25.8281 20.4727C26.373 20.2207 26.4844 20.127 26.4844 19.9102C26.4844 19.6758 26.3906 19.5176 26.209 19.4473C26.0859 19.4004 25.9629 19.4414 25.4707 19.6641C23.3144 20.6543 20.8418 20.4141 18.9609 19.0312L18.5566 18.7383L20.7246 16.5703L22.8867 14.4082L25.8281 15.1816C28.5996 15.9141 28.7695 15.9668 28.7344 16.084C28.6348 16.4297 28.1426 17.3262 27.8086 17.7832C27.5977 18.0645 27.4219 18.3398 27.4219 18.3984C27.4219 18.5977 27.6504 18.8086 27.873 18.8086C28.1191 18.8086 28.3301 18.6035 28.7754 17.9297C29.2852 17.1621 29.6426 16.3184 29.8652 15.3398C30.0059 14.7188 30.0059 13.1426 29.8594 12.4922C29.5371 10.998 28.8457 9.73828 27.7793 8.71289C26.4961 7.47656 24.9375 6.79102 23.1914 6.69727L22.5644 6.66211L22.5469 5.27344L22.5293 3.88477L22.3594 3.66797C22.1309 3.36328 21.8027 3.22266 21.3223 3.22266H20.9355L20.9062 2.78906C20.8535 2.01562 20.5312 1.69922 19.7402 1.65234L19.2773 1.62305V1.40625C19.2773 1.03711 19.166 0.585938 19.0371 0.398438C18.9668 0.298828 18.8027 0.169922 18.668 0.111328C18.4394 0.00585938 18.0879 0 9.60937 0.00585938C2.44922 0.00585938 0.755859 0.0175781 0.615234 0.0820312ZM18.3398 0.990234C18.3691 1.05469 18.3984 1.22461 18.3984 1.37109V1.64062H13.248C8.13281 1.64062 8.10351 1.64062 7.94531 1.76367C7.75195 1.91602 7.73437 2.22656 7.91015 2.40234C8.02734 2.51953 8.10351 2.51953 13.9629 2.51953C18.5039 2.51953 19.916 2.53711 19.9687 2.58984C20.0098 2.63086 20.0391 2.78906 20.0391 2.94141V3.22266L12.0234 3.23438L4.00781 3.25195L3.76758 3.41016C3.29883 3.7207 3.33984 2.56055 3.33984 15.7559V27.5449L2.94727 27.5273L2.54883 27.5098L2.53125 15.0938C2.52539 5.96484 2.53711 2.66016 2.58398 2.60156C2.63672 2.53711 2.98242 2.51953 4.2832 2.51953C5.8418 2.51953 5.92383 2.51367 6.03516 2.40234C6.21094 2.22656 6.19336 1.91602 5.99414 1.75781C5.8418 1.63477 5.79492 1.63477 4.10156 1.65234C2.41406 1.66992 2.36719 1.67578 2.15625 1.80469C2.03906 1.88086 1.88086 2.03906 1.80469 2.15625L1.66992 2.37305L1.65234 14.1387L1.64062 25.9043L1.27148 25.8867L0.908203 25.8691L0.890625 13.4473C0.884766 6.61523 0.890625 0.990234 0.908203 0.949219C0.925781 0.896484 2.72461 0.878906 9.60351 0.878906C18.1172 0.878906 18.2754 0.878906 18.3398 0.990234ZM21.668 5.43164C21.6855 6.87891 21.7266 6.74414 21.2402 6.82617C20.8184 6.89648 20.0742 7.1543 19.5644 7.41211C17.6309 8.36719 16.2949 10.0137 15.7207 12.1289C15.5215 12.8848 15.4629 14.3555 15.6094 15.1465C16.0137 17.3379 17.3027 19.1602 19.207 20.2383C19.6992 20.5195 20.5078 20.8359 21.0059 20.9473C21.2344 20.9941 21.4746 21.0527 21.5508 21.0645L21.6797 21.0938V23.0566V25.0195H20.1445C19.2656 25.0195 18.5098 25.0488 18.3867 25.0781C18.1055 25.1602 17.8184 25.4004 17.7187 25.6465C17.6543 25.793 17.6367 26.2266 17.6367 27.4805V29.1211L10.9453 29.1094L4.24805 29.0918L4.23047 16.6699C4.22461 9.83789 4.23047 4.21289 4.24805 4.17188C4.26562 4.11328 6.04101 4.10156 12.9609 4.11328L21.6504 4.13086L21.668 5.43164ZM23.7949 7.64648C24.0644 7.69336 24.4394 7.77539 24.6211 7.83398L24.9551 7.93945L23.666 10.8105L22.3769 13.6816L20.1562 15.9023L17.9414 18.123L17.6426 17.7188C17.0801 16.957 16.6699 16.0254 16.5 15.1172C16.3828 14.502 16.377 13.3359 16.4941 12.7324C16.9863 10.1367 19.0254 8.12109 21.6387 7.64648C22.2129 7.54102 23.2031 7.54102 23.7949 7.64648ZM26.2617 8.60742C28.1719 9.85547 29.3203 12.2461 29.0801 14.4961C29.0449 14.8066 29.0098 15.0703 28.998 15.082C28.9746 15.1055 23.4316 13.6582 23.3906 13.6172C23.3555 13.5938 25.7402 8.32031 25.7871 8.32031C25.8047 8.32031 26.0215 8.44922 26.2617 8.60742ZM19.7988 27.1758L18.5449 28.4531L18.5273 27.2227C18.5156 26.2793 18.5332 25.9863 18.5859 25.9453C18.6269 25.9219 19.2012 25.8984 19.8574 25.8984H21.0469L19.7988 27.1758Z" />
                                <path
                                    d="M6.80277 7.41793C6.66215 7.59957 6.65043 7.66989 6.75004 7.88082C6.79106 7.97457 6.89066 8.05075 7.02543 8.09176C7.16605 8.13278 8.69535 8.1445 11.5899 8.13278L15.9493 8.1152L16.0606 7.97457C16.2071 7.79879 16.2012 7.52926 16.0547 7.38278C15.9375 7.26559 15.8614 7.26559 11.4317 7.26559H6.91996L6.80277 7.41793Z" />
                                <path
                                    d="M6.80273 10.6406C6.73242 10.7285 6.67969 10.834 6.67969 10.875C6.67969 11.039 6.82031 11.2441 6.9668 11.3086C7.07227 11.3496 8.16797 11.3672 10.541 11.3554L13.957 11.3379L14.0684 11.1972C14.2148 11.0215 14.209 10.7519 14.0625 10.6054C13.9453 10.4883 13.8691 10.4883 10.4355 10.4883H6.91992L6.80273 10.6406Z" />
                                <path
                                    d="M6.80273 13.8633C6.73242 13.9511 6.67969 14.0566 6.67969 14.0976C6.67969 14.2617 6.82031 14.4668 6.9668 14.5312C7.07227 14.5722 8.0332 14.5898 10.0723 14.5781L13.0195 14.5605L13.1309 14.4199C13.2773 14.2441 13.2715 13.998 13.125 13.8398L13.0019 13.7109H9.96093H6.91992L6.80273 13.8633Z" />
                                <path
                                    d="M9.78517 18.75C9.67384 18.8613 9.66798 18.9434 9.66798 20.3027V21.7383H8.67774H7.68165L7.56446 21.8906C7.44728 22.043 7.44142 22.1191 7.44142 23.6719V25.2949L7.20118 25.3301C7.07228 25.3477 6.91407 25.4062 6.84962 25.4648C6.70314 25.5996 6.70314 25.9219 6.85548 26.0742C6.97267 26.1914 7.04884 26.1914 11.1914 26.1914C15.334 26.1914 15.4102 26.1914 15.5274 26.0742C15.7969 25.8047 15.6035 25.3887 15.1875 25.3301L14.9414 25.2949V23.1152C14.9414 20.9941 14.9356 20.9297 14.8184 20.7773L14.7012 20.625H13.7051H12.7149V19.7578C12.7149 18.5508 12.832 18.6328 11.1914 18.6328C9.97852 18.6328 9.89649 18.6387 9.78517 18.75ZM11.8359 22.4121V25.3125H11.1914H10.5469V22.4121V19.5117H11.1914H11.8359V22.4121ZM14.0625 23.4082V25.3125H13.3887H12.7149V23.4082V21.5039H13.3887H14.0625V23.4082ZM9.66798 23.9648V25.3125H8.99415H8.32032V23.9648V22.6172H8.99415H9.66798V23.9648Z" />
                            </svg>
                            <h6>{{ translate('Pages') }}</h6>
                        </a>
                    </li>
                @endadmin


                @admin
                    <li @if (Illuminate\Support\Facades\Route::is('menu.list') || request()->is('dashboard/menu/*')) class="active" @endif>
                        <a href="{{ route('menu.list') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-menu-app-fill" viewBox="0 0 16 16">
                                <path
                                    d="M0 1.5A1.5 1.5 0 0 1 1.5 0h2A1.5 1.5 0 0 1 5 1.5v2A1.5 1.5 0 0 1 3.5 5h-2A1.5 1.5 0 0 1 0 3.5v-2zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V8zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2H1zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2h14zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5z" />
                            </svg>
                            <h6>{{ translate('Menu') }}</h6>
                        </a>
                    </li>




                    <li @if (Illuminate\Support\Facades\Route::is('blog.list') || request()->is('dashboard/blogs/*')) class="active" @endif>
                        <a href="{{ route('blog.list') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_692_2372)">
                                    <path
                                        d="M4.2832 0.0820312C3.97851 0.251953 3.99023 0.111328 3.98437 5.01562V9.5918L3.59179 9.66211C2.43164 9.87305 1.4707 10.8047 1.23633 11.9414C1.18945 12.1582 1.17187 14.209 1.17187 18.6621V25.0781H1.04297C0.849608 25.084 0.46289 25.2715 0.310546 25.4355C-8.01403e-07 25.7754 -0.0703133 26.2383 0.0878898 26.9824C0.380858 28.3887 1.61133 29.6191 3.01758 29.9121C3.63281 30.041 26.3672 30.041 26.9824 29.9121C28.0547 29.6895 29.0918 28.8809 29.584 27.8906C30.1055 26.8301 30.1289 25.834 29.6426 25.3887C29.4785 25.2363 29.1211 25.0781 28.9453 25.0781C28.8281 25.0781 28.8281 25.0723 28.8281 19.2363V13.3945L29.414 12.8027C30.0234 12.1816 30.0879 12.0586 29.9355 11.7598C29.8125 11.5137 27.2812 9.02344 27.0937 8.96484C26.8418 8.88281 26.6367 8.9707 26.2969 9.30469L26.0156 9.57422V5.02148C26.0156 0.0703125 26.0273 0.222656 25.6933 0.0703125C25.582 0.0175781 24.5156 0 21.5273 0C17.1738 0 17.2676 -0.00585938 17.1211 0.322266C16.9863 0.621094 17.0801 0.949219 17.3496 1.08984C17.4844 1.16016 18.123 1.17188 21.1816 1.17188H24.8437V5.96484V10.752L22.7168 12.8789L20.5957 15H13.9629C8.94726 15 7.29492 15.0176 7.17773 15.0703C7.00781 15.1465 6.85546 15.3926 6.85546 15.5859C6.85546 15.7793 7.00781 16.0254 7.17773 16.1016C7.29492 16.1543 8.81249 16.1719 13.3769 16.1719H19.4238L18.1348 17.4609L16.8457 18.75H12.0879C8.52538 18.75 7.28906 18.7676 7.17773 18.8203C7.00781 18.8965 6.85546 19.1426 6.85546 19.3359C6.85546 19.5293 7.00781 19.7754 7.17773 19.8516C7.28906 19.9043 8.39062 19.9219 11.4961 19.9219H15.6621L15.1699 20.4375C14.7187 20.9062 14.6543 21.0117 14.3906 21.627C14.2324 21.9961 13.8281 22.9219 13.4941 23.6836L12.8789 25.0781H9.01757H5.15625V13.125V1.17188H8.81835C11.5254 1.17188 12.5156 1.1543 12.6269 1.10156C12.7969 1.02539 12.9492 0.779297 12.9492 0.585938C12.9492 0.392578 12.7969 0.146484 12.6269 0.0703125C12.3926 -0.0351562 4.47656 -0.0234375 4.2832 0.0820312ZM23.1328 17.4434L17.6953 22.8809L16.875 22.0605L16.0547 21.2402L21.4863 15.8086L26.9238 10.3711L27.7441 11.1914L28.5644 12.0117L23.1328 17.4434ZM3.98437 17.9297V25.0781H3.16406H2.34375V18.7031C2.34375 11.7188 2.33203 12 2.68945 11.502C2.7832 11.373 2.94726 11.2031 3.05273 11.127C3.24023 10.9922 3.75586 10.7871 3.91406 10.7812C3.96679 10.7812 3.98437 12.2461 3.98437 17.9297ZM27.6562 19.834V25.0781H26.8359H26.0156V20.6426V16.2012L26.8183 15.3984C27.2637 14.9531 27.6328 14.5898 27.6387 14.5898C27.6504 14.5898 27.6562 16.9512 27.6562 19.834ZM24.8262 25.0605C24.8144 25.0723 22.8457 25.0723 20.4609 25.0664L16.1133 25.0488L17.0449 24.6445L17.9824 24.2344L21.3984 20.8184L24.8144 17.4023L24.832 21.2227C24.8379 23.3203 24.8379 25.0488 24.8262 25.0605ZM16.6406 23.502C16.6406 23.543 14.4668 24.5098 14.4433 24.4805C14.4316 24.4629 14.8418 23.5137 15.2695 22.5645L15.416 22.2422L16.0254 22.8516C16.3652 23.1914 16.6406 23.4785 16.6406 23.502ZM28.8281 26.3379C28.8281 26.5605 28.6758 27.0762 28.5234 27.3867C28.1894 28.0488 27.4687 28.6055 26.7305 28.7637C26.5078 28.8105 23.2324 28.8281 14.9824 28.8281C2.50781 28.8281 3.25195 28.8457 2.5664 28.5C2.16211 28.2949 1.69336 27.8145 1.47656 27.3867C1.32422 27.0762 1.17187 26.5605 1.17187 26.3379C1.17187 26.2617 2.95312 26.25 15 26.25C27.0469 26.25 28.8281 26.2617 28.8281 26.3379Z" />
                                    <path
                                        d="M14.7128 0.0820311C14.2031 0.363281 14.4199 1.17188 14.9999 1.17188C15.2988 1.17188 15.5859 0.884766 15.5859 0.591797C15.5859 0.152344 15.0996 -0.134766 14.7128 0.0820311Z" />
                                    <path
                                        d="M7.15432 3.83203C6.76174 4.04883 6.77346 4.66992 7.17776 4.85156C7.41213 4.95703 9.73831 4.94531 9.9551 4.83984C10.3418 4.63477 10.3418 4.03711 9.9551 3.83203C9.72659 3.71485 7.36526 3.71485 7.15432 3.83203Z" />
                                    <path
                                        d="M12.6621 3.83203C12.2696 4.04883 12.2813 4.66992 12.6856 4.85156C12.7969 4.9043 14.0684 4.92188 17.7539 4.92188C21.4395 4.92188 22.711 4.9043 22.8223 4.85156C22.9922 4.77539 23.1445 4.5293 23.1445 4.33594C23.1445 4.14258 22.9922 3.89648 22.8223 3.82031C22.5879 3.71484 12.8555 3.72656 12.6621 3.83203Z" />
                                    <path
                                        d="M7.15432 7.58208C6.76174 7.79888 6.77346 8.41997 7.17776 8.60162C7.38284 8.69537 16.2715 8.70123 16.4824 8.60748C16.6465 8.5313 16.8164 8.26763 16.8164 8.08599C16.8164 7.90435 16.6465 7.64068 16.4824 7.5645C16.2598 7.4649 7.34182 7.48247 7.15432 7.58208Z" />
                                    <path
                                        d="M19.2246 7.58203C18.9492 7.73437 18.8555 8.07422 19.0078 8.37305C19.1543 8.6543 19.2715 8.67188 21.041 8.67188C22.8223 8.67188 22.9395 8.6543 23.0742 8.34961C23.209 8.05078 23.1153 7.72266 22.8457 7.58203C22.6172 7.46484 19.4356 7.46484 19.2246 7.58203Z" />
                                    <path
                                        d="M7.15432 11.332C6.76174 11.5488 6.77346 12.1699 7.17776 12.3516C7.28909 12.4043 8.02737 12.4219 9.87893 12.4219C12.0352 12.4219 12.4512 12.4102 12.5977 12.334C12.8028 12.2285 12.8906 12.082 12.8906 11.8359C12.8906 11.5898 12.8028 11.4434 12.5977 11.3379C12.3633 11.2148 7.37698 11.209 7.15432 11.332Z" />
                                    <path
                                        d="M15.2227 11.4199C15.0645 11.5605 15.0293 11.6309 15.0293 11.8359C15.0293 12.041 15.0645 12.1113 15.2227 12.252L15.4102 12.4219H18.2812C21.3574 12.4219 21.3809 12.4219 21.5391 12.123C21.6973 11.8184 21.5859 11.4492 21.2988 11.3203C21.1875 11.2676 20.3789 11.25 18.2812 11.25H15.4102L15.2227 11.4199Z" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_692_2372">
                                        <rect width="30" height="30" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <h6>{{ translate('Blogs') }}</h6>
                        </a>
                    </li>

                    <li @if (Illuminate\Support\Facades\Route::is('email.template.list') || request()->is('dashboard/email/template/*')) class="active" @endif>
                        <a href="{{ route('email.template.list') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-envelope" viewBox="0 0 16 16">
                                <path
                                    d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z" />
                            </svg>
                            <h6>{{ translate('Email Template') }}</h6>
                        </a>
                    </li>
                @endadmin
                <li @if (Illuminate\Support\Facades\Route::is('support.list') || request()->is('dashboard/supports/*')) class="active" @endif>
                    <a href="{{ route('support.list') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                            class="bi bi-chat-left-text" viewBox="0 0 16 16">
                            <path
                                d="M14 1a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H4.414A2 2 0 0 0 3 11.586l-2 2V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12.793a.5.5 0 0 0 .854.353l2.853-2.853A1 1 0 0 1 4.414 12H14a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z" />
                            <path
                                d="M3 3.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zM3 6a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9A.5.5 0 0 1 3 6zm0 2.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5z" />
                        </svg>
                        <h6>{{ translate('Ticket/Support') }}</h6>
                    </a>
                </li>


                @admin
                    <li @if (Illuminate\Support\Facades\Route::is('languages.list') || request()->is('dashboard/languages/*')) class="active" @endif>
                        <a href="{{ route('languages.list') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-translate" viewBox="0 0 16 16">
                                <path
                                    d="M4.545 6.714 4.11 8H3l1.862-5h1.284L8 8H6.833l-.435-1.286H4.545zm1.634-.736L5.5 3.956h-.049l-.679 2.022H6.18z" />
                                <path
                                    d="M0 2a2 2 0 0 1 2-2h7a2 2 0 0 1 2 2v3h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-3H2a2 2 0 0 1-2-2V2zm2-1a1 1 0 0 0-1 1v7a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H2zm7.138 9.995c.193.301.402.583.63.846-.748.575-1.673 1.001-2.768 1.292.178.217.451.635.555.867 1.125-.359 2.08-.844 2.886-1.494.777.665 1.739 1.165 2.93 1.472.133-.254.414-.673.629-.89-1.125-.253-2.057-.694-2.82-1.284.681-.747 1.222-1.651 1.621-2.757H14V8h-3v1.047h.765c-.318.844-.74 1.546-1.272 2.13a6.066 6.066 0 0 1-.415-.492 1.988 1.988 0 0 1-.94.31z" />
                            </svg>
                            <h6>{{ translate('Languages') }}</h6>
                        </a>
                    </li>
                    <li @if (Illuminate\Support\Facades\Route::is('payment.methods')) class="active" @endif>
                        <a href="{{ route('payment.methods') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-credit-card-2-front" viewBox="0 0 16 16">
                                <path
                                    d="M14 3a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zM2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2z" />
                                <path
                                    d="M2 5.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5m3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5m3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5m3 0a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5" />
                            </svg>
                            <h6>{{ translate('Payment Methods') }}</h6>
                        </a>
                    </li>
                    <li @if (Illuminate\Support\Facades\Route::is('location.list') ||
                            request()->is('dashboard/country/*') ||
                            request()->is('dashboard/state/*') ||
                            request()->is('dashboard/city/*')) class="active" @endif>
                        <a href="{{ route('location.list') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor"
                                class="bi bi-geo-alt" viewBox="0 0 16 16">
                                <path
                                    d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z" />
                                <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" />
                            </svg>
                            <h6>{{ translate('Location') }}</h6>
                        </a>
                    </li>
                    <li @if (Illuminate\Support\Facades\Route::is('contact.list') || request()->is('dashboard/contacts/*')) class="active" @endif>
                        <a href="{{ route('contact.list') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="35" height="35"
                                viewBox="0,0,256,256">
                                <g fill="#ffffff" fill-rule="nonzero" stroke="none" stroke-width="1"
                                    stroke-linecap="butt" stroke-linejoin="miter" stroke-miterlimit="5"
                                    stroke-dasharray="" stroke-dashoffset="0" font-family="none" font-weight="none"
                                    font-size="none" text-anchor="none" style="mix-blend-mode: normal">
                                    <g transform="scale(5.12,5.12)">
                                        <path
                                            d="M13,4c-4.95852,0 -9,4.04148 -9,9v24c0,4.95852 4.04148,9 9,9h24c4.95852,0 9,-4.04148 9,-9v-24c0,-4.95852 -4.04148,-9 -9,-9zM13,6h24c0.35503,0 0.65944,0.15063 1,0.20117v37.59766c-0.34056,0.05054 -0.64497,0.20117 -1,0.20117h-24c-3.87748,0 -7,-3.12252 -7,-7v-24c0,-3.87748 3.12252,-7 7,-7zM40,6.73047c2.35207,1.12598 4,3.47897 4,6.26953v1h-4zM22,12c-7.16786,0 -13,5.83214 -13,13c0,7.16786 5.83214,13 13,13c7.16786,0 13,-5.83214 13,-13c0,-7.16786 -5.83214,-13 -13,-13zM22,14c6.08698,0 11,4.91302 11,11c0,2.8221 -1.06519,5.38334 -2.80469,7.32813c-2.02551,-2.0565 -4.95552,-3.32812 -8.19531,-3.32812c-3.24107,0 -6.16612,1.27667 -8.19141,3.33398c-1.74226,-1.9454 -2.80859,-4.50938 -2.80859,-7.33398c0,-6.08698 4.91302,-11 11,-11zM40,16h4v8h-4zM22,18c-1.58333,0 -2.89811,0.62976 -3.74805,1.58594c-0.84994,0.95618 -1.25195,2.19184 -1.25195,3.41406c0,1.22222 0.40201,2.45788 1.25195,3.41406c0.84994,0.95618 2.16471,1.58594 3.74805,1.58594c1.58333,0 2.89811,-0.62976 3.74805,-1.58594c0.84994,-0.95618 1.25195,-2.19184 1.25195,-3.41406c0,-1.22222 -0.40201,-2.45788 -1.25195,-3.41406c-0.84994,-0.95618 -2.16471,-1.58594 -3.74805,-1.58594zM22,20c1.08333,0 1.76856,0.37024 2.25195,0.91406c0.48339,0.54382 0.74805,1.30816 0.74805,2.08594c0,0.77778 -0.26465,1.54212 -0.74805,2.08594c-0.48339,0.54382 -1.16862,0.91406 -2.25195,0.91406c-1.08333,0 -1.76856,-0.37024 -2.25195,-0.91406c-0.48339,-0.54382 -0.74805,-1.30816 -0.74805,-2.08594c0,-0.77778 0.26465,-1.54212 0.74805,-2.08594c0.48339,-0.54382 1.16862,-0.91406 2.25195,-0.91406zM40,26h4v8h-4zM22,31c2.69439,0 5.0928,1.05593 6.73047,2.69531c-1.85982,1.44025 -4.18954,2.30469 -6.73047,2.30469c-2.5384,0 -4.8657,-0.86313 -6.72461,-2.30078c1.63766,-1.63856 4.03035,-2.69922 6.72461,-2.69922zM40,36h4v1c0,2.79057 -1.64793,5.14355 -4,6.26953z">
                                        </path>
                                    </g>
                                </g>
                            </svg>
                            @php
                                $unread = App\Models\Contact::where('status', 1)->count();
                            @endphp
                            @if ($unread)
                                <span class="unread">{{ $unread }}</span>
                            @endif
                            <h6>{{ translate('Contacts') }}</h6>
                        </a>
                    </li>
                    <li @if (Illuminate\Support\Facades\Route::is('backend.setting')) class="active" @endif>
                        <a href="{{ route('backend.setting') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M1.875 1.01367C1.58789 1.10742 1.0957 1.61133 1.00781 1.91015C0.908203 2.26172 0.908203 22.1133 1.00781 22.4648C1.10156 22.7871 1.58789 23.2734 1.91016 23.3672C2.0918 23.4199 3.28711 23.4375 6.63281 23.4375H11.1211L10.5352 25.7812L9.94922 28.125H8.95899H7.96875V28.5937V29.0625H15H22.0313V28.5937V28.125H21.041H20.0508L19.4648 25.7812L18.8789 23.4375H23.373C28.4883 23.4375 28.2305 23.4609 28.6699 22.9687C28.8105 22.8164 28.957 22.582 28.9922 22.4531C29.0918 22.1191 29.0918 2.25586 28.9922 1.92187C28.957 1.79297 28.8105 1.55859 28.6699 1.40625C28.207 0.890623 29.5195 0.937498 14.9766 0.943357C5.75391 0.949217 2.02148 0.966795 1.875 1.01367ZM27.9785 2.02148C28.1016 2.13867 28.125 2.2207 28.125 2.49023V2.8125H16.4063H4.6875V3.28125V3.75H16.4063H28.125V11.7187V19.6875H15H1.875V10.9277V2.16211L2.02148 2.02148L2.16211 1.875H15H27.8379L27.9785 2.02148ZM28.125 21.416C28.125 22.1836 28.1191 22.2187 27.9785 22.3535L27.8379 22.5H15H2.16211L2.02148 22.3535C1.88086 22.2187 1.875 22.1836 1.875 21.416V20.625H15H28.125V21.416ZM18.5039 25.7812L19.0898 28.125H15H10.9102L11.4961 25.7812L12.082 23.4375H15H17.918L18.5039 25.7812Z" />
                                <path d="M2.8125 3.28125V3.75H3.28125H3.75V3.28125V2.8125H3.28125H2.8125V3.28125Z" />
                                <path d="M7.03125 11.7188V18.75H7.5H7.96875V11.7188V4.6875H7.5H7.03125V11.7188Z" />
                                <path
                                    d="M9.65041 5.61328C8.66603 6.60938 8.66603 6.51562 9.66798 7.52344L10.4297 8.29102L10.7637 7.95703L11.1035 7.61719L10.5762 7.08984L10.0488 6.5625L10.5645 6.04688C10.8457 5.76562 11.0742 5.50781 11.0742 5.47266C11.0742 5.39648 10.5293 4.86328 10.4531 4.86328C10.4238 4.86328 10.0606 5.20312 9.65041 5.61328Z" />
                                <path
                                    d="M14.5137 5.14453C14.3613 5.29688 14.2383 5.44922 14.2383 5.47852C14.2383 5.50781 14.4668 5.76562 14.748 6.04688L15.2637 6.5625L14.7363 7.08984L14.209 7.61719L14.5488 7.95703L14.8828 8.29102L15.6445 7.52344C16.6465 6.51562 16.6465 6.60938 15.6621 5.61328C15.252 5.20312 14.8887 4.86328 14.8535 4.86328C14.8184 4.86328 14.666 4.99219 14.5137 5.14453Z" />
                                <path d="M2.8125 5.625V6.09375H3.98437H5.15625V5.625V5.15625H3.98437H2.8125V5.625Z" />
                                <path
                                    d="M12.2285 6.58008C11.9766 7.20117 11.7773 7.73437 11.7773 7.76367C11.7773 7.79883 11.9648 7.89258 12.1934 7.98047L12.6094 8.13281L12.6855 7.95117C13.1602 6.80273 13.5234 5.82422 13.4883 5.78906C13.4648 5.76562 13.2715 5.68359 13.0606 5.60156L12.6738 5.45508L12.2285 6.58008Z" />
                                <path d="M17.3438 6.5625V7.03125H22.2656H27.1875V6.5625V6.09375H22.2656H17.3438V6.5625Z" />
                                <path d="M2.8125 7.96875V8.4375H3.98437H5.15625V7.96875V7.5H3.98437H2.8125V7.96875Z" />
                                <path d="M16.875 8.4375V8.90625H20.8594H24.8438V8.4375V7.96875H20.8594H16.875V8.4375Z" />
                                <path d="M2.8125 10.3125V10.7812H3.98437H5.15625V10.3125V9.84375H3.98437H2.8125V10.3125Z" />
                                <path
                                    d="M8.90625 10.3125V10.7812H14.7656H20.625V10.3125V9.84375H14.7656H8.90625V10.3125Z" />
                                <path
                                    d="M9.84375 12.1875V12.6562H17.8125H25.7813V12.1875V11.7188H17.8125H9.84375V12.1875Z" />
                                <path d="M3.75 12.6562V13.125H4.92188H6.09375V12.6562V12.1875H4.92188H3.75V12.6562Z" />
                                <path
                                    d="M8.90625 14.0625V14.5312H12.1875H15.4688V14.0625V13.5938H12.1875H8.90625V14.0625Z" />
                                <path d="M3.75 15V15.4688H4.92188H6.09375V15V14.5312H4.92188H3.75V15Z" />
                                <path
                                    d="M17.6192 15.9258C16.6348 16.9219 16.6348 16.8281 17.6367 17.8359L18.3985 18.6035L18.7324 18.2695L19.0723 17.9297L18.5449 17.4023L18.0176 16.875L18.5332 16.3594C18.8145 16.0781 19.043 15.8203 19.043 15.7852C19.043 15.709 18.4981 15.1758 18.4219 15.1758C18.3926 15.1758 18.0293 15.5156 17.6192 15.9258Z" />
                                <path
                                    d="M22.4824 15.457C22.3301 15.6094 22.207 15.7617 22.207 15.791C22.207 15.8203 22.4355 16.0781 22.7168 16.3594L23.2324 16.875L22.7051 17.4023L22.1777 17.9297L22.5176 18.2695L22.8516 18.6035L23.6133 17.8359C24.6152 16.8281 24.6152 16.9219 23.6309 15.9258C23.2207 15.5156 22.8574 15.1758 22.8223 15.1758C22.7871 15.1758 22.6348 15.3047 22.4824 15.457Z" />
                                <path
                                    d="M10.7812 15.9375V16.4062H13.3594H15.9375V15.9375V15.4688H13.3594H10.7812V15.9375Z" />
                                <path
                                    d="M20.1973 16.8926C19.9453 17.5137 19.7461 18.0469 19.7461 18.0762C19.7461 18.1524 20.5254 18.457 20.5781 18.4043C20.6074 18.375 20.9355 17.5781 21.4863 16.1895C21.5039 16.1484 21.4512 16.0899 21.3809 16.0606C21.3047 16.0313 21.1055 15.9551 20.9414 15.8906L20.6426 15.7734L20.1973 16.8926Z" />
                                <path d="M3.75 17.3438V17.8125H4.92188H6.09375V17.3438V16.875H4.92188H3.75V17.3438Z" />
                                <path d="M9.84375 17.8125V18.2812H12.4219H15V17.8125V17.3438H12.4219H9.84375V17.8125Z" />
                            </svg>
                            <h6>{{ translate('Backend Setting') }}</h6>
                        </a>
                    </li>
                    <li @if (Illuminate\Support\Facades\Route::is('frontend.setting')) class="active" @endif>
                        <a href="{{ route('frontend.setting') }}">
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_692_2419)">
                                    <path
                                        d="M0.433569 0.626943C0.363257 0.638662 0.246069 0.714834 0.169897 0.796865L0.0292725 0.949209L0.0116943 4.11913C-2.44118e-05 6.43358 0.0175537 7.33593 0.0644287 7.45897C0.216772 7.8164 -0.896508 7.79296 15 7.79296C30.8965 7.79296 29.7832 7.8164 29.9355 7.45897C29.9824 7.33593 30 6.43358 29.9883 4.11913L29.9707 0.93749L29.8066 0.779287L29.6484 0.615224L15.0996 0.609365C7.10154 0.603506 0.498022 0.615224 0.433569 0.626943ZM28.8281 4.18944V6.62108H15H1.17185V4.18944V1.7578H15H28.8281V4.18944Z" />
                                    <path
                                        d="M24.7441 2.21484C23.9414 2.50195 23.4551 3.23438 23.5078 4.08984C23.5781 5.28516 24.7324 6.08789 25.9101 5.74805L26.1914 5.67188L26.5195 5.9707C26.7949 6.22852 26.8769 6.26953 27.082 6.26953C27.5391 6.26953 27.8144 5.76563 27.5625 5.39063C27.5039 5.30859 27.3691 5.14453 27.2578 5.02734C27.0586 4.81641 27.0527 4.80469 27.1172 4.55859C27.2637 3.99023 27.2285 3.52734 27.0117 3.10547C26.6133 2.33789 25.5703 1.92188 24.7441 2.21484ZM25.7285 3.41602C26.0918 3.66211 26.1445 4.14258 25.834 4.44727C25.2715 5.00977 24.3633 4.3125 24.7676 3.62695C24.8789 3.43945 25.1484 3.28711 25.3769 3.28125C25.459 3.28125 25.6172 3.33984 25.7285 3.41602Z" />
                                    <path
                                        d="M5.63083 4.86913C5.33786 5.15624 5.40818 5.59569 5.77146 5.77148C5.94138 5.84765 6.68552 5.85937 12.0937 5.85937C18.6914 5.85937 18.5332 5.86523 18.6914 5.5664C18.8262 5.3203 18.7793 5.02733 18.5801 4.85155L18.3984 4.68749H12.1054H5.81247L5.63083 4.86913Z" />
                                    <path
                                        d="M20.0332 4.85743C19.5937 5.30274 20.0566 6.03516 20.6308 5.80079C20.8711 5.70118 20.9765 5.5254 20.9765 5.23829C20.9765 4.74024 20.3906 4.50587 20.0332 4.85743Z" />
                                    <path
                                        d="M0.46875 8.68945C0.328125 8.73633 0.228516 8.82422 0.164062 8.96484C0.0644531 9.1582 0.0585938 9.56836 0.0585938 15C0.0585938 20.4316 0.0644531 20.8418 0.164062 21.0352C0.228516 21.1758 0.328125 21.2637 0.46875 21.3105C0.767578 21.4102 18.334 21.4102 18.6328 21.3105C18.7734 21.2637 18.873 21.1758 18.9375 21.0352C19.0371 20.8418 19.043 20.4316 19.043 15C19.043 9.56836 19.0371 9.1582 18.9375 8.96484C18.873 8.82422 18.7734 8.73633 18.6328 8.68945C18.334 8.58984 0.767578 8.58984 0.46875 8.68945ZM17.8711 14.543C17.8711 17.5254 17.8477 19.2773 17.8184 19.248C17.7832 19.2129 16.7402 17.8301 15.498 16.1719C14.2559 14.5137 13.1777 13.125 13.1074 13.084C12.9492 12.9961 12.6855 12.9902 12.5449 13.0664C12.4863 13.0957 11.5898 14.2559 10.5527 15.6387L8.66016 18.1582L8.21484 17.6426C7.96289 17.3613 7.35352 16.6465 6.85547 16.0664C5.91211 14.9648 5.69531 14.7891 5.39062 14.8535C5.19141 14.9004 4.98633 15.1172 2.87109 17.5781C2.05664 18.5273 1.35352 19.3477 1.30664 19.3945C1.24219 19.4648 1.23047 18.6914 1.23047 14.6309V9.78516H9.55078H17.8711V14.543ZM13.3184 15.2051L13.7695 15.8203H12.8262H11.8828L12.0234 15.627C12.1055 15.5273 12.3105 15.2461 12.4863 15.0117C12.6562 14.7832 12.8145 14.5898 12.832 14.5898C12.8496 14.5957 13.0664 14.8711 13.3184 15.2051ZM6.72656 17.7188C7.37695 18.4746 7.91016 19.1074 7.91016 19.125C7.91016 19.1484 7.73438 19.4004 7.51758 19.6875L7.11914 20.2148H4.66992C2.78906 20.2148 2.2207 20.1973 2.23828 20.1445C2.26758 20.0566 5.4668 16.3477 5.51367 16.3477C5.53125 16.3477 6.08203 16.9629 6.72656 17.7188ZM15.791 18.5156C16.4238 19.3594 16.9512 20.0801 16.9746 20.127C16.998 20.2031 16.4531 20.2148 12.8027 20.2148C9.46289 20.2148 8.61328 20.1973 8.64844 20.1387C8.67188 20.1035 8.91797 19.7695 9.1875 19.4063L9.67969 18.75H11.127C12.3809 18.75 12.5977 18.7383 12.7734 18.6445C13.0547 18.5098 13.1602 18.2754 13.0723 17.9883C12.9609 17.6074 12.8613 17.5781 11.6602 17.5781C11.0801 17.5781 10.6055 17.5664 10.6055 17.5488C10.6055 17.5313 10.6934 17.3965 10.7988 17.2559L10.9863 16.998L12.8145 16.9922H14.6484L15.791 18.5156Z" />
                                    <path
                                        d="M7.40034 10.5996C6.86128 10.7871 6.46284 11.1563 6.19917 11.707C5.77143 12.5977 6.09956 13.7285 6.93159 14.2617C7.34174 14.5254 7.59956 14.5957 8.07417 14.5957C9.62104 14.5898 10.5937 12.9785 9.89057 11.584C9.72065 11.25 9.29292 10.8516 8.91792 10.6758C8.47846 10.4766 7.83979 10.4414 7.40034 10.5996ZM8.5019 11.7891C8.94135 12.0352 9.0937 12.6738 8.80073 13.0605C8.41987 13.5762 7.57026 13.5352 7.28315 12.9902C6.86714 12.1934 7.7226 11.3496 8.5019 11.7891Z" />
                                    <path
                                        d="M20.2148 8.68945C20.0742 8.73633 19.9746 8.82422 19.9102 8.96484C19.8105 9.1582 19.8047 9.56836 19.8047 14.9766C19.8047 18.9668 19.8223 20.8418 19.8691 20.9648C19.9043 21.0645 20.0039 21.1934 20.0859 21.252C20.2383 21.3516 20.4961 21.3574 24.9023 21.3574C29.2969 21.3574 29.5723 21.3516 29.7246 21.252C29.8184 21.1934 29.9121 21.082 29.9414 21C30.0352 20.7598 30.0176 9.14648 29.9238 8.96484C29.8828 8.88281 29.7598 8.77148 29.6484 8.71875C29.4609 8.61914 29.1152 8.61328 24.9316 8.61914C21.5977 8.61914 20.3672 8.63672 20.2148 8.68945ZM28.8281 15V20.2148L24.9199 20.2031L21.0059 20.1855L20.9883 15.0586C20.9824 12.2402 20.9883 9.89648 21.0059 9.85547C21.0234 9.80273 21.8672 9.78516 24.9316 9.78516H28.8281V15Z" />
                                    <path
                                        d="M21.9668 10.9512C21.6679 11.25 21.7734 11.7539 22.1601 11.8887C22.2773 11.9297 23.2324 11.9531 24.914 11.9531C27.7734 11.9531 27.8261 11.9473 27.9726 11.5898C28.084 11.332 28.0312 11.1035 27.8203 10.9277L27.6445 10.7812H24.8906H22.1367L21.9668 10.9512Z" />
                                    <path
                                        d="M22.0781 13.2129C21.8789 13.3242 21.7969 13.4707 21.7969 13.7227C21.7969 13.9629 21.9141 14.1387 22.1309 14.2324C22.2539 14.2793 23.0391 14.2969 25.0078 14.2852L27.7148 14.2676L27.8672 14.1094C28.084 13.8926 28.084 13.5293 27.8672 13.3125L27.7148 13.1543L24.9727 13.1426C22.6406 13.1309 22.2129 13.1426 22.0781 13.2129Z" />
                                    <path
                                        d="M22.1484 15.5332C21.791 15.6738 21.6796 16.1484 21.9374 16.4473L22.0781 16.6113L24.8671 16.6289L27.6503 16.6465L27.832 16.4824C27.9726 16.3535 28.0195 16.2598 28.0253 16.1016C28.0312 15.8555 27.8847 15.6035 27.6796 15.5273C27.4804 15.4512 22.3417 15.457 22.1484 15.5332Z" />
                                    <path
                                        d="M22.0781 17.9063C21.5801 18.1758 21.7793 18.9844 22.3418 18.9844C22.5937 18.9844 22.8808 18.7617 22.9277 18.5332C22.9746 18.2754 22.8281 17.9941 22.5937 17.9004C22.3418 17.7949 22.2773 17.7949 22.0781 17.9063Z" />
                                    <path
                                        d="M24.3749 17.877C23.9941 18.0293 23.9003 18.5215 24.1933 18.8145L24.3632 18.9844H26.0039H27.6445L27.8203 18.8379C28.0312 18.6621 28.0839 18.4336 27.9726 18.1758C27.8378 17.8418 27.6738 17.8125 26.0039 17.8184C25.1835 17.8184 24.457 17.8477 24.3749 17.877Z" />
                                    <path
                                        d="M0.369141 22.2773C0.0644531 22.4238 0.0585938 22.4824 0.0585938 25.7754C0.0585938 29.1914 0.0585938 29.168 0.433594 29.3262C0.609375 29.4023 1.32422 29.4141 5.16797 29.4141C9.36328 29.4141 9.71484 29.4082 9.90234 29.3086C10.0137 29.2559 10.1367 29.1445 10.1777 29.0625C10.2715 28.8809 10.2891 22.8281 10.2012 22.5996C10.166 22.5176 10.084 22.3945 10.0137 22.3301C9.87891 22.207 9.87305 22.207 5.19141 22.2129C2.00977 22.2129 0.457031 22.2363 0.369141 22.2773ZM9.08203 25.8105V28.2422H5.15625H1.23047V25.8105V23.3789H5.15625H9.08203V25.8105Z" />
                                    <path
                                        d="M11.3027 22.377L11.1328 22.5469V25.7988V29.0449L11.2852 29.1973C11.3672 29.2852 11.5195 29.3672 11.625 29.3848C11.7246 29.4023 15.8203 29.4082 20.7246 29.4023L29.6484 29.3848L29.8066 29.2207L29.9707 29.0625L29.9883 25.9102C29.9941 24.1816 29.9883 22.6992 29.9707 22.6289C29.9531 22.5527 29.8652 22.4238 29.7832 22.3477L29.6309 22.207H20.5547H11.4727L11.3027 22.377ZM28.8281 25.8105V28.2422H20.5664H12.3047V25.8105V23.3789H20.5664H28.8281V25.8105Z" />
                                    <path
                                        d="M14.08 25.3066C13.5937 25.6055 13.8163 26.3672 14.3906 26.3672C14.6835 26.3672 14.9413 26.1035 14.9413 25.8106C14.9413 25.5352 14.8652 25.3945 14.6484 25.2832C14.4257 25.166 14.3085 25.1719 14.08 25.3066Z" />
                                    <path
                                        d="M16.3652 25.2832C15.9902 25.4414 15.9199 25.9277 16.2246 26.2031L16.4063 26.3672H21.5391H26.6719L26.8418 26.1973C27.1465 25.8867 27.0352 25.4062 26.6191 25.248C26.5371 25.2188 24.4688 25.1953 21.5156 25.1953C17.4316 25.2012 16.5176 25.2129 16.3652 25.2832Z" />
                                </g>
                                <defs>
                                    <clipPath id="clip0_692_2419">
                                        <rect width="30" height="30" fill="white" />
                                    </clipPath>
                                </defs>

                            </svg>

                            <h6>{{ translate('Frontend Setting') }}</h6>
                        </a>
                    </li>


                    <li @if (Illuminate\Support\Facades\Route::is('license.verify')) class="active" @endif>
                        <a href="{{ route('license.verify') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor" class="bi bi-patch-check" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M10.354 6.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708 0"/>
                                <path d="m10.273 2.513-.921-.944.715-.698.622.637.89-.011a2.89 2.89 0 0 1 2.924 2.924l-.01.89.636.622a2.89 2.89 0 0 1 0 4.134l-.637.622.011.89a2.89 2.89 0 0 1-2.924 2.924l-.89-.01-.622.636a2.89 2.89 0 0 1-4.134 0l-.622-.637-.89.011a2.89 2.89 0 0 1-2.924-2.924l.01-.89-.636-.622a2.89 2.89 0 0 1 0-4.134l.637-.622-.011-.89a2.89 2.89 0 0 1 2.924-2.924l.89.01.622-.636a2.89 2.89 0 0 1 4.134 0l-.715.698a1.89 1.89 0 0 0-2.704 0l-.92.944-1.32-.016a1.89 1.89 0 0 0-1.911 1.912l.016 1.318-.944.921a1.89 1.89 0 0 0 0 2.704l.944.92-.016 1.32a1.89 1.89 0 0 0 1.912 1.911l1.318-.016.921.944a1.89 1.89 0 0 0 2.704 0l.92-.944 1.32.016a1.89 1.89 0 0 0 1.911-1.912l-.016-1.318.944-.921a1.89 1.89 0 0 0 0-2.704l-.944-.92.016-1.32a1.89 1.89 0 0 0-1.912-1.911z"/>
                              </svg>
                            <h6>{{ translate('License Verify ') }}</h6>
                        </a>
                    </li>
                @endadmin

            </ul>
        </div>



        <div class="sidebar-footer">
            <ul class="d-flex align-items-center justify-content-center flex-wrap gap-5 m-0 p-0">
                <li><a href="{{ route('logout') }}"
                        onclick="event.preventDefault();
                                            document.getElementById('logout-form').submit();"><img
                            src="{{ asset('backend/images/icons/power.svg') }}" alt=""></a></li>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                    @csrf
                </form>
            </ul>
        </div>
    </div>
</div>
