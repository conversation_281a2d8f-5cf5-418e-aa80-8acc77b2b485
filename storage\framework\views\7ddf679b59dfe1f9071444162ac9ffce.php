        <header class="header-area">
            <div class="sidebar-header">
                <a href="<?php echo e(route('backend.dashboard')); ?>" class="header-logo-icon">
                    <?php if(get_setting('front_favicon')): ?>
                        <img src="<?php echo e(asset('assets/logo/' . get_setting('front_favicon'))); ?>" alt="header-logo-icon">
                    <?php else: ?>
                        <img src="<?php echo e(asset('backend/images/bg/title-loog.svg')); ?>" alt="header-logo-icon">
                    <?php endif; ?>
                </a>
                <a href="<?php echo e(route('backend.dashboard')); ?>" class="header-logo">
                    <?php if(get_setting('admin_logo')): ?>
                        <img src="<?php echo e(asset('assets/logo/' . get_setting('admin_logo'))); ?>" alt="admin-logo"
                            height="30">
                    <?php else: ?>
                        <img src="<?php echo e(asset('backend/logo.svg')); ?>" alt="header-logo" height="30">
                    <?php endif; ?>
                </a>

                <img src="<?php echo e(asset('backend/images/icons/sidebar.svg')); ?>" class="sidebar-toggle-button"
                    alt="sidebar-toggle-button">
            </div>
            <div class="main-conent-header">
                <div class="breadcrumb-area">
                    <h5><?php echo e(translate('Dashboard')); ?></h5>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="#"><?php echo e(translate('Home')); ?></a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?php echo e($page_title ?? ''); ?></li>
                        </ol>
                    </nav>
                </div>
                <ul class="header-icons d-flex align-items-center">

                    <li><a href="<?php echo e(route('support.list')); ?>">
                            <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M0.757812 1.01954C0.480469 1.12501 0.261719 1.32814 0.121094 1.60939L0 1.84767V6.25001V10.6524L0.121094 10.8906C0.261719 11.1758 0.480469 11.375 0.769531 11.4844C0.933594 11.5469 1.07422 11.5625 1.42578 11.5625H1.86719L1.88281 12.4141C1.89844 13.375 1.91797 13.457 2.1875 13.5938C2.36719 13.6836 2.49219 13.6914 2.66797 13.6172C2.73828 13.5899 3.30078 13.1133 3.91797 12.5625L5.04297 11.5625H5.41016H5.77734L5.78906 13.836L5.80078 16.1133L5.89062 16.2774C6.02734 16.5391 6.19922 16.707 6.45312 16.836L6.69141 16.9531H10.8203H14.9453L16.1094 17.9883C17.3281 19.0703 17.3789 19.1055 17.6641 19.0469C17.8359 19.0117 17.957 18.9258 18.0352 18.7774C18.0938 18.6719 18.1055 18.5235 18.1172 17.8008L18.1328 16.9531H18.5898C19.1875 16.9531 19.3594 16.8945 19.6523 16.6055C19.9883 16.2656 20 16.211 20 14.8985V13.793L19.8984 13.7149C19.7656 13.6133 19.6484 13.6094 19.5195 13.711L19.418 13.7891L19.4062 14.836C19.3945 15.8047 19.3906 15.8867 19.3125 16.0156C19.2695 16.0899 19.1758 16.1914 19.1016 16.2383C18.9883 16.3203 18.9258 16.3281 18.5195 16.3281C17.9609 16.332 17.7969 16.3672 17.6406 16.5195L17.5195 16.6406L17.5 17.5L17.4805 18.3555L16.4258 17.4141C15.8477 16.8985 15.3281 16.4453 15.2734 16.4102C15.1875 16.3555 14.7695 16.3477 10.9766 16.3281C7.01172 16.3086 6.76953 16.3047 6.66797 16.2383C6.60547 16.1992 6.52344 16.1133 6.48438 16.043C6.41016 15.9219 6.40625 15.8281 6.40625 13.7383V11.5625H9.82422C13.1836 11.5625 13.2422 11.5625 13.457 11.4805C13.7539 11.3711 14.0273 11.0977 14.1328 10.8086C14.2148 10.5899 14.2188 10.5313 14.2188 8.76955V6.95314H16.5508C18.8281 6.95314 18.8867 6.95314 19.0469 7.03517C19.1484 7.08205 19.2539 7.1758 19.3047 7.25783L19.3945 7.40236L19.4141 10.0899C19.4336 12.6953 19.4375 12.7813 19.5078 12.836C19.6211 12.918 19.8281 12.9063 19.9219 12.8125C20 12.7344 20 12.6836 20 9.9883V7.2383L19.8789 7.00001C19.75 6.7422 19.582 6.57814 19.2969 6.43751L19.1211 6.34767L16.6719 6.33595L14.2188 6.32423V4.12111C14.2188 1.9922 14.2148 1.91017 14.1367 1.70314C14.0391 1.44142 13.7812 1.16017 13.5234 1.04298L13.3398 0.957045L8.70312 0.945326C4.09766 0.937513 4.0625 0.937513 3.98438 1.01564C3.86328 1.13673 3.87891 1.37892 4.00781 1.48048C4.11328 1.56251 4.11719 1.56251 8.625 1.56251H13.1367L13.3008 1.66017C13.6172 1.84376 13.5938 1.50392 13.5938 6.25001C13.5938 10.9258 13.6094 10.6406 13.3438 10.832L13.2227 10.918L9.02344 10.9375C6.71484 10.9492 4.79297 10.9727 4.75781 10.9922C4.71875 11.0078 4.19922 11.457 3.60547 11.9922L2.51953 12.957L2.5 12.1133C2.48047 11.1992 2.47266 11.1641 2.25 11.0313C2.15234 10.9688 2.02344 10.9531 1.55469 10.9375C0.917969 10.9141 0.839844 10.8867 0.683594 10.6211C0.605469 10.4922 0.605469 10.418 0.605469 6.25001C0.605469 1.53126 0.585938 1.87501 0.867188 1.67189C0.992188 1.58595 1.02734 1.58204 1.97656 1.56251C2.875 1.54298 2.96875 1.53517 3.04297 1.46876C3.15625 1.3672 3.15625 1.12501 3.03906 1.01564C2.95703 0.94142 2.90625 0.937513 1.95703 0.94142C1.0625 0.94142 0.9375 0.949232 0.757812 1.01954Z"
                                    fill="#1F2937" />
                                <path
                                    d="M5.40231 5.86726C4.97653 5.98054 5.05075 6.6446 5.49215 6.65632C5.78121 6.66022 5.96871 6.45319 5.92184 6.17585C5.88278 5.93757 5.65231 5.80085 5.40231 5.86726Z" />
                                <path
                                    d="M7.00391 5.86325C6.83203 5.8984 6.76563 5.957 6.71485 6.12887C6.58985 6.54684 7.09375 6.84372 7.40234 6.53122C7.53906 6.3984 7.53906 6.1445 7.40234 5.98434C7.29297 5.85543 7.19141 5.82418 7.00391 5.86325Z" />
                                <path
                                    d="M8.39455 5.97273C8.2383 6.13289 8.23439 6.35554 8.39064 6.52742C8.66799 6.83601 9.18752 6.55476 9.08595 6.15242C9.01174 5.85164 8.61721 5.75007 8.39455 5.97273Z" />
                            </svg>
                        </a></li>
                    <li><a target="_blank" href="<?php echo e(url('/')); ?>">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                                class="bi bi-globe" viewBox="0 0 16 16">
                                <path
                                    d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm7.5-6.923c-.67.204-1.335.82-1.887 1.855A7.97 7.97 0 0 0 5.145 4H7.5V1.077zM4.09 4a9.267 9.267 0 0 1 .64-1.539 6.7 6.7 0 0 1 .597-.933A7.025 7.025 0 0 0 2.255 4H4.09zm-.582 3.5c.03-.877.138-1.718.312-2.5H1.674a6.958 6.958 0 0 0-.656 2.5h2.49zM4.847 5a12.5 12.5 0 0 0-.338 2.5H7.5V5H4.847zM8.5 5v2.5h2.99a12.495 12.495 0 0 0-.337-2.5H8.5zM4.51 8.5a12.5 12.5 0 0 0 .337 2.5H7.5V8.5H4.51zm3.99 0V11h2.653c.187-.765.306-1.608.338-2.5H8.5zM5.145 12c.138.386.295.744.468 1.068.552 1.035 1.218 1.65 1.887 1.855V12H5.145zm.182 2.472a6.696 6.696 0 0 1-.597-.933A9.268 9.268 0 0 1 4.09 12H2.255a7.024 7.024 0 0 0 3.072 2.472zM3.82 11a13.652 13.652 0 0 1-.312-2.5h-2.49c.062.89.291 1.733.656 2.5H3.82zm6.853 3.472A7.024 7.024 0 0 0 13.745 12H11.91a9.27 9.27 0 0 1-.64 1.539 6.688 6.688 0 0 1-.597.933zM8.5 12v2.923c.67-.204 1.335-.82 1.887-1.855.173-.324.33-.682.468-1.068H8.5zm3.68-1h2.146c.365-.767.594-1.61.656-2.5h-2.49a13.65 13.65 0 0 1-.312 2.5zm2.802-3.5a6.959 6.959 0 0 0-.656-2.5H12.18c.174.782.282 1.623.312 2.5h2.49zM11.27 2.461c.247.464.462.98.64 1.539h1.835a7.024 7.024 0 0 0-3.072-2.472c.218.284.418.598.597.933zM10.855 4a7.966 7.966 0 0 0-.468-1.068C9.835 1.897 9.17 1.282 8.5 1.077V4h2.355z" />
                            </svg>
                        </a></li>
                    <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                        <li><a href="<?php echo e(route('backend.setting')); ?>">
                                <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.84766 0.0546837C8.42578 0.187496 8.03125 0.480465 7.81641 0.812496C7.62891 1.10937 7.53906 1.41796 7.51172 1.86328L7.48828 2.26171L7.34766 2.30859C7.26953 2.33203 7 2.4375 6.74609 2.54687L6.28906 2.74218L6.01953 2.49218C5.38281 1.90234 4.57812 1.76562 3.84766 2.125C3.61328 2.24218 3.47266 2.36328 2.90234 2.9375C2.33984 3.5039 2.21094 3.65625 2.11328 3.86718C1.76953 4.59765 1.91016 5.39062 2.49219 6.01953L2.74219 6.29297L2.60547 6.59375C2.53125 6.75781 2.42188 7.02734 2.36328 7.1914L2.25781 7.48828L1.86328 7.51172C1.02734 7.55859 0.484375 7.91015 0.148438 8.61718L0.0195312 8.88672V10V11.1133L0.148438 11.3828C0.484375 12.0898 1.02734 12.4414 1.86328 12.4883L2.25781 12.5117L2.36328 12.8086C2.42188 12.9727 2.53125 13.2422 2.60547 13.4062L2.74219 13.707L2.49219 13.9805C2.05078 14.457 1.86328 15.0078 1.94922 15.5781C2.03125 16.1211 2.14453 16.3008 2.90234 17.0625C3.66016 17.8242 3.87891 17.9687 4.39844 18.0469C5.00781 18.1406 5.55859 17.9492 6.0625 17.4687L6.28516 17.2578L6.74609 17.4531C7 17.5625 7.26953 17.668 7.34766 17.6914L7.48828 17.7383L7.51172 18.1367C7.55859 18.9727 7.90625 19.5156 8.61719 19.8516L8.88672 19.9805H10H11.1133L11.3828 19.8516C12.0898 19.5195 12.4414 18.9727 12.4883 18.1406L12.5117 17.7461L12.8086 17.6367C12.9727 17.5781 13.2422 17.4687 13.4102 17.3906L13.7109 17.2539L13.9375 17.4687C14.4414 17.9492 14.9961 18.1406 15.6094 18.0469C16.1211 17.9687 16.3438 17.8203 17.0977 17.0625C17.6602 16.4961 17.7891 16.3437 17.8867 16.1328C18.2266 15.4023 18.0898 14.6055 17.5078 13.9805L17.2578 13.7109L17.4531 13.2539C17.5625 13 17.6719 12.7305 17.6992 12.6523L17.7461 12.5117L18.1406 12.4883C18.9727 12.4414 19.5195 12.0898 19.8516 11.3828L19.9805 11.1133V10V8.88672L19.8516 8.61718C19.5195 7.91015 18.9727 7.55859 18.1406 7.51172L17.7461 7.48828L17.6992 7.34765C17.6719 7.26953 17.5625 7 17.4531 6.74609L17.2578 6.28906L17.5078 6.01953C18.0977 5.38281 18.2344 4.57812 17.875 3.84765C17.7578 3.61328 17.6367 3.47265 17.0625 2.90234C16.4961 2.33984 16.3438 2.21093 16.1328 2.11328C15.4023 1.76953 14.6094 1.91015 13.9805 2.49218L13.707 2.74218L13.4062 2.60937C13.2422 2.53125 12.9688 2.42187 12.8047 2.35937L12.5 2.24609V2.01171C12.5 1.26562 12.2148 0.675777 11.6797 0.30859C11.2734 0.0312462 11.1094 -3.8147e-06 9.98828 0.00390244C9.30859 0.00390244 8.95703 0.0195274 8.84766 0.0546837ZM10.9102 1.26562C10.9961 1.30859 11.1094 1.41015 11.1641 1.49609C11.2656 1.64843 11.2695 1.68359 11.2891 2.30078C11.3164 3.18359 11.293 3.15234 12.1289 3.41796C12.3867 3.5 12.8359 3.6914 13.125 3.83593C13.625 4.08984 13.6641 4.10546 13.875 4.08984L14.0977 4.07812L14.5469 3.64062C15.0508 3.15234 15.1797 3.08593 15.4805 3.16406C15.6172 3.20312 15.7422 3.30859 16.2188 3.78125C16.6914 4.25781 16.7969 4.38281 16.8359 4.51953C16.9141 4.82031 16.8477 4.94921 16.3594 5.45312L15.9219 5.90234L15.9102 6.125C15.8945 6.33593 15.9102 6.375 16.1641 6.875C16.3086 7.16406 16.5 7.61328 16.582 7.87109C16.8477 8.70703 16.8164 8.68359 17.6992 8.71093C18.3164 8.73047 18.3516 8.73437 18.5039 8.83593C18.7812 9.01953 18.8086 9.1289 18.8086 10C18.8086 10.8711 18.7812 10.9805 18.5039 11.1641C18.3516 11.2656 18.3164 11.2695 17.6992 11.2891C16.8164 11.3164 16.8477 11.293 16.582 12.1289C16.5 12.3867 16.3086 12.8359 16.1641 13.125C15.9102 13.625 15.8945 13.6641 15.9102 13.875L15.9219 14.0977L16.3594 14.5469C16.8477 15.0508 16.9141 15.1797 16.8359 15.4766C16.7969 15.6172 16.6914 15.7422 16.2188 16.2187C15.7422 16.6914 15.6172 16.7969 15.4805 16.8359C15.1797 16.9141 15.0508 16.8477 14.5469 16.3594L14.0977 15.9219L13.875 15.9102C13.6641 15.8945 13.625 15.9102 13.125 16.1641C12.8359 16.3086 12.3867 16.5 12.1289 16.582C11.293 16.8477 11.3164 16.8164 11.2891 17.6992C11.2695 18.3164 11.2656 18.3516 11.1641 18.5039C10.9805 18.7812 10.8711 18.8086 10 18.8086C9.12891 18.8086 9.01953 18.7812 8.83594 18.5039C8.73438 18.3516 8.73047 18.3164 8.71094 17.6992C8.68359 16.8164 8.70703 16.8477 7.87109 16.582C7.61328 16.5 7.16406 16.3086 6.875 16.1641C6.375 15.9102 6.33594 15.8945 6.125 15.9102L5.90234 15.9219L5.45312 16.3594C4.94922 16.8477 4.82031 16.9141 4.51953 16.8359C4.38281 16.7969 4.25781 16.6914 3.78125 16.2187C3.30859 15.7422 3.20312 15.6172 3.16406 15.4766C3.08594 15.1797 3.15234 15.0508 3.64062 14.5469L4.07812 14.0977L4.08984 13.875C4.10547 13.6641 4.08984 13.625 3.83594 13.125C3.69141 12.8359 3.5 12.3867 3.41797 12.1289C3.15234 11.293 3.18359 11.3164 2.30078 11.2891C1.68359 11.2695 1.64844 11.2656 1.49609 11.1641C1.21875 10.9805 1.19141 10.8711 1.19141 10C1.19141 9.1289 1.21875 9.01953 1.49609 8.83593C1.64844 8.73437 1.68359 8.73047 2.30078 8.71093C3.18359 8.68359 3.15234 8.70703 3.41797 7.87109C3.5 7.61328 3.69141 7.16406 3.83594 6.875C4.08984 6.375 4.10547 6.33593 4.08984 6.125L4.07812 5.90234L3.64062 5.45312C3.15234 4.94921 3.08594 4.82031 3.16406 4.51953C3.20312 4.38281 3.30859 4.25781 3.78125 3.78125C4.42188 3.14062 4.53125 3.07812 4.83984 3.16406C4.96875 3.19921 5.09766 3.30078 5.45312 3.64062C5.89844 4.07031 5.90234 4.07421 6.11328 4.09375C6.31641 4.10937 6.35547 4.09375 6.88281 3.83203C7.19141 3.67968 7.66016 3.48437 7.92578 3.39843C8.19531 3.3125 8.45312 3.21484 8.49609 3.1875C8.66797 3.07421 8.71094 2.89843 8.71094 2.33593C8.71094 1.72265 8.74609 1.5664 8.92578 1.38671C9.10938 1.20312 9.27734 1.17578 10.0781 1.18359C10.6562 1.1914 10.7852 1.20312 10.9102 1.26562Z" />
                                    <path
                                        d="M9.42188 5.66797C8.82422 5.7461 8.03125 6.04297 7.55469 6.3711C7.21484 6.60156 6.84766 6.94531 6.58203 7.27344C6.27344 7.66016 5.89844 8.39063 5.78125 8.84766C5.58984 9.58985 5.58984 10.418 5.78125 11.1523C5.875 11.5 6.17188 12.1602 6.37109 12.4453C7.14453 13.5742 8.37891 14.2734 9.74609 14.3594C11.1445 14.4492 12.5234 13.8359 13.418 12.7266C13.7266 12.3398 14.1016 11.6094 14.2188 11.1523C14.4102 10.4102 14.4102 9.58985 14.2188 8.84766C14.1016 8.39063 13.7266 7.66016 13.418 7.27344C12.9766 6.72656 12.5117 6.36328 11.8633 6.05469C11.1055 5.69141 10.2578 5.5586 9.42188 5.66797ZM10.7617 6.9375C11.8984 7.24219 12.7305 8.06641 13.0547 9.21094C13.1797 9.64063 13.1758 10.3633 13.0547 10.7969C12.7383 11.9023 11.9023 12.7383 10.793 13.0547C10.3594 13.1797 9.64063 13.1797 9.20313 13.0547C8.08594 12.7344 7.26563 11.9141 6.94531 10.7969C6.83203 10.3945 6.82422 9.64453 6.92969 9.25781C7.28516 7.96485 8.27734 7.07422 9.58984 6.85938C9.80859 6.82813 10.5234 6.8711 10.7617 6.9375Z" />
                                </svg>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                <div class="admin-area dropdown">
                    <?php
                        if (Session::has('locale')) {
                            $locale = Session::get('locale', Config::get('app.locale'));
                        } else {
                            $locale = get_setting('DEFAULT_LANGUAGE', 'en');
                        }
                    ?>
                    <div id="lang-change">
                        <a class="no-arrow dropdown-toggle d-flex jusify-content-start align-items-center gap-2 border-none"
                            href="javascript:void(0);" id="dropdownlanguage" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <span class="">
                                <img src="<?php echo e(asset('assets/img/flags/' . $locale . '.png')); ?>"
                                    alt="<?php echo e(translate('Language')); ?>" height="11">
                            </span><span class="text-uppercase"><?php echo e($locale); ?></span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="dropdownlanguage">
                            <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><a class="dropdown-item" href="javascript:void(0)"
                                        data-flag="<?php echo e($language->code); ?>"
                                        class="dropdown-item <?php if($locale == $language->code): ?> active <?php endif; ?>"><img
                                            src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>"
                                            class="mr-2">
                                        <span class="language"><?php echo e($language->name); ?></span></a></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
                <?php if (\Illuminate\Support\Facades\Blade::check('merchant')): ?>
                    <div class="marchant-balance">
                        <?php echo e(translate('Balance')); ?> : <?php echo e(currency_symbol() . number_format(Auth::user()->wallet_balance, 2)); ?>

                    </div>
                <?php endif; ?>
                <div class="admin-area dropdown">
                    <button class=" dropdown-toggle d-flex jusify-content-start align-items-center gap-2 border-none"
                        id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="admin-thumb">
                            <?php if(Auth::user()->image): ?>
                                <img src="<?php echo e(asset('uploads/users/' . Auth::user()->image)); ?>" alt="Profile Photo">
                            <?php else: ?>
                                <img src="<?php echo e(asset('uploads/users/user.png')); ?>" alt="Profile Photo">
                            <?php endif; ?>
                        </span>
                        <span class="admin-desig">
                            <?php if(Auth::user()->fname): ?>
                                <h6><?php echo e(Auth::user()->fname . ' ' . Auth::user()->lname); ?></h6>
                                <p><?php echo e(Auth::user()->username); ?></p>
                            <?php else: ?>
                                <h6><?php echo e(Auth::user()->username); ?></h6>
                            <?php endif; ?>
                        </span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                        <li><a class="dropdown-item " href="<?php echo e(route('backend.profile')); ?>"><i
                                    class="bi bi-person"></i><?php echo e(translate('Profile')); ?></a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('backend.shop')); ?>"><i
                                    class="bi bi-gear"></i><?php echo e(translate('My Agency')); ?></a></li>
                        <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                            <li><a class="dropdown-item" href="<?php echo e(route('frontend.setting')); ?>"><i
                                        class="bi bi-gear"></i><?php echo e(translate('Frontend Settings')); ?></a></li>
                            <li><a class="dropdown-item" href="<?php echo e(route('backend.setting')); ?>"><i
                                        class="bi bi-gear"></i><?php echo e(translate('Backend Settings')); ?></a></li>
                        <?php endif; ?>
                        <li><a class="dropdown-item" href="<?php echo e(route('logout')); ?>"
                                onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();"><i
                                    class="bi bi-box-arrow-left"></i><?php echo e(translate('Logout')); ?></a></li>
                                    
                    </ul>
                </div>
            </div>
        </header>
<?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/layouts/header.blade.php ENDPATH**/ ?>