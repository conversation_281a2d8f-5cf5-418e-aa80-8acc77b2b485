<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Installer</title>

    <?php if(!alreadyInstalled()): ?>

        <?php if(indexFile() == true): ?>
            <!-- style css -->
            <link rel="stylesheet" href="<?php echo e(asset('installer/assets/css/style.css')); ?>">
        <?php else: ?>

            <?php if(env('ASSET_URL') !== null): ?>

            <link rel="stylesheet" href="<?php echo e(asset('installer/assets/css/style.css')); ?>">
            <?php else: ?>
            <link rel="stylesheet" href="<?php echo e(asset('public/installer/assets/css/style.css')); ?>">
            <?php endif; ?>

        <?php endif; ?>

    <?php else: ?>
     <link rel="stylesheet" href="<?php echo e(asset('installer/assets/css/style.css')); ?>">
    <?php endif; ?>
</head>

<body>

    <?php echo $__env->yieldContent('content'); ?>



    <?php if(!alreadyInstalled()): ?>


      <?php if(indexFile() == true): ?>
            <script src="<?php echo e(asset('installer/assets/js/jquery-3.7.1.min.js')); ?>"></script>
            <script src="<?php echo e(asset('installer/assets/js/js-confetti.browser.js')); ?>"></script>
            <script src="<?php echo e(asset('installer/assets/js/main.js')); ?>"></script>
        <?php else: ?>

        <?php if(env('ASSET_URL') !== null): ?>
            <script src="<?php echo e(asset('installer/assets/js/jquery-3.7.1.min.js')); ?>"></script>
            <script src="<?php echo e(asset('installer/assets/js/js-confetti.browser.js')); ?>"></script>
            <script src="<?php echo e(asset('installer/assets/js/main.js')); ?>"></script>
        <?php else: ?>
        <script src="<?php echo e(asset('public/installer/assets/js/jquery-3.7.1.min.js')); ?>"></script>
        <script src="<?php echo e(asset('public/installer/assets/js/js-confetti.browser.js')); ?>"></script>
        <script src="<?php echo e(asset('public/installer/assets/js/main.js')); ?>"></script>
        <?php endif; ?>

      <?php endif; ?>


    <?php else: ?>
        <script src="<?php echo e(asset('installer/assets/js/jquery-3.7.1.min.js')); ?>"></script>
        <script src="<?php echo e(asset('installer/assets/js/js-confetti.browser.js')); ?>"></script>
        <script src="<?php echo e(asset('installer/assets/js/main.js')); ?>"></script>
    <?php endif; ?>

    <?php if(request()->is('/')): ?>
    <script>
        window.location.replace(`install`);
    </script>
    <?php endif; ?>
    <?php if(request()->is('install/final')): ?>
    <script>
       const canvas = document.getElementById('custom_canvas');
            const jsConfetti = new JSConfetti({ canvas })
            jsConfetti.addConfetti();

    </script>
    <?php endif; ?>
</body>
</html>
<?php /**PATH C:\laragon\www\triprex-app\resources\views/installer/layout.blade.php ENDPATH**/ ?>