@extends('backend.layouts.master')
@section('content')
    <div class="row mb-35 g-4">
        <div class="col-md-3">
            <div class="page-title text-md-start">
                <h4>{{ $page_title ?? '' }}</h4>
            </div>
        </div>
        <div
            class="col-md-9 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
            <form action="" method="get">
                <div class="input-with-btn d-flex jusify-content-start align-items-strech">
                    <input type="text" name="search" placeholder="{{ translate('Search your blog') }}...">
                    <button type="submit"><i class="bi bi-search"></i></button>
                </div>
            </form>
            <a href="{{ route('blog.create') }}" class="eg-btn btn--primary back-btn"><img
                    src="{{ asset('backend/images/icons/add-icon.svg') }}" alt="{{ translate('Add New') }}">
                {{ translate('Add New') }}</a>
            <a href="{{ route('blog.category.list') }}" class="eg-btn btn--primary back-btn"><img
                    src="{{ asset('backend/images/icons/add-icon.svg') }}" alt="{{ translate('Category') }}">
                {{ translate('Category') }}</a>
        </div>
    </div>
    @php
        $locale = get_setting('DEFAULT_LANGUAGE', 'en');
    @endphp
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
               <form id="bulkDeleteForm" method="POST" action="{{ route('blogs.bulk-delete') }}">
    @csrf
    @method('DELETE')

    <button
        type="submit"
        id="bulkDeleteBtn"
        style="display: none;"
        class="eg-btn red-light--btn mb-3"
    >
        {{ translate('Delete Selected') }}
    </button>

    <table class="eg-table table customer-table">
        <thead>
            <tr>
                <th><input type="checkbox" id="select-all"></th>
                <th>{{ translate('S.N') }}</th>
                <th>{{ translate('Image') }}</th>
                <th>{{ translate('Title') }}</th>
                <th>{{ translate('Category') }}</th>
                <th>{{ translate('Date') }}</th>
                <th>{{ translate('Published') }}</th>
                <th>
                    @foreach (\App\Models\Language::all() as $language)
                        <img src="{{ asset('assets/img/flags/' . $language->code . '.png') }}" class="mr-2">
                    @endforeach
                </th>
                <th>{{ translate('Option') }}</th>
            </tr>
        </thead>
        <tbody>
            @if ($blogs->count() > 0)
                @foreach ($blogs as $key => $blog)
                    <tr>
                        <td>
                            <input
                                type="checkbox"
                                name="ids[]"
                                class="select-row"
                                value="{{ $blog->id }}"
                            >
                        </td>
                        <td data-label="S.N">
                            {{ ($blogs->currentPage() - 1) * $blogs->perPage() + $key + 1 }}
                        </td>
                        <td data-label="Image">
                            @if($blog->image)
                                <img src="{{ asset('uploads/blog/' . $blog->image) }}" alt="{{ $blog->title }}">
                            @endif
                        </td>
                        <td data-label="Title">{{ $blog->getTranslation('title', $lang) }}</td>
                        <td data-label="Category">{{ $blog->blog_categories->getTranslation('name', $lang) }}</td>
                        <td data-label="Date">{{ dateFormat($blog->created_at) }}</td>
                        <td data-label="Published">
                            <div class="form-check form-switch">
                                <input
                                    class="form-check-input flexSwitchCheckStatus"
                                    type="checkbox"
                                    data-activations-status="{{ $blog->status }}"
                                    data-id="{{ $blog->id }}"
                                    data-type="blogs"
                                    id="flexSwitchCheckStatus{{ $blog->id }}"
                                    {{ $blog->status == 1 ? 'checked' : '' }}
                                >
                            </div>
                        </td>
                        <td data-label="Language">
                            @foreach(\App\Models\Language::all() as $language)
                                @if($locale == $language->code)
                                    <i class="text-success bi bi-check-lg"></i>
                                @else
                                    <a href="{{ route('blog.edit', ['id'=>$blog->id,'lang'=>$language->code]) }}">
                                        <i class="text--primary bi bi-pencil-square"></i>
                                    </a>
                                @endif
                            @endforeach
                        </td>
                        <td data-label="Option">
                            <div class="d-flex gap-2 justify-content-end">
                                <a target="_blank" class="eg-btn account--btn" href="{{ url('blog/' . $blog->slug) }}">
                                    <i class="bi bi-box-arrow-up-right"></i>
                                </a>
                                <a class="eg-btn add--btn" href="{{ route('blog.edit', ['id'=>$blog->id,'lang'=>get_setting('DEFAULT_LANGUAGE','en')]) }}">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <form method="POST" action="{{ route('blog.delete', $blog->id) }}">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="eg-btn delete--btn show_confirm" title="{{ translate('Delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="9" class="text-center">
                        <h5 class="data-not-found">{{ translate('No Data Found') }}</h5>
                    </td>
                </tr>
            @endif
        </tbody>
    </table>
</form>
            </div>
        </div>
    </div>
    @push('footer')
        <div class="d-flex justify-content-center custom-pagination">
            {!! $blogs->links() !!}
        </div>
    @endpush
@endsection
