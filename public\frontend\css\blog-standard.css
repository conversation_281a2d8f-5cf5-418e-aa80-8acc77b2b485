/* Blog Standard Layout Styles - Modern Design */

/* Override any conflicting styles */
.blog-sidebar-modern * {
    box-sizing: border-box;
}

.blog-sidebar-modern .sidebar-widget * {
    box-sizing: border-box;
}

/* Reset any conflicting list styles */
.blog-sidebar-modern ul {
    list-style: none !important;
    padding-left: 0 !important;
}

.blog-sidebar-modern li {
    list-style: none !important;
}

/* Ensure proper form control styles */
.blog-sidebar-modern input[type="text"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

.blog-standard-wrapper {
    max-width: 100%;
}

.blog-card-modern {
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.4s ease;
    border: 1px solid rgba(0, 0, 0, 0.03);
    margin-bottom: 40px;
}

.blog-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.blog-image-container {
    position: relative;
    height: 300px;
    overflow: hidden;
    border-radius: 20px 20px 0 0;
    width: 100%;
}

.blog-featured-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.blog-card-modern:hover .blog-featured-image {
    transform: scale(1.08);
}

.blog-image-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    font-weight: 500;
}

.blog-image-placeholder i {
    font-size: 56px;
    margin-bottom: 15px;
    opacity: 0.8;
}

.date-badge {
    position: absolute;
    top: 25px;
    left: 25px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 12px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    min-width: 65px;
}

.date-day {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 2px;
}

.date-month {
    font-size: 12px;
    font-weight: 600;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.blog-content-area {
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.blog-stats {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 13px;
    color: #7f8c8d;
    font-weight: 500;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-item i {
    color: #3498db;
    font-size: 12px;
}

.stat-separator {
    color: #bdc3c7;
    margin: 0 4px;
}

.blog-title-modern {
    font-size: 28px;
    font-weight: 900;
    line-height: 1.3;
    margin-bottom: 20px;
    color: #6C2EB9;
}

.blog-title-modern a {
    color: #6C2EB9;
    text-decoration: none;
    transition: all 0.3s ease;
}

.blog-title-modern a:hover {
    color: #5a2496;
}

.blog-excerpt-modern {
    color: #7f8c8d;
    line-height: 1.7;
    margin-bottom: 25px;
    font-size: 16px;
    font-weight: 400;
}

.blog-read-more {
    margin-top: auto;
}

.read-more-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #6C2EB9;
    text-decoration: none;
    font-weight: 800;
    font-size: 15px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.read-more-link:hover {
    color: #5a2496;
    transform: translateX(5px);
}

.read-more-link i {
    transition: transform 0.3s ease;
    font-size: 14px;
}

.read-more-link:hover i {
    transform: translateX(5px);
}

.blog-pagination {
    margin-top: 60px;
    text-align: center;
}

.blog-pagination .pagination {
    display: inline-flex;
    gap: 12px;
    padding: 0;
    margin: 0;
}

.blog-pagination .page-item .page-link {
    border: 2px solid #e9ecef;
    color: #666;
    padding: 12px 18px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    background: white;
}

.blog-pagination .page-item.active .page-link {
    background: #3498db;
    border-color: #3498db;
    color: white;
}

.blog-pagination .page-item .page-link:hover {
    background: #3498db;
    border-color: #3498db;
    color: white;
    transform: translateY(-3px);
}

.no-blogs-found {
    text-align: center;
    padding: 100px 20px;
}

.empty-state {
    max-width: 450px;
    margin: 0 auto;
}

.empty-state i {
    font-size: 72px;
    color: #bdc3c7;
    margin-bottom: 25px;
}

.empty-state h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 28px;
    font-weight: 600;
}

.empty-state p {
    color: #7f8c8d;
    font-size: 16px;
    line-height: 1.6;
}

/* Sidebar Styles */
.blog-sidebar-modern {
    padding-left: 30px;
}

.blog-sidebar-modern .sidebar-widget {
    background: #fff !important;
    border-radius: 16px !important;
    padding: 30px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    margin-bottom: 30px !important;
    transition: all 0.3s ease !important;
}

.blog-sidebar-modern .sidebar-widget:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-2px) !important;
}

.blog-sidebar-modern .widget-title {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: #2c3e50 !important;
    margin-bottom: 25px !important;
    padding-bottom: 12px !important;
    border-bottom: 3px solid #6C2EB9 !important;
    position: relative !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    display: block !important;
    width: 100% !important;
}

.blog-sidebar-modern .widget-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #6C2EB9, #8e44ad);
    border-radius: 2px;
}

/* Search Widget */
.blog-sidebar-modern .search-widget .search-input-group {
    position: relative;
}

.blog-sidebar-modern .search-widget .search-input-group input {
    width: 100% !important;
    padding: 12px 50px 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 25px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    background: #fff !important;
    color: #333 !important;
    height: auto !important;
    box-shadow: none !important;
}

.blog-sidebar-modern .search-widget .search-input-group input::placeholder {
    color: #999 !important;
    opacity: 1 !important;
}

.blog-sidebar-modern .search-widget .search-input-group input:focus {
    outline: none !important;
    border-color: #6C2EB9 !important;
    box-shadow: 0 0 0 3px rgba(108, 46, 185, 0.1) !important;
}

.blog-sidebar-modern .search-widget .search-btn {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: #6C2EB9 !important;
    border: none !important;
    color: white !important;
    width: 35px !important;
    height: 35px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.blog-sidebar-modern .search-widget .search-btn:hover {
    background: #5a2496 !important;
    transform: translateY(-50%) scale(1.05) !important;
}

.blog-sidebar-modern .search-widget .search-btn i {
    font-size: 14px !important;
    color: white !important;
}

/* Fix for form-control conflicts */
.blog-sidebar-modern .search-widget .form-control {
    width: 100% !important;
    padding: 12px 50px 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 25px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    background: #fff !important;
    color: #333 !important;
    height: auto !important;
    box-shadow: none !important;
    line-height: normal !important;
    min-height: 45px !important;
}

.blog-sidebar-modern .search-widget .form-control:focus {
    outline: none !important;
    border-color: #6C2EB9 !important;
    box-shadow: 0 0 0 3px rgba(108, 46, 185, 0.1) !important;
}

.blog-sidebar-modern .search-widget .search-form {
    position: relative !important;
}

/* Additional Search Form Fixes */
.blog-sidebar-modern .search-widget input[type="text"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    width: 100% !important;
    padding: 12px 50px 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 25px !important;
    font-size: 14px !important;
    background: #fff !important;
    color: #333 !important;
    height: 45px !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    line-height: normal !important;
}

.blog-sidebar-modern .search-widget input[type="text"]:focus {
    outline: none !important;
    border-color: #6C2EB9 !important;
    box-shadow: 0 0 0 3px rgba(108, 46, 185, 0.1) !important;
}

.blog-sidebar-modern .search-widget button[type="submit"] {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: #6C2EB9 !important;
    border: none !important;
    color: white !important;
    width: 35px !important;
    height: 35px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    z-index: 10 !important;
}

.blog-sidebar-modern .search-widget button[type="submit"]:hover {
    background: #5a2496 !important;
}

/* Categories Widget */
.blog-sidebar-modern .categories-widget .category-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.blog-sidebar-modern .categories-widget .category-list li {
    margin-bottom: 15px !important;
}

.blog-sidebar-modern .categories-widget .category-list li:last-child {
    margin-bottom: 0 !important;
}

.blog-sidebar-modern .categories-widget .category-list li a {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    color: #333 !important;
    text-decoration: none !important;
    padding: 12px 15px !important;
    border-radius: 8px !important;
    background: #f8f9fa !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    border: 1px solid transparent !important;
}

.blog-sidebar-modern .categories-widget .category-list li a:hover {
    color: #6C2EB9 !important;
    background: rgba(108, 46, 185, 0.05) !important;
    border-color: rgba(108, 46, 185, 0.1) !important;
    transform: translateX(5px) !important;
}

.blog-sidebar-modern .categories-widget .post-count {
    background: #6C2EB9 !important;
    color: white !important;
    padding: 4px 10px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    min-width: 25px !important;
    text-align: center !important;
}

/* Recent Posts Widget */
.blog-sidebar-modern .recent-posts-widget .recent-post-item {
    display: flex !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
    padding: 15px !important;
    border-radius: 10px !important;
    background: #f8f9fa !important;
    transition: all 0.3s ease !important;
    border: 1px solid transparent !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-item:hover {
    background: rgba(108, 46, 185, 0.05) !important;
    border-color: rgba(108, 46, 185, 0.1) !important;
    transform: translateY(-2px) !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-item:last-child {
    margin-bottom: 0 !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-image {
    width: 70px !important;
    height: 70px !important;
    border-radius: 10px !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
    position: relative !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-image img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.3s ease !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-item:hover .recent-post-image img {
    transform: scale(1.1) !important;
}

.blog-sidebar-modern .recent-posts-widget .post-image-placeholder {
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 20px !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-content {
    flex: 1 !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-content h6 {
    margin-bottom: 8px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    font-weight: 600 !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-content h6 a {
    color: #2c3e50 !important;
    text-decoration: none !important;
    transition: color 0.3s ease !important;
}

.blog-sidebar-modern .recent-posts-widget .recent-post-content h6 a:hover {
    color: #6C2EB9 !important;
}

.blog-sidebar-modern .recent-posts-widget .post-date {
    font-size: 12px !important;
    color: #7f8c8d !important;
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
    font-weight: 500 !important;
}

.blog-sidebar-modern .recent-posts-widget .post-date i {
    color: #6C2EB9 !important;
}

/* Tags Widget */
.blog-sidebar-modern .tags-widget .tag-cloud {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 8px !important;
}

.blog-sidebar-modern .tags-widget .tag-item {
    background: rgba(108, 46, 185, 0.1) !important;
    color: #6C2EB9 !important;
    padding: 6px 12px !important;
    border-radius: 15px !important;
    font-size: 12px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
}

.blog-sidebar-modern .tags-widget .tag-item:hover {
    background: #6C2EB9 !important;
    color: white !important;
    transform: translateY(-1px) !important;
}

/* Sidebar Area Styles - Updated Structure */
.sidebar-area {
    padding-left: 30px;
}

.sidebar-area .single-widget {
    background: #fff !important;
    border-radius: 16px !important;
    padding: 30px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    margin-bottom: 30px !important;
    transition: all 0.3s ease !important;
}

.sidebar-area .single-widget:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-2px) !important;
}

.sidebar-area .widget-title {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: #2c3e50 !important;
    margin-bottom: 25px !important;
    padding-bottom: 12px !important;
    border-bottom: 3px solid #6C2EB9 !important;
    position: relative !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    display: block !important;
    width: 100% !important;
}

.sidebar-area .widget-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #6C2EB9, #8e44ad);
    border-radius: 2px;
}

/* Search Box Styles */
.sidebar-area .search-box {
    position: relative !important;
}

.sidebar-area .search-box input[type="text"] {
    width: 100% !important;
    padding: 12px 50px 12px 15px !important;
    border: 2px solid #e9ecef !important;
    border-radius: 25px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    background: #fff !important;
    color: #333 !important;
    height: 45px !important;
    box-sizing: border-box !important;
    line-height: normal !important;
}

.sidebar-area .search-box input[type="text"]:focus {
    outline: none !important;
    border-color: #6C2EB9 !important;
    box-shadow: 0 0 0 3px rgba(108, 46, 185, 0.1) !important;
}

.sidebar-area .search-box button[type="submit"] {
    position: absolute !important;
    right: 5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: #6C2EB9 !important;
    border: none !important;
    color: white !important;
    width: 35px !important;
    height: 35px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    z-index: 10 !important;
    transition: all 0.3s ease !important;
}

.sidebar-area .search-box button[type="submit"]:hover {
    background: #5a2496 !important;
    transform: translateY(-50%) scale(1.05) !important;
}

.sidebar-area .search-box button[type="submit"] i {
    font-size: 14px !important;
    color: white !important;
}

/* Category List Styles */
.sidebar-area .category-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.sidebar-area .category-list li {
    margin-bottom: 15px !important;
}

.sidebar-area .category-list li:last-child {
    margin-bottom: 0 !important;
}

.sidebar-area .category-list li a {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    color: #333 !important;
    text-decoration: none !important;
    padding: 12px 15px !important;
    border-radius: 8px !important;
    background: #f8f9fa !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    border: 1px solid transparent !important;
}

.sidebar-area .category-list li a:hover {
    color: #6C2EB9 !important;
    background: rgba(108, 46, 185, 0.05) !important;
    border-color: rgba(108, 46, 185, 0.1) !important;
    transform: translateX(5px) !important;
}

.sidebar-area .category-list li a span {
    background: #6C2EB9 !important;
    color: white !important;
    padding: 4px 10px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    min-width: 25px !important;
    text-align: center !important;
}

/* Recent Post Widget Styles */
.sidebar-area .recent-post-widget {
    display: flex !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
    padding: 15px !important;
    border-radius: 10px !important;
    background: #f8f9fa !important;
    transition: all 0.3s ease !important;
    border: 1px solid transparent !important;
}

.sidebar-area .recent-post-widget:hover {
    background: rgba(108, 46, 185, 0.05) !important;
    border-color: rgba(108, 46, 185, 0.1) !important;
    transform: translateY(-2px) !important;
}

.sidebar-area .recent-post-widget:last-child {
    margin-bottom: 0 !important;
}

.sidebar-area .recent-post-img {
    width: 70px !important;
    height: 70px !important;
    border-radius: 10px !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
    position: relative !important;
}

.sidebar-area .recent-post-img img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.3s ease !important;
}

.sidebar-area .recent-post-widget:hover .recent-post-img img {
    transform: scale(1.1) !important;
}

.sidebar-area .recent-post-content {
    flex: 1 !important;
}

.sidebar-area .recent-post-content a {
    font-size: 12px !important;
    color: #7f8c8d !important;
    text-decoration: none !important;
    margin-bottom: 8px !important;
    display: block !important;
}

.sidebar-area .recent-post-content h6 {
    margin-bottom: 0 !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    font-weight: 600 !important;
}

.sidebar-area .recent-post-content h6 a {
    color: #2c3e50 !important;
    text-decoration: none !important;
    transition: color 0.3s ease !important;
    font-size: 14px !important;
}

.sidebar-area .recent-post-content h6 a:hover {
    color: #6C2EB9 !important;
}

/* Responsive Design for Vertical Cards */
@media (max-width: 991px) {
    .blog-image-container {
        height: 250px;
    }
    
    .blog-content-area {
        padding: 25px;
    }
    
    .blog-title-modern {
        font-size: 24px;
    }
    
    .blog-stats {
        gap: 6px;
    }

    .blog-sidebar-modern {
        padding-left: 0;
        margin-top: 50px;
    }
    
    .sidebar-area {
        margin-top: 50px;
    }
}

@media (max-width: 767px) {
    .blog-card-modern {
        margin-bottom: 30px;
        border-radius: 16px;
    }
    
    .blog-image-container {
        height: 220px;
        border-radius: 16px 16px 0 0;
    }
    
    .blog-content-area {
        padding: 20px;
    }
    
    .blog-title-modern {
        font-size: 20px;
        margin-bottom: 15px;
    }
    
    .blog-stats {
        flex-wrap: wrap;
        gap: 4px;
        font-size: 12px;
    }
    
    .blog-excerpt-modern {
        font-size: 14px;
        margin-bottom: 20px;
    }
    
    .read-more-link {
        font-size: 14px;
    }
    
    .date-badge {
        top: 20px;
        left: 20px;
        padding: 10px;
        min-width: 55px;
    }
    
    .date-day {
        font-size: 20px;
    }
    
    .date-month {
        font-size: 11px;
    }

    .blog-sidebar-modern .sidebar-widget {
        padding: 20px;
        margin-bottom: 30px;
    }

    .blog-sidebar-modern .recent-posts-widget .recent-post-item {
        gap: 10px;
    }

    .blog-sidebar-modern .recent-posts-widget .recent-post-image {
        width: 50px;
        height: 50px;
    }
    
    .sidebar-area .single-widget {
        padding: 20px;
        margin-bottom: 20px;
    }
}

@media (max-width: 575px) {
    .blog-image-container {
        height: 200px;
    }
    
    .blog-content-area {
        padding: 15px;
    }
    
    .blog-title-modern {
        font-size: 18px;
    }
    
    .blog-stats {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .stat-separator {
        display: none;
    }
    
    .date-badge {
        top: 15px;
        left: 15px;
        padding: 8px;
        min-width: 50px;
    }
    
    .date-day {
        font-size: 18px;
    }
    
    .date-month {
        font-size: 10px;
    }
}