@extends('frontend.template-' . selectedTheme() . '.customer.partials.master')
@section('master')
    <div class="main-content">
        <div class="row">
            <div class="col-xl-12 mb-30">
                <div class="page-title">
                    <h4>{{ $title }}:</h4>
                    <div class="booking-options">
                        <ul class="nav nav-pills" id="pills-tab2" role="tablist">
                            <li class="nav-item" role="presentation">
                                <form action="" method="GET">
                                    <input type="hidden" name="type">
                                    <button class="nav-link {{ Request::get('type') == null ? 'active' : '' }}"
                                        type="submit">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23"
                                            fill="currentColor" class="bi bi-border-all" viewBox="0 0 23 23">
                                            <path
                                                d="M0 0h16v16H0zm1 1v6.5h6.5V1zm7.5 0v6.5H15V1zM15 8.5H8.5V15H15zM7.5 15V8.5H1V15z" />
                                        </svg>
                                        {{ translate('All Booking') }}
                                    </button>
                                </form>
                            </li>
                            <li class="nav-item" role="presentation">
                                <form action="" method="GET">
                                    <input type="hidden" name="type" value="tour">
                                    <button class="nav-link {{ Request::get('type') == 'tour' ? 'active' : '' }}"
                                        type="submit">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23"
                                            viewBox="0 0 23 23">
                                            <g>
                                                <path
                                                    d="M5.64903 6.91357C2.53954 6.91357 0.00976562 9.44335 0.00976562 12.5528C0.00976562 14.4834 0.928148 16.5592 2.73944 18.7225C2.9952 19.0275 3.26021 19.3246 3.53411 19.6134C2.36462 19.9327 1.66244 20.5062 1.66244 21.1756C1.66244 22.3605 3.71636 23 5.64885 23C7.58139 23 9.63527 22.3605 9.63527 21.1756C9.63527 20.5067 8.93332 19.9331 7.76458 19.6137C8.2315 19.1208 8.73391 18.5408 9.21062 17.894C9.23746 17.8583 9.25696 17.8178 9.26801 17.7746C9.27906 17.7314 9.28143 17.6864 9.27499 17.6423C9.26854 17.5981 9.25341 17.5557 9.23047 17.5175C9.20753 17.4792 9.17723 17.4459 9.14132 17.4195C9.10542 17.393 9.06463 17.3739 9.02131 17.3634C8.97799 17.3528 8.933 17.3509 8.88894 17.3578C8.84489 17.3647 8.80264 17.3803 8.76465 17.4037C8.72666 17.427 8.69367 17.4577 8.6676 17.4938C8.09206 18.2749 7.46909 18.9613 6.92549 19.5065C6.86236 19.5385 6.8109 19.5896 6.77841 19.6525C6.29146 20.131 5.88168 20.486 5.64917 20.68C5.41921 20.4879 5.01568 20.1377 4.53497 19.6654C4.50093 19.5907 4.44083 19.531 4.36593 19.4974C2.83827 17.9609 0.684268 15.3062 0.684268 12.5529C0.684268 9.81539 2.91145 7.58821 5.64899 7.58821C8.38653 7.58821 10.6137 9.81539 10.6137 12.5529C10.6137 13.7484 10.2064 15.0395 9.40297 16.3902C9.38033 16.4283 9.36541 16.4705 9.35906 16.5143C9.35271 16.5581 9.35506 16.6028 9.36597 16.6457C9.37687 16.6887 9.39613 16.729 9.42264 16.7645C9.44914 16.8 9.48238 16.8299 9.52045 16.8525C9.55851 16.8752 9.60067 16.8901 9.6445 16.8965C9.68834 16.9028 9.73299 16.9005 9.77592 16.8895C9.81885 16.8786 9.85921 16.8594 9.89469 16.8329C9.93018 16.8064 9.9601 16.7731 9.98274 16.7351C10.849 15.2785 11.2883 13.8715 11.2883 12.5529C11.2883 9.44335 8.75852 6.91357 5.64903 6.91357ZM5.44262 21.3815C5.50169 21.4273 5.57431 21.4521 5.64903 21.452C5.72376 21.4521 5.79638 21.4273 5.85545 21.3815C5.89682 21.3495 6.46041 20.91 7.21164 20.1755C8.34915 20.4074 8.96081 20.8541 8.96081 21.1756C8.96081 21.4157 8.63171 21.699 8.10195 21.9148C7.4522 22.1796 6.58103 22.3255 5.6489 22.3255C3.62714 22.3255 2.33699 21.6444 2.33699 21.1756C2.33699 20.8536 2.94882 20.4072 4.08697 20.1752C4.84237 20.9141 5.40677 21.3538 5.44262 21.3815Z">
                                                </path>
                                                <path
                                                    d="M5.64915 10.1009C5.1157 10.1009 4.60844 10.2693 4.18222 10.5879C4.1106 10.6415 4.06318 10.7213 4.05039 10.8098C4.0376 10.8984 4.06049 10.9883 4.11403 11.06C4.16761 11.1316 4.24744 11.179 4.33596 11.1918C4.42448 11.2046 4.51446 11.1817 4.58612 11.1282C4.89477 10.8974 5.26237 10.7754 5.6491 10.7754C6.62916 10.7754 7.42648 11.5727 7.42648 12.5528C7.42648 13.5329 6.62916 14.3302 5.6491 14.3302C4.66904 14.3302 3.87172 13.5329 3.87172 12.5528C3.87172 12.3856 3.89486 12.2202 3.94045 12.0615C3.96513 11.9755 3.95465 11.8833 3.91132 11.805C3.86799 11.7268 3.79534 11.6689 3.70938 11.6442C3.6234 11.6196 3.53117 11.6301 3.45294 11.6735C3.37471 11.7168 3.31687 11.7894 3.29214 11.8754C3.22906 12.0957 3.19713 12.3237 3.19727 12.5528C3.19727 13.9048 4.29718 15.0048 5.64919 15.0048C7.00116 15.0048 8.10112 13.9048 8.10112 12.5528C8.10107 11.2008 7.00112 10.1009 5.64915 10.1009ZM20.3471 9.7984C20.5276 9.60501 20.7029 9.40685 20.8728 9.20413C22.2769 7.52706 22.9889 5.91486 22.9889 4.41218C22.9889 1.97935 21.0096 0 18.5767 0C16.7995 0 15.2037 1.05773 14.5114 2.69468C14.4942 2.73547 14.4851 2.77926 14.4848 2.82354C14.4844 2.86783 14.4928 2.91175 14.5095 2.95279C14.5261 2.99383 14.5507 3.0312 14.5818 3.06275C14.6128 3.0943 14.6498 3.11942 14.6906 3.13667C14.773 3.1715 14.8658 3.17218 14.9487 3.13858C15.0316 3.10498 15.0977 3.03984 15.1326 2.95748C15.7192 1.57065 17.0711 0.674502 18.5767 0.674502C20.6377 0.674502 22.3144 2.35126 22.3144 4.41223C22.3144 7.19931 19.3883 9.86952 18.5766 10.5564C18.3977 10.4053 18.1161 10.1578 17.786 9.83421C17.7533 9.76971 17.7007 9.71745 17.636 9.68516C16.4715 8.51265 14.839 6.49714 14.839 4.41223C14.839 4.32278 14.8035 4.23699 14.7403 4.17374C14.677 4.11049 14.5912 4.07495 14.5018 4.07495C14.4123 4.07495 14.3265 4.11049 14.2633 4.17374C14.2 4.23699 14.1645 4.32278 14.1645 4.41223C14.1645 5.91486 14.8765 7.52706 16.2807 9.20418C16.4505 9.40677 16.6257 9.60484 16.806 9.79818C15.9366 10.0594 15.4347 10.504 15.4347 11.0395C15.4347 11.491 15.801 11.889 16.4662 12.16C17.0354 12.3919 17.7849 12.5197 18.5767 12.5197C19.3684 12.5197 20.1179 12.3919 20.6871 12.16C21.3522 11.8889 21.7185 11.491 21.7185 11.0394C21.7185 10.505 21.2162 10.0599 20.3471 9.7984ZM18.5766 11.8451C16.9802 11.8451 16.1093 11.3129 16.1093 11.0394C16.1093 10.8612 16.5054 10.5352 17.3514 10.3553C17.9206 10.9091 18.3431 11.2382 18.3703 11.2594C18.4294 11.3052 18.502 11.33 18.5768 11.3299C18.6515 11.33 18.7241 11.3052 18.7832 11.2594C18.8104 11.2383 19.2328 10.9092 19.8021 10.3554C20.1789 10.4358 20.5054 10.5535 20.7339 10.6926C20.9281 10.8109 21.0441 10.9405 21.0441 11.0394C21.044 11.3129 20.1731 11.8451 18.5766 11.8451Z">
                                                </path>
                                                <path
                                                    d="M18.576 2.44968C17.4939 2.44968 16.6135 3.33006 16.6135 4.41227C16.6135 5.49439 17.4939 6.37477 18.576 6.37477C19.6582 6.37477 20.5386 5.49444 20.5386 4.41227C20.5386 3.3301 19.6582 2.44968 18.576 2.44968ZM18.5761 5.70022C17.8658 5.70022 17.288 5.12244 17.288 4.41222C17.288 3.70201 17.8658 3.12418 18.5761 3.12418C19.2863 3.12418 19.8641 3.70196 19.8641 4.41222C19.8641 5.12244 19.2863 5.70022 18.5761 5.70022ZM14.2011 10.7021H14.0471C13.935 10.7021 13.8223 10.7096 13.7121 10.7242C13.6241 10.7369 13.5446 10.7837 13.491 10.8545C13.4374 10.9254 13.4138 11.0145 13.4256 11.1026C13.4373 11.1907 13.4833 11.2706 13.5536 11.325C13.6239 11.3794 13.7128 11.4038 13.8011 11.3929C13.8826 11.3822 13.9648 11.3768 14.0471 11.3768H14.2011V11.3767C14.3874 11.3767 14.5384 11.2258 14.5384 11.0395C14.5384 10.8531 14.3874 10.7021 14.2011 10.7021ZM13.0861 20.8211H13.0845L12.6391 20.8231C12.5496 20.8233 12.4639 20.859 12.4008 20.9224C12.3377 20.9858 12.3023 21.0717 12.3025 21.1611C12.3027 21.2506 12.3384 21.3363 12.4018 21.3994C12.4652 21.4625 12.5511 21.4979 12.6405 21.4977H12.6421L13.0876 21.4957C13.177 21.4952 13.2626 21.4593 13.3256 21.3958C13.3885 21.3322 13.4237 21.2463 13.4233 21.1569C13.4229 21.0677 13.3872 20.9823 13.324 20.9193C13.2608 20.8564 13.1753 20.8211 13.0861 20.8211ZM14.6013 15.0708H14.1558C14.0664 15.0708 13.9806 15.1063 13.9174 15.1696C13.8541 15.2328 13.8186 15.3186 13.8186 15.4081C13.8186 15.4975 13.8541 15.5833 13.9174 15.6466C13.9806 15.7098 14.0664 15.7453 14.1558 15.7453H14.6013C14.6456 15.7453 14.6894 15.7366 14.7304 15.7197C14.7713 15.7027 14.8085 15.6779 14.8398 15.6466C14.8711 15.6152 14.896 15.5781 14.9129 15.5371C14.9299 15.4962 14.9386 15.4524 14.9386 15.4081C14.9386 15.3638 14.9299 15.3199 14.9129 15.279C14.896 15.2381 14.8711 15.2009 14.8398 15.1696C14.8085 15.1382 14.7713 15.1134 14.7304 15.0965C14.6894 15.0795 14.6456 15.0708 14.6013 15.0708ZM12.8142 11.3893C12.7535 11.3236 12.6693 11.2846 12.5799 11.281C12.4905 11.2774 12.4034 11.3095 12.3376 11.3701C12.2116 11.4863 12.0977 11.615 11.9977 11.7543C11.9477 11.827 11.9282 11.9164 11.9434 12.0034C11.9586 12.0903 12.0072 12.1679 12.0789 12.2194C12.1506 12.2709 12.2396 12.2922 12.3269 12.2788C12.4141 12.2654 12.4927 12.2184 12.5456 12.1478C12.619 12.0456 12.7026 11.9512 12.795 11.8659C12.8607 11.8052 12.8997 11.721 12.9033 11.6316C12.9069 11.5422 12.8749 11.4551 12.8142 11.3893ZM14.8679 20.8129H14.8664L14.421 20.815C14.3325 20.8166 14.2482 20.8529 14.1862 20.9162C14.1243 20.9794 14.0897 21.0644 14.0899 21.153C14.09 21.2415 14.125 21.3264 14.1873 21.3893C14.2495 21.4523 14.3339 21.4883 14.4225 21.4895H14.424L14.8694 21.4875C14.9579 21.4858 15.0423 21.4495 15.1042 21.3863C15.1661 21.323 15.2007 21.238 15.2006 21.1495C15.2004 21.061 15.1654 20.9761 15.1032 20.9131C15.0409 20.8501 14.9565 20.8142 14.8679 20.8129ZM11.3043 20.8291H11.3027L10.8572 20.8312C10.7678 20.8316 10.6822 20.8675 10.6192 20.9311C10.5563 20.9946 10.5211 21.0805 10.5215 21.17C10.5219 21.2591 10.5576 21.3445 10.6208 21.4075C10.684 21.4704 10.7695 21.5057 10.8587 21.5057H10.8603L11.3057 21.5037C11.3952 21.5033 11.4808 21.4673 11.5437 21.4038C11.6067 21.3403 11.6419 21.2543 11.6415 21.1649C11.6411 21.0757 11.6054 20.9903 11.5422 20.9274C11.479 20.8645 11.3934 20.8291 11.3043 20.8291ZM13.0633 14.7873C12.957 14.7202 12.8578 14.6425 12.7671 14.5554C12.7027 14.4934 12.6162 14.4595 12.5268 14.4613C12.4373 14.463 12.3523 14.5002 12.2902 14.5646C12.2595 14.5965 12.2354 14.6342 12.2193 14.6754C12.2031 14.7167 12.1952 14.7607 12.1961 14.805C12.197 14.8492 12.2065 14.8929 12.2243 14.9335C12.242 14.9741 12.2676 15.0108 12.2995 15.0415C12.4231 15.1603 12.5584 15.2663 12.7034 15.3578C12.7408 15.3815 12.7826 15.3975 12.8263 15.405C12.8699 15.4126 12.9146 15.4114 12.9579 15.4016C13.0011 15.3918 13.0419 15.3736 13.0781 15.348C13.1143 15.3224 13.145 15.29 13.1686 15.2525C13.1923 15.215 13.2083 15.1732 13.2158 15.1296C13.2233 15.0859 13.2221 15.0412 13.2123 14.9981C13.2025 14.9549 13.1843 14.914 13.1588 14.8778C13.1332 14.8417 13.1008 14.8109 13.0633 14.7873ZM12.2342 13.5793C12.2115 13.4622 12.2001 13.3431 12.2001 13.2238L12.2002 13.2051C12.2007 13.1608 12.1925 13.1168 12.176 13.0757C12.1595 13.0346 12.1351 12.9971 12.1042 12.9654C12.0732 12.9338 12.0363 12.9085 11.9956 12.8911C11.9549 12.8736 11.9111 12.8644 11.8669 12.8639L11.8629 12.8639C11.7741 12.8639 11.6889 12.8989 11.6258 12.9613C11.5627 13.0237 11.5267 13.1085 11.5257 13.1972L11.5255 13.2238C11.5255 13.3867 11.5412 13.5497 11.5721 13.7084C11.5806 13.7518 11.5975 13.7932 11.622 13.8301C11.6465 13.8671 11.678 13.8988 11.7147 13.9235C11.7514 13.9483 11.7927 13.9656 11.8361 13.9744C11.8795 13.9832 11.9242 13.9834 11.9677 13.9749C12.0112 13.9664 12.0526 13.9494 12.0895 13.925C12.1264 13.9005 12.1581 13.869 12.1829 13.8323C12.2076 13.7956 12.2249 13.7543 12.2337 13.7109C12.2425 13.6675 12.2427 13.6228 12.2342 13.5793ZM16.3832 15.0708H15.9378C15.8483 15.0708 15.7625 15.1063 15.6993 15.1696C15.636 15.2328 15.6005 15.3186 15.6005 15.4081C15.6005 15.4975 15.636 15.5833 15.6993 15.6466C15.7625 15.7098 15.8483 15.7453 15.9378 15.7453H16.3832C16.4727 15.7453 16.5584 15.7098 16.6217 15.6466C16.6849 15.5833 16.7205 15.4975 16.7205 15.4081C16.7205 15.3186 16.6849 15.2328 16.6217 15.1696C16.5584 15.1063 16.4727 15.0708 16.3832 15.0708ZM21.7981 15.7575C21.6679 15.6547 21.5298 15.5621 21.3852 15.4807C21.2231 15.3892 21.0173 15.4467 20.9258 15.609C20.9041 15.6475 20.8902 15.69 20.8849 15.734C20.8795 15.778 20.8829 15.8226 20.8948 15.8652C20.9068 15.9079 20.927 15.9478 20.9543 15.9826C20.9816 16.0175 21.0156 16.0466 21.0541 16.0683C21.1682 16.1327 21.2772 16.2058 21.38 16.2869C21.4395 16.334 21.513 16.3596 21.5888 16.3595C21.659 16.3596 21.7273 16.3378 21.7845 16.2972C21.8417 16.2566 21.8848 16.1992 21.9078 16.133C21.9308 16.0668 21.9326 15.995 21.913 15.9277C21.8933 15.8604 21.8532 15.8009 21.7981 15.7575ZM20.5462 21.0546C20.5206 20.8701 20.3501 20.741 20.1659 20.7667C20.0555 20.7819 19.9424 20.79 19.8296 20.7904L19.7665 20.7907C19.6783 20.793 19.5946 20.8296 19.5332 20.8928C19.4717 20.9559 19.4374 21.0406 19.4376 21.1288C19.4378 21.2169 19.4725 21.3015 19.5343 21.3644C19.596 21.4272 19.6799 21.4635 19.768 21.4653H19.7697L19.8327 21.465C19.975 21.4644 20.1172 21.4543 20.2583 21.4349C20.3021 21.4288 20.3444 21.4142 20.3826 21.3918C20.4208 21.3694 20.4543 21.3397 20.481 21.3044C20.5077 21.2691 20.5273 21.2288 20.5384 21.186C20.5496 21.1431 20.5523 21.0985 20.5462 21.0546ZM22.0347 20.1062C22.0042 20.0741 21.9676 20.0483 21.9271 20.0304C21.8866 20.0125 21.843 20.0027 21.7987 20.0016C21.7544 20.0005 21.7104 20.0082 21.669 20.0241C21.6277 20.0401 21.59 20.064 21.5579 20.0946C21.4629 20.185 21.3611 20.2679 21.2534 20.3427C21.1799 20.3937 21.1297 20.4718 21.1137 20.5598C21.0978 20.6478 21.1174 20.7385 21.1684 20.8121C21.1994 20.8569 21.2409 20.8936 21.2892 20.9188C21.3376 20.9441 21.3913 20.9573 21.4459 20.9572C21.5144 20.9572 21.5814 20.9363 21.6377 20.8971C21.7741 20.8025 21.9029 20.6975 22.0231 20.583C22.0552 20.5525 22.081 20.5159 22.0989 20.4754C22.1168 20.4349 22.1266 20.3913 22.1277 20.347C22.1288 20.3027 22.1211 20.2587 22.1051 20.2173C22.0892 20.176 22.0652 20.1382 22.0347 20.1062ZM22.6911 18.4682C22.6032 18.4517 22.5123 18.4707 22.4385 18.5212C22.3646 18.5716 22.3138 18.6493 22.2973 18.7372C22.273 18.8658 22.2387 18.9924 22.1948 19.1158C22.1659 19.1998 22.1713 19.2918 22.2098 19.3718C22.2483 19.4518 22.3169 19.5135 22.4005 19.5433C22.4842 19.5731 22.5762 19.5687 22.6567 19.5311C22.7371 19.4935 22.7995 19.4256 22.8303 19.3423C22.886 19.1858 22.9294 19.0253 22.9602 18.862C22.9767 18.7741 22.9577 18.6832 22.9072 18.6094C22.8567 18.5355 22.779 18.4847 22.6911 18.4682ZM22.8914 17.384C22.8455 17.2242 22.7872 17.0683 22.7169 16.9176C22.6782 16.8379 22.6097 16.7766 22.5263 16.747C22.4428 16.7173 22.3511 16.7217 22.2708 16.7592C22.1905 16.7966 22.1282 16.8641 22.0973 16.9471C22.0664 17.0301 22.0694 17.122 22.1056 17.2028C22.1609 17.3214 22.2069 17.4443 22.2431 17.5701C22.2633 17.6405 22.3058 17.7024 22.3643 17.7464C22.4227 17.7905 22.4939 17.8144 22.5671 17.8144C22.6194 17.8144 22.6709 17.8023 22.7177 17.779C22.7644 17.7557 22.8051 17.7218 22.8366 17.6801C22.868 17.6384 22.8894 17.5899 22.8989 17.5385C22.9084 17.4872 22.9059 17.4343 22.8914 17.384ZM18.1651 15.0708H17.7196C17.6302 15.0708 17.5444 15.1063 17.4811 15.1696C17.4179 15.2328 17.3824 15.3186 17.3824 15.4081C17.3824 15.4975 17.4179 15.5833 17.4811 15.6466C17.5444 15.7098 17.6302 15.7453 17.7196 15.7453H18.1651C18.2545 15.7453 18.3403 15.7098 18.4036 15.6466C18.4668 15.5833 18.5024 15.4975 18.5024 15.4081C18.5024 15.3186 18.4668 15.2328 18.4036 15.1696C18.3403 15.1063 18.2545 15.0708 18.1651 15.0708ZM16.6498 20.8048H16.6482L16.2028 20.8069C16.1143 20.8085 16.03 20.8448 15.968 20.9081C15.9061 20.9713 15.8715 21.0564 15.8717 21.1449C15.8719 21.2334 15.9069 21.3183 15.9691 21.3812C16.0313 21.4442 16.1158 21.4802 16.2043 21.4814H16.2059L16.6513 21.4794C16.7408 21.4792 16.8265 21.4435 16.8896 21.3801C16.9527 21.3167 16.988 21.2308 16.9878 21.1414C16.9876 21.0519 16.9519 20.9662 16.8885 20.9031C16.8251 20.84 16.7393 20.8046 16.6498 20.8048ZM19.9618 15.074C19.9139 15.0719 19.8661 15.0708 19.8182 15.0708H19.5015C19.412 15.0708 19.3262 15.1064 19.263 15.1696C19.1997 15.2329 19.1642 15.3187 19.1642 15.4081C19.1642 15.4976 19.1997 15.5833 19.263 15.6466C19.3262 15.7098 19.412 15.7454 19.5015 15.7454H19.8182V15.7453C19.8563 15.7453 19.8943 15.7462 19.9319 15.7479C19.9762 15.7498 20.0204 15.7431 20.062 15.7279C20.1036 15.7128 20.1419 15.6897 20.1746 15.6598C20.2072 15.6299 20.2337 15.5938 20.2524 15.5537C20.2712 15.5136 20.2818 15.4701 20.2838 15.4259C20.2857 15.3817 20.279 15.3374 20.2639 15.2958C20.2487 15.2542 20.2256 15.2159 20.1957 15.1833C20.1658 15.1506 20.1297 15.1241 20.0896 15.1054C20.0495 15.0866 20.006 15.076 19.9618 15.074ZM18.4316 20.7968H18.4301L17.9846 20.7988C17.8961 20.8004 17.8118 20.8368 17.7499 20.9C17.6879 20.9633 17.6533 21.0483 17.6535 21.1368C17.6537 21.2253 17.6887 21.3102 17.7509 21.3732C17.8131 21.4362 17.8976 21.4721 17.9861 21.4734H17.9877L18.4331 21.4713C18.5226 21.4709 18.6082 21.435 18.6711 21.3715C18.7341 21.3079 18.7693 21.222 18.7689 21.1325C18.7685 21.0434 18.7328 20.958 18.6696 20.895C18.6064 20.8321 18.5208 20.7968 18.4316 20.7968Z">
                                                </path>
                                            </g>
                                        </svg>
                                        {{ translate('Tour') }}
                                    </button>
                                </form>
                            </li>
                            <li class="nav-item" role="presentation">
                                <form action="" method="GET">
                                    <input type="hidden" name="type" value="hotel">
                                    <button class="nav-link {{ Request::get('type') == 'hotel' ? 'active' : '' }}"
                                        type="submit">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23"
                                            viewBox="0 0 23 23">
                                            <g>
                                                <path
                                                    d="M19.5496 12.2665H16.0038C15.9021 12.2665 15.8046 12.3069 15.7327 12.3788C15.6608 12.4506 15.6204 12.5481 15.6204 12.6498V22.2333H13.6079V18.2562C13.6079 18.1545 13.5675 18.057 13.4956 17.9851C13.4237 17.9132 13.3262 17.8728 13.2245 17.8728H9.77448C9.67281 17.8728 9.57531 17.9132 9.50342 17.9851C9.43153 18.057 9.39114 18.1545 9.39114 18.2562V22.2333H7.37861V8.33724C7.37861 7.91452 7.72256 7.57056 8.14529 7.57056H14.8537C15.2765 7.57056 15.6204 7.91452 15.6204 8.33724V10.4696C15.6204 10.5712 15.6608 10.6687 15.7327 10.7406C15.8046 10.8125 15.9021 10.8529 16.0038 10.8529C16.1054 10.8529 16.2029 10.8125 16.2748 10.7406C16.3467 10.6687 16.3871 10.5712 16.3871 10.4696V8.33724C16.3871 7.49174 15.6992 6.80389 14.8537 6.80389H8.14529C7.29978 6.80389 6.61193 7.49174 6.61193 8.33724V22.2333H3.44937C3.02665 22.2333 2.6827 21.8894 2.6827 21.4666V13.7998C2.6827 13.3771 3.02665 13.0332 3.44937 13.0332H4.79106C4.89273 13.0332 4.99024 12.9928 5.06213 12.9209C5.13402 12.849 5.1744 12.7515 5.1744 12.6498C5.1744 12.5481 5.13402 12.4506 5.06213 12.3788C4.99024 12.3069 4.89273 12.2665 4.79106 12.2665H3.44937C2.60387 12.2665 1.91602 12.9543 1.91602 13.7998V21.4666C1.91602 22.3121 2.60387 23 3.44937 23H16.0038C16.1054 23 16.2029 22.9596 16.2748 22.8877C16.3467 22.8158 16.3871 22.7183 16.3871 22.6166V13.0332H19.5496C19.9724 13.0332 20.3163 13.3771 20.3163 13.7998V21.4666C20.3163 21.8894 19.9724 22.2333 19.5496 22.2333H17.9205C17.8188 22.2333 17.7213 22.2737 17.6494 22.3456C17.5775 22.4175 17.5371 22.515 17.5371 22.6166C17.5371 22.7183 17.5775 22.8158 17.6494 22.8877C17.7213 22.9596 17.8188 23 17.9205 23H19.5496C20.3951 23 21.083 22.3121 21.083 21.4666V13.7998C21.083 12.9543 20.3951 12.2665 19.5496 12.2665ZM10.1578 18.6395H12.8412V22.2333H10.1578V18.6395ZM9.91047 3.38493L9.66638 4.81306C9.61295 5.12577 9.94233 5.36473 10.2231 5.21667L11.4995 4.54323L12.7759 5.21667C13.0564 5.36473 13.3861 5.12591 13.3326 4.81306L13.0885 3.38493L14.1222 2.37377C14.3485 2.15239 14.2231 1.76613 13.9095 1.72042L12.4821 1.51226L11.8435 0.213796C11.7034 -0.0712652 11.2957 -0.0712652 11.1556 0.213796L10.517 1.51226L9.0896 1.72042C8.77631 1.76603 8.65029 2.15215 8.87684 2.37377L9.91047 3.38493ZM10.8272 2.24181C10.8888 2.23283 10.9472 2.209 10.9975 2.17239C11.0478 2.13577 11.0884 2.08747 11.1159 2.03164L11.4995 1.25159L11.8831 2.03164C11.9106 2.08746 11.9512 2.13577 12.0015 2.17238C12.0518 2.209 12.1102 2.23283 12.1718 2.24181L13.0324 2.36735L12.4087 2.97739C12.3644 3.02074 12.3313 3.07419 12.3122 3.13316C12.293 3.19213 12.2885 3.25486 12.2989 3.31597L12.4458 4.17571C11.6045 3.73185 11.6245 3.72648 11.4995 3.72648C11.3729 3.72648 11.3843 3.73717 10.5531 4.17571L10.7 3.31597C10.7105 3.25486 10.7059 3.19213 10.6868 3.13316C10.6677 3.07418 10.6345 3.02073 10.5902 2.97739L9.96653 2.36735L10.8272 2.24181ZM5.66953 3.49173C5.75655 3.22397 6.00088 3.22766 6.24406 3.1923C6.35365 2.9702 6.42462 2.73842 6.70633 2.73842C6.98784 2.73842 7.05986 2.97193 7.16863 3.1923L7.43371 3.23082C7.74748 3.27644 7.87335 3.66323 7.64618 3.8847L7.45436 4.07168C7.4962 4.31577 7.57507 4.54506 7.34713 4.71061C7.11937 4.87612 6.92387 4.72949 6.70633 4.61511C6.48715 4.73035 6.29347 4.87621 6.06552 4.71061C5.83772 4.54515 5.91674 4.3139 5.95829 4.07168C5.78113 3.89889 5.58247 3.75964 5.66953 3.49173ZM2.31531 5.40843C2.40233 5.14067 2.64666 5.14436 2.88984 5.109L3.00839 4.86879C3.14874 4.58449 3.55546 4.58425 3.69591 4.86879L3.81446 5.109C4.05951 5.1446 4.30192 5.14048 4.38899 5.40843C4.47601 5.6762 4.27614 5.81683 4.10019 5.98838L4.14547 6.2524C4.18577 6.48729 4.00426 6.70053 3.76759 6.70053C3.64943 6.70053 3.60271 6.66358 3.3522 6.53181L3.11515 6.65649C2.8345 6.80398 2.5053 6.56516 2.55892 6.2524L2.60421 5.98838C2.42691 5.81559 2.22825 5.67634 2.31531 5.40843ZM15.2559 3.49173C15.3429 3.22397 15.5872 3.22766 15.8304 3.1923L15.9489 2.95209C16.0893 2.66775 16.496 2.6676 16.6365 2.95209L16.755 3.1923C17 3.2279 17.2425 3.22378 17.3295 3.49173C17.4165 3.7595 17.2167 3.90013 17.0407 4.07168L17.086 4.3357C17.1263 4.5706 16.9448 4.78383 16.7081 4.78383C16.59 4.78383 16.5433 4.74688 16.2927 4.61511L16.0557 4.73979C15.775 4.88728 15.4458 4.64846 15.4995 4.3357L15.5447 4.07168C15.3674 3.89889 15.1688 3.75964 15.2559 3.49173ZM18.6101 5.40843C18.6971 5.14067 18.9414 5.14436 19.1846 5.109L19.3032 4.86879C19.4435 4.58444 19.8502 4.5843 19.9907 4.86879L20.1092 5.109C20.3542 5.1446 20.5967 5.14048 20.6838 5.40843C20.7708 5.6762 20.5709 5.81683 20.395 5.98838L20.4402 6.2524C20.4805 6.48729 20.299 6.70053 20.0624 6.70053C19.9442 6.70053 19.8975 6.66358 19.647 6.53181L19.4099 6.65649C19.1293 6.80398 18.8001 6.56516 18.8537 6.2524L18.899 5.98838C18.7216 5.81559 18.523 5.67634 18.6101 5.40843ZM4.64731 15.7165C4.74898 15.7165 4.84648 15.7569 4.91837 15.8288C4.99026 15.9007 5.03065 15.9982 5.03065 16.0999V17.7291C5.03065 17.8307 4.99026 17.9282 4.91837 18.0001C4.84648 18.072 4.74898 18.1124 4.64731 18.1124C4.54564 18.1124 4.44814 18.072 4.37625 18.0001C4.30436 17.9282 4.26397 17.8307 4.26397 17.7291V16.0999C4.26397 15.9982 4.30436 15.9007 4.37625 15.8288C4.44814 15.7569 4.54564 15.7165 4.64731 15.7165ZM18.3517 18.1124C18.25 18.1124 18.1525 18.072 18.0806 18.0001C18.0088 17.9282 17.9684 17.8307 17.9684 17.7291V16.0999C17.9684 15.9982 18.0088 15.9007 18.0806 15.8288C18.1525 15.7569 18.25 15.7165 18.3517 15.7165C18.4534 15.7165 18.5509 15.7569 18.6228 15.8288C18.6947 15.9007 18.735 15.9982 18.735 16.0999V17.7291C18.735 17.8307 18.6947 17.9282 18.6228 18.0001C18.5509 18.072 18.4534 18.1124 18.3517 18.1124ZM13.7037 11.4519H9.29531C9.19364 11.4519 9.09613 11.4115 9.02424 11.3396C8.95235 11.2677 8.91197 11.1702 8.91197 11.0685C8.91197 10.9669 8.95235 10.8694 9.02424 10.7975C9.09613 10.7256 9.19364 10.6852 9.29531 10.6852H13.7037C13.8054 10.6852 13.9029 10.7256 13.9748 10.7975C14.0467 10.8694 14.0871 10.9669 14.0871 11.0685C14.0871 11.1702 14.0467 11.2677 13.9748 11.3396C13.9029 11.4115 13.8054 11.4519 13.7037 11.4519ZM13.0329 14.3748C13.1345 14.3748 13.232 14.4152 13.3039 14.4871C13.3758 14.559 13.4162 14.6565 13.4162 14.7582C13.4162 14.8599 13.3758 14.9574 13.3039 15.0292C13.232 15.1011 13.1345 15.1415 13.0329 15.1415H9.96615C9.86448 15.1415 9.76698 15.1011 9.69509 15.0292C9.6232 14.9574 9.58281 14.8599 9.58281 14.7582C9.58281 14.6565 9.6232 14.559 9.69509 14.4871C9.76698 14.4152 9.86448 14.3748 9.96615 14.3748H13.0329Z">
                                                </path>
                                            </g>
                                        </svg>
                                        {{ translate('Hotel') }}
                                    </button>
                                </form>
                            </li>
                            <li class="nav-item" role="presentation">
                                <form action="" method="GET">
                                    <input type="hidden" name="type" value="activities">
                                    <button class="nav-link {{ Request::get('type') == 'activities' ? 'active' : '' }}"
                                        type="submit">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23"
                                            viewBox="0 0 23 23">
                                            <path
                                                d="M16.7611 10.0896L15.946 9.90118L15.7703 9.02653C15.7562 8.95659 15.7249 8.89201 15.6795 8.83957C15.6341 8.78713 15.5764 8.74879 15.5125 8.72857C15.4485 8.70835 15.3806 8.707 15.316 8.72467C15.2514 8.74233 15.1924 8.77836 15.1453 8.82896L13.6465 10.4371C13.6026 10.4842 13.5705 10.5423 13.553 10.6061C13.5354 10.6699 13.5331 10.7374 13.5462 10.8025L13.7297 11.7162L13.1228 12.3674C12.6046 11.9319 11.953 11.6713 11.2453 11.6713C9.56304 11.6713 8.1944 13.1398 8.1944 14.9449C8.1944 16.7499 9.56304 18.2184 11.2453 18.2184C12.9276 18.2184 14.2962 16.7499 14.2962 14.9449C14.2962 14.1855 14.0533 13.4864 13.6475 12.9304L14.2544 12.2792L15.1059 12.4761C15.1666 12.4902 15.2295 12.4877 15.289 12.4689C15.3485 12.4501 15.4026 12.4156 15.4464 12.3685L16.9453 10.7603C16.9924 10.7097 17.0259 10.6464 17.0424 10.5771C17.0588 10.5078 17.0575 10.435 17.0387 10.3663C17.0198 10.2977 16.9841 10.2358 16.9353 10.1871C16.8864 10.1384 16.8262 10.1048 16.7611 10.0896ZM13.5543 14.9448C13.5543 16.3109 12.5185 17.4223 11.2453 17.4223C9.97217 17.4223 8.93633 16.3109 8.93633 14.9448C8.93633 13.5788 9.97217 12.4673 11.2453 12.4673C11.7483 12.4673 12.2138 12.6413 12.5935 12.9354L11.8476 13.7356C11.8255 13.6807 11.792 13.6318 11.7498 13.5927C11.7075 13.5536 11.6576 13.5252 11.6037 13.5097C11.5087 13.4825 11.4074 13.4968 11.3222 13.5496C11.237 13.6024 11.1749 13.6893 11.1493 13.7912L10.887 14.8418C10.8702 14.9093 10.8702 14.9803 10.887 15.0478C10.9039 15.1153 10.937 15.1768 10.9831 15.2262C11.0291 15.2756 11.0864 15.3111 11.1493 15.3292C11.2122 15.3473 11.2784 15.3474 11.3413 15.3293L12.3204 15.0479C12.3675 15.0343 12.4116 15.011 12.4502 14.9792C12.4889 14.9474 12.5213 14.9077 12.5457 14.8624C12.57 14.8171 12.5858 14.7672 12.5922 14.7154C12.5985 14.6635 12.5953 14.6109 12.5827 14.5604C12.5683 14.5025 12.5419 14.449 12.5054 14.4036C12.469 14.3583 12.4234 14.3224 12.3722 14.2986L13.1181 13.4984C13.3922 13.9057 13.5543 14.4052 13.5543 14.9448ZM15.0647 11.6522L14.4472 11.5094L14.314 10.8468L15.1884 9.90854L15.2711 10.3201C15.2862 10.3949 15.321 10.4635 15.3714 10.5177C15.4219 10.5718 15.4858 10.6092 15.5556 10.6253L15.9391 10.714L15.0647 11.6522ZM16.468 13.979C16.5188 14.298 16.5444 14.6211 16.5445 14.9448C16.5445 18.0801 14.1673 20.6307 11.2453 20.6307C8.32331 20.6307 5.94615 18.08 5.94615 14.9448C5.94615 11.8096 8.32336 9.25894 11.2453 9.25894C11.546 9.25894 11.8488 9.28655 12.1454 9.34098C12.2424 9.35882 12.3288 9.41724 12.3856 9.5034C12.4425 9.58956 12.4651 9.69642 12.4485 9.80047C12.4319 9.90452 12.3775 9.99722 12.2971 10.0582C12.2168 10.1192 12.1173 10.1435 12.0203 10.1257C11.7643 10.0787 11.505 10.055 11.2453 10.055C8.73244 10.055 6.68808 12.2486 6.68808 14.9448C6.68808 17.641 8.73244 19.8346 11.2453 19.8346C13.7582 19.8346 15.8026 17.641 15.8026 14.9448C15.8025 14.6661 15.7804 14.388 15.7367 14.1133C15.7201 14.0093 15.7427 13.9024 15.7995 13.8163C15.8563 13.7301 15.9427 13.6717 16.0397 13.6538C16.0877 13.645 16.1369 13.6464 16.1844 13.658C16.2319 13.6695 16.2768 13.691 16.3166 13.7212C16.3564 13.7514 16.3902 13.7897 16.4162 13.8339C16.4421 13.8782 16.4597 13.9275 16.468 13.979ZM20.834 2.12563H20.249V0.879517C20.249 0.394556 19.8813 0 19.4293 0H18.5395C18.0875 0 17.7197 0.394556 17.7197 0.879517V2.12563H16.5068V0.879517C16.5068 0.394556 16.139 0 15.6871 0H14.7973C14.3453 0 13.9776 0.394556 13.9776 0.879517V2.12563H12.7646V0.879517C12.7646 0.394556 12.3969 0 11.9449 0H11.0551C10.6031 0 10.2354 0.394556 10.2354 0.879517V2.12563H9.0224V0.879517C9.0224 0.394556 8.65463 0 8.20265 0H7.31284C6.86086 0 6.49314 0.394556 6.49314 0.879517V2.12563H5.28017V0.879517C5.28017 0.394556 4.91245 0 4.46047 0H3.5707C3.11873 0 2.75096 0.394556 2.75096 0.879517V2.12563H2.16599C0.971657 2.12563 0 3.16814 0 4.44963V20.676C0 21.9575 0.971657 23 2.16599 23H18.116C18.7113 23 19.2266 22.771 19.6476 22.3194L22.3656 19.403C22.7866 18.9513 23 18.3984 23 17.7597V4.44963C23 3.16814 22.0283 2.12563 20.834 2.12563ZM18.4617 0.879517C18.462 0.857472 18.4702 0.836413 18.4848 0.820827C18.4993 0.805242 18.5189 0.796362 18.5395 0.796078H19.4293C19.4498 0.796375 19.4695 0.805261 19.484 0.820845C19.4985 0.836429 19.5068 0.85748 19.5071 0.879517V3.80018C19.5068 3.82223 19.4985 3.84329 19.484 3.85888C19.4695 3.87448 19.4498 3.88337 19.4293 3.88367H18.5395C18.5189 3.88337 18.4993 3.87448 18.4848 3.85889C18.4702 3.84329 18.4619 3.82223 18.4617 3.80018V0.879517ZM14.7195 0.879517C14.7198 0.857472 14.7281 0.836413 14.7426 0.820827C14.7571 0.805242 14.7768 0.796362 14.7973 0.796078H15.6871C15.7076 0.796362 15.7272 0.805242 15.7418 0.820827C15.7563 0.836413 15.7646 0.857472 15.7649 0.879517V3.80018C15.7649 3.84541 15.7292 3.88367 15.6871 3.88367H14.7973C14.7767 3.88337 14.7571 3.87448 14.7426 3.85889C14.7281 3.84329 14.7198 3.82223 14.7195 3.80018V0.879517ZM10.9773 0.879517C10.9776 0.857472 10.9859 0.836413 11.0004 0.820827C11.0149 0.805242 11.0346 0.796362 11.0551 0.796078H11.9449C11.9654 0.796362 11.9851 0.805242 11.9996 0.820827C12.0141 0.836413 12.0224 0.857472 12.0227 0.879517V3.80018C12.0227 3.84541 11.987 3.88367 11.9449 3.88367H11.0551C11.0346 3.88337 11.0149 3.87448 11.0004 3.85889C10.9859 3.84329 10.9776 3.82223 10.9773 3.80018V0.879517ZM7.23508 0.879517C7.23535 0.85748 7.24363 0.836429 7.25816 0.820845C7.27268 0.805261 7.2923 0.796375 7.31284 0.796078H8.20265C8.2232 0.796362 8.24283 0.805242 8.25737 0.820827C8.2719 0.836413 8.28019 0.857472 8.28046 0.879517V3.80018C8.28046 3.84541 8.2448 3.88367 8.20265 3.88367H7.31284C7.29229 3.88337 7.27267 3.87448 7.25814 3.85888C7.24362 3.84329 7.23534 3.82223 7.23508 3.80018V0.879517ZM3.49289 0.879517C3.49317 0.857472 3.50146 0.836413 3.51599 0.820827C3.53053 0.805242 3.55016 0.796362 3.5707 0.796078H4.46047C4.48101 0.796375 4.50063 0.805261 4.51515 0.820845C4.52968 0.836429 4.53796 0.85748 4.53823 0.879517V3.80018C4.53797 3.82223 4.52969 3.84329 4.51517 3.85888C4.50064 3.87448 4.48102 3.88337 4.46047 3.88367H3.5707C3.55015 3.88337 3.53052 3.87448 3.51599 3.85889C3.50146 3.84329 3.49317 3.82223 3.49289 3.80018V0.879517ZM2.16599 2.92171H2.75096V3.80018C2.75096 4.28514 3.11873 4.67975 3.5707 4.67975H4.46047C4.91245 4.67975 5.28017 4.28514 5.28017 3.80018V2.92171H6.49314V3.80018C6.49314 4.28514 6.86086 4.67975 7.31284 4.67975H8.20265C8.65463 4.67975 9.0224 4.28514 9.0224 3.80018V2.92171H10.2353V3.80018C10.2353 4.28514 10.6031 4.67975 11.0551 4.67975H11.9448C12.3968 4.67975 12.7646 4.28514 12.7646 3.80018V2.92171H13.9775V3.80018C13.9775 4.28514 14.3453 4.67975 14.7973 4.67975H15.687C16.139 4.67975 16.5068 4.28514 16.5068 3.80018V2.92171H17.7197V3.80018C17.7197 4.28514 18.0875 4.67975 18.5394 4.67975H19.4293C19.8812 4.67975 20.2489 4.28514 20.2489 3.80018V2.92171H20.834C21.6192 2.92171 22.258 3.60713 22.258 4.44963V6.32887H0.741935V4.44963C0.741935 3.60713 1.38074 2.92171 2.16599 2.92171ZM2.16599 22.2039C1.38074 22.2039 0.741935 21.5185 0.741935 20.676V7.1249H22.2581V17.7597C22.2581 17.9007 22.2428 18.0336 22.2126 18.1594H19.3083C18.8563 18.1594 18.4886 18.554 18.4886 19.039V22.1551C18.3713 22.1875 18.2474 22.2039 18.116 22.2039H2.16599ZM19.2305 21.6409V19.0389C19.2308 19.0169 19.239 18.9958 19.2536 18.9802C19.2681 18.9646 19.2877 18.9557 19.3083 18.9554H21.7334L19.2305 21.6409Z">
                                            </path>
                                        </svg>
                                        {{ translate('Activities') }}
                                    </button>
                                </form>
                            </li>
                            <li class="nav-item" role="presentation">
                                <form action="" method="GET">
                                    <input type="hidden" name="type" value="transports">
                                    <button class="nav-link {{ Request::get('type') == 'transports' ? 'active' : '' }}"
                                        type="submit">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="23" height="23"
                                            viewBox="0 0 51 51">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M45.8564 34.4958C45.8564 35.7619 46.8834 36.7871 48.1488 36.7871H50.2528C50.5289 36.7871 50.7528 36.5633 50.7528 36.2871V31.249C50.7528 30.9728 50.5289 30.749 50.2528 30.749H48.1488C46.883 30.749 45.8564 31.7757 45.8564 33.0413V34.4958ZM48.1488 35.7871C47.435 35.7871 46.8564 35.2088 46.8564 34.4958V33.0413C46.8564 32.328 47.4354 31.749 48.1488 31.749H49.7528V35.7871H48.1488Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M0.578816 36.5923C0.673478 36.7152 0.819779 36.7871 0.974858 36.7871H3.80037C5.06692 36.7871 6.09273 35.7617 6.09273 34.4958V33.0413C6.09273 31.7758 5.0673 30.749 3.80037 30.749H2.30235C2.07527 30.749 1.87671 30.902 1.81885 31.1216L0.49136 36.1597C0.451847 36.3097 0.484154 36.4695 0.578816 36.5923ZM1.62367 35.7871L2.68767 31.749H3.80037C4.51466 31.749 5.09273 32.3277 5.09273 33.0413V34.4958C5.09273 35.209 4.51504 35.7871 3.80037 35.7871H1.62367Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M8.26435 27.315C8.26436 27.3149 8.26435 27.315 8.26435 27.315L10.3466 19.4132C10.3639 19.3473 10.4026 19.2887 10.4565 19.247C10.5105 19.2054 10.5767 19.1828 10.6448 19.1827H18.1558C18.6207 19.1827 18.9975 19.5595 18.9975 20.0243V27.5296C18.9975 27.9943 18.6207 28.3711 18.1558 28.3711H9.07815C8.52629 28.3711 8.12377 27.8488 8.26435 27.315ZM7.29734 27.0602C6.98972 28.2281 7.87034 29.3711 9.07815 29.3711H18.1558C19.1729 29.3711 19.9975 28.5466 19.9975 27.5296V20.0243C19.9975 19.0073 19.173 18.1827 18.1558 18.1827H10.6442C10.3553 18.183 10.0741 18.2789 9.84539 18.4555C9.61672 18.6321 9.4529 18.8793 9.37949 19.1587L7.29734 27.0602Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M25.2215 28.3711C24.7567 28.3711 24.3799 27.9943 24.3799 27.5295V20.0243C24.3799 19.5596 24.7566 19.1828 25.2215 19.1828H32.4557C32.5237 19.1828 32.5898 19.2054 32.6436 19.2469C32.6974 19.2884 32.736 19.3466 32.7533 19.4123L33.3161 21.5481C33.3864 21.8151 33.6599 21.9746 33.9269 21.9042C34.194 21.8339 34.3534 21.5604 34.2831 21.2933L33.7204 19.1577C33.6469 18.8785 33.483 18.6314 33.2543 18.455C33.0257 18.2787 32.7451 18.1829 32.4563 18.1828H25.2215C24.2044 18.1828 23.3799 19.0073 23.3799 20.0243V27.5295C23.3799 28.5466 24.2044 29.3711 25.2215 29.3711H34.0221C35.2493 29.3711 36.1076 28.2048 35.7966 27.0381L35.792 27.0209L35.7916 27.0191L35.0628 24.2527C34.9924 23.9857 34.719 23.8263 34.4519 23.8966C34.1849 23.967 34.0254 24.2405 34.0958 24.5075L34.825 27.2756L34.8255 27.2774L34.8304 27.2957C34.9772 27.8466 34.5727 28.3711 34.0221 28.3711H25.2215Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M11.0698 41.0047C11.0698 42.687 12.4336 44.0508 14.1159 44.0508C15.7983 44.0508 17.162 42.687 17.162 41.0047C17.162 39.3224 15.7983 37.9586 14.1159 37.9586C12.4336 37.9586 11.0698 39.3224 11.0698 41.0047ZM14.1159 43.0508C12.9859 43.0508 12.0698 42.1347 12.0698 41.0047C12.0698 39.8746 12.9859 38.9586 14.1159 38.9586C15.246 38.9586 16.162 39.8746 16.162 41.0047C16.162 42.1347 15.246 43.0508 14.1159 43.0508Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M32.3147 41.0047C32.3147 42.687 33.6785 44.0508 35.3608 44.0508C37.0431 44.0508 38.4069 42.687 38.4069 41.0047C38.4069 39.3224 37.0431 37.9586 35.3608 37.9586C33.6785 37.9586 32.3147 39.3224 32.3147 41.0047ZM35.3608 43.0508C34.2308 43.0508 33.3147 42.1347 33.3147 41.0047C33.3147 39.8746 34.2308 38.9586 35.3608 38.9586C36.4908 38.9586 37.4069 39.8746 37.4069 41.0047C37.4069 42.1347 36.4908 43.0508 35.3608 43.0508Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M7.52888 13.9199C6.44663 13.9199 5.56934 13.0427 5.56934 11.9605V8.40513C5.56934 7.32301 6.44664 6.44568 7.52888 6.44568H12.0994V13.9199H7.52888ZM4.56934 11.9605C4.56934 13.595 5.8944 14.9199 7.52888 14.9199H12.5994C12.8755 14.9199 13.0994 14.6961 13.0994 14.4199V5.94568C13.0994 5.66954 12.8755 5.44568 12.5994 5.44568H7.52888C5.89439 5.44568 4.56934 6.7707 4.56934 8.40513V11.9605Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M12.0996 14.4199C12.0996 14.6961 12.3235 14.9199 12.5996 14.9199H17.2592C17.5354 14.9199 17.7592 14.6961 17.7592 14.4199V5.94568C17.7592 4.38279 16.4923 3.11582 14.9294 3.11582C13.3666 3.11582 12.0996 4.3828 12.0996 5.94568V14.4199ZM13.0996 13.9199V5.94568C13.0996 4.93506 13.9189 4.11582 14.9294 4.11582C15.94 4.11582 16.7592 4.93507 16.7592 5.94568V13.9199H13.0996Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M16.759 14.4199C16.759 14.6961 16.9829 14.9199 17.259 14.9199H25.1014C25.3775 14.9199 25.6014 14.6961 25.6014 14.4199V5.94568C25.6014 5.66954 25.3775 5.44568 25.1014 5.44568H17.259C16.9829 5.44568 16.759 5.66954 16.759 5.94568V14.4199ZM17.759 13.9199V6.44568H24.6014V13.9199H17.759Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M24.6016 14.4199C24.6016 14.6961 24.8254 14.9199 25.1016 14.9199H29.7612C30.0373 14.9199 30.2612 14.6961 30.2612 14.4199V5.94568C30.2612 4.38279 28.9942 3.11582 27.4313 3.11582C25.8684 3.11582 24.6016 4.38282 24.6016 5.94568V14.4199ZM25.6016 13.9199V5.94568C25.6016 4.93504 26.4207 4.11582 27.4313 4.11582C28.4419 4.11582 29.2612 4.93507 29.2612 5.94568V13.9199H25.6016Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M36.791 11.9609C36.791 12.2371 37.0149 12.4609 37.291 12.4609H40.6825C41.9404 12.4609 42.9601 11.4412 42.9601 10.1833C42.9601 8.92542 41.9404 7.90569 40.6825 7.90569H37.291C37.0149 7.90569 36.791 8.12955 36.791 8.40569V11.9609ZM37.791 11.4609V8.90569H40.6825C41.3881 8.90569 41.9601 9.47771 41.9601 10.1833C41.9601 10.8889 41.3881 11.4609 40.6825 11.4609H37.791Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M29.261 14.4199C29.261 14.6961 29.4848 14.9199 29.761 14.9199H34.8315C36.466 14.9199 37.791 13.595 37.791 11.9605V8.40513C37.791 6.7707 36.466 5.44568 34.8315 5.44568H29.761C29.4848 5.44568 29.261 5.66954 29.261 5.94568V14.4199ZM30.261 13.9199V6.44568H34.8315C35.9137 6.44568 36.791 7.32301 36.791 8.40513V11.9605C36.791 13.0427 35.9137 13.9199 34.8315 13.9199H30.261Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M7.23584 41.0046C7.23584 44.8044 10.3162 47.8848 14.116 47.8848C17.9158 47.8848 20.9962 44.8044 20.9962 41.0046C20.9962 37.2048 17.9158 34.1244 14.116 34.1244C10.3162 34.1244 7.23584 37.2048 7.23584 41.0046ZM14.116 46.8848C10.8685 46.8848 8.23584 44.2521 8.23584 41.0046C8.23584 37.7571 10.8685 35.1244 14.116 35.1244C17.3635 35.1244 19.9962 37.7571 19.9962 41.0046C19.9962 44.2521 17.3635 46.8848 14.116 46.8848Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M1.34781 36.8327C1.34784 36.8326 1.34778 36.8328 1.34781 36.8327L1.45832 36.4151L2.78595 31.3765L3.76729 27.6525C3.83766 27.3855 3.67824 27.112 3.41121 27.0416C3.14418 26.9712 2.87067 27.1306 2.80031 27.3977L1.81896 31.1217L0.380936 36.5774C-0.286037 39.1098 1.62328 41.5898 4.24304 41.5898H7.73792C7.87144 41.5898 7.99942 41.5364 8.09334 41.4415C8.18725 41.3466 8.2393 41.2181 8.23789 41.0846C8.23769 41.0652 8.23702 41.0463 8.2366 41.0341L8.23647 41.0303C8.23598 41.0161 8.23583 41.0097 8.23583 41.0051C8.23583 37.7575 10.8685 35.1249 14.116 35.1249C17.3635 35.1249 19.9961 37.7575 19.9961 41.0051C19.9961 41.0248 19.996 41.0263 19.9956 41.0287C19.9953 41.0318 19.9947 41.0365 19.994 41.0828C19.9921 41.2166 20.044 41.3456 20.1379 41.4409C20.2319 41.5362 20.3601 41.5898 20.494 41.5898H28.9827C29.1166 41.5898 29.2448 41.5362 29.3388 41.4409C29.4327 41.3456 29.4846 41.2166 29.4827 41.0828C29.4821 41.0402 29.4815 41.0338 29.4811 41.0294C29.4808 41.0258 29.4806 41.0236 29.4806 41.0051C29.4806 37.7575 32.1132 35.1249 35.3607 35.1249C38.6082 35.1249 41.2409 37.7575 41.2409 41.0051C41.2409 41.0097 41.2407 41.0161 41.2402 41.0303L41.2401 41.0341C41.2397 41.0462 41.239 41.0652 41.2388 41.0846C41.2374 41.2181 41.2894 41.3466 41.3834 41.4415C41.4773 41.5364 41.6053 41.5898 41.7388 41.5898H46.7595C48.9651 41.5898 50.7529 39.8019 50.7529 37.5955V31.2491C50.7529 29.0436 48.9651 27.2556 46.7595 27.2556H41.6271C41.2359 27.2555 40.8556 27.126 40.5458 26.8872C40.2359 26.6483 40.0138 26.3136 39.9141 25.9353L37.8427 18.0728C37.1983 15.6254 34.9851 13.9204 32.4561 13.9204H10.6442C8.11412 13.9204 5.90196 15.6254 5.25754 18.0728L3.58772 24.4094C3.51735 24.6764 3.67677 24.9499 3.9438 25.0203C4.21083 25.0907 4.48434 24.9312 4.5547 24.6642L6.22455 18.3275C6.75333 16.3193 8.56839 14.9204 10.6442 14.9204H32.4561C34.5309 14.9204 36.3469 16.3193 36.8757 18.3274L38.9471 26.1901C39.1031 26.782 39.4505 27.3056 39.9353 27.6792C40.4201 28.0528 41.0149 28.2555 41.627 28.2556H46.7595C48.4127 28.2556 49.7529 29.5959 49.7529 31.2491V37.5955C49.7529 39.2498 48.4127 40.5898 46.7595 40.5898H42.2285C42.0139 36.9834 39.0211 34.1249 35.3607 34.1249C31.7003 34.1249 28.7077 36.9834 28.493 40.5898H20.9838C20.7691 36.9834 17.7764 34.1249 14.116 34.1249C10.4556 34.1249 7.46284 36.9834 7.24815 40.5898H4.24304C2.2795 40.5898 0.848019 38.7312 1.34781 36.8327Z">
                                            </path>
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M28.4805 41.0046C28.4805 44.8044 31.5608 47.8848 35.3606 47.8848C39.1604 47.8848 42.2408 44.8044 42.2408 41.0046C42.2408 37.2048 39.1604 34.1244 35.3606 34.1244C31.5608 34.1244 28.4805 37.2048 28.4805 41.0046ZM35.3606 46.8848C32.1131 46.8848 29.4805 44.2521 29.4805 41.0046C29.4805 37.7571 32.1131 35.1244 35.3606 35.1244C38.6082 35.1244 41.2408 37.7571 41.2408 41.0046C41.2408 44.2521 38.6082 46.8848 35.3606 46.8848Z">
                                            </path>
                                        </svg>
                                        {{ translate('Transport') }}
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="recent-listing-area">
                    <div class="title-and-tab">
                        @if (Request::get('type') == null)
                            <h6>{{ translate('Booking Info') }}</h6>
                        @elseif (Request::get('type') == 'tour')
                            <h6>{{ translate('Tour Package Info') }}</h6>
                        @elseif (Request::get('type') == 'hotel')
                            <h6>{{ translate('Hotel Info') }}</h6>
                        @elseif (Request::get('type') == 'activities')
                            <h6>{{ translate('Activities Info') }}</h6>
                        @elseif (Request::get('type') == 'transport')
                            <h6>{{ translate('Transport Info') }}</h6>
                        @endif

                        <ul class="nav nav-tabs" id="myTab">
                            <li class="nav-item">
                                <form action="" method="get">
                                    <input type="hidden" name="type" value="{{ Request::get('type') }}">
                                    <input type="hidden" name="status" value="1">
                                    <button
                                        class="nav-link {{ request('status') == 1 ? 'active' : '' }}">{{ translate('Pending') }}</button>
                                </form>
                            </li>
                            <li class="nav-item">
                                <form action="" method="get">
                                    <input type="hidden" name="type" value="{{ Request::get('type') }}">
                                    <input type="hidden" name="status" value="2">
                                    <button
                                        class="nav-link {{ request('status') == 2 ? 'active' : '' }}">{{ translate('Processing') }}</button>
                                </form>
                            </li>
                            <li class="nav-item">
                                <form action="" method="get">
                                    <input type="hidden" name="type" value="{{ Request::get('type') }}">
                                    <input type="hidden" name="status" value="3">
                                    <button
                                        class="nav-link {{ request('status') == 3 ? 'active' : '' }}">{{ translate('Approved') }}</button>
                                </form>
                            </li>
                            <li class="nav-item">
                                <form action="" method="get">
                                    <input type="hidden" name="type" value="{{ Request::get('type') }}">
                                    <input type="hidden" name="status" value="4">
                                    <button
                                        class="nav-link {{ request('status') == 4 ? 'active' : '' }}">{{ translate('Cancelled') }}</button>
                                </form>
                            </li>
                        </ul>
                    </div>

                    <div class="recent-listing-table">
                        <table class="eg-table2">
                            <thead>
                                <tr>
                                    <th>{{ translate('Tour Package') }}</th>
                                    @if (Request::get('type') == 'tour')
                                        <th>{{ translate('Tourist') }}</th>
                                    @endif

                                    @if (Request::get('type') == '')
                                        <th>{{ translate('Category') }}</th>
                                    @endif
                                    @if(Request::get('type') == 'transports')
                                    <th>{{ translate('Type') }}</th>
                                    @endif
                                    <th>{{ translate('Amount') }}</th>
                                    @if (Request::get('type') == 'hotel')
                                        <th>{{ translate('Room Type') }}</th>
                                        <th>{{ translate('Person') }}</th>
                                    @endif
                                    <th>{{ translate('Status') }}</th>
                                    <th>{{ translate('Timeline') }}</th>
                                    <th>{{ translate('Option') }}</th>
                                </tr>
                            </thead>
                            <tbody>

                                @if ($orders->count() > 0)
                                    @foreach ($orders as $order)
                                        <tr>
                                            <td data-label="Tour Package">
                                                <div class="product-name">
                                                    <div class="img">
                                                        @if ($order->product_type == 'tour')
                                                            <img src="{{ asset('uploads/tour/features/' . $order->tours?->features_image) }}"
                                                                alt="">
                                                        @elseif ($order->product_type == 'hotel')
                                                            <img src="{{ asset('uploads/hotel/features/' . $order->hotels?->feature_img) }}"
                                                                alt="">
                                                        @elseif ($order->product_type == 'activities')
                                                            <img src="{{ asset('uploads/activities/features/' . $order->activities?->feature_img) }}"
                                                                alt="">
                                                        @elseif ($order->product_type == 'transports')
                                                            <img src="{{ asset('uploads/transports/features/' . $order->transports?->feature_img) }}"
                                                                alt="">
                                                        @endif
                                                    </div>
                                                    <div class="product-content">
                                                        <h6>
                                                            @if ($order->product_type == 'tour')
                                                                <a target="_blank"
                                                                    href="{{ route('tour.details', ['slug' => $order->tours?->slug]) }}">
                                                                    {{ $order->tours?->title }}
                                                                </a>
                                                            @elseif ($order->product_type == 'hotel')
                                                                <a target="_blank"
                                                                    href="{{ route('hotel.details', ['slug' => $order->hotels?->slug]) }}">
                                                                    {{ $order->hotels?->title }}
                                                                </a>
                                                            @elseif ($order->product_type == 'activities')
                                                                <a target="_blank"
                                                                    href="{{ route('activities.details', ['slug' => $order->activities?->slug]) }}">
                                                                    {{ $order->activities?->title }}
                                                                </a>
                                                            @elseif ($order->product_type == 'transports')
                                                                <a target="_blank"
                                                                    href="{{ route('transport.details', ['slug' => $order->transports?->slug]) }}">
                                                                    {{ $order->transports?->title }}
                                                                </a>
                                                            @endif
                                                        </h6>
                                                        <p>
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18"
                                                                height="18" viewBox="0 0 18 18">
                                                                <path
                                                                    d="M8.99939 0C5.40484 0 2.48047 2.92437 2.48047 6.51888C2.48047 10.9798 8.31426 17.5287 8.56264 17.8053C8.79594 18.0651 9.20326 18.0646 9.43613 17.8053C9.68451 17.5287 15.5183 10.9798 15.5183 6.51888C15.5182 2.92437 12.5939 0 8.99939 0ZM8.99939 9.79871C7.19088 9.79871 5.71959 8.32739 5.71959 6.51888C5.71959 4.71037 7.19091 3.23909 8.99939 3.23909C10.8079 3.23909 12.2791 4.71041 12.2791 6.51892C12.2791 8.32743 10.8079 9.79871 8.99939 9.79871Z">
                                                                </path>
                                                            </svg> <span>
                                                                @if ($order->product_type == 'tour')
                                                                    {{ $order->tours?->countries?->name }}
                                                                @elseif ($order->product_type == 'hotel')
                                                                    {{ $order->hotels?->countries?->name }}
                                                                @elseif ($order->product_type == 'activities')
                                                                    {{ $order->activities?->countries?->name }}
                                                                @elseif ($order->product_type == 'transports')
                                                                    {{ $order->transports?->countries?->name }}
                                                                @endif
                                                            </span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </td>
                                            @if (Request::get('type') == 'tour')
                                                <td data-label="Tourist">
                                                    {{ $order->adult_qty + $order->child_qty }} {{ translate('Person') }}
                                                </td>
                                            @endif
                                            @if (Request::get('type') == '')
                                                <td data-label="Category">{{ $order->product_type }}</td>
                                            @endif
                                            @if(Request::get('type') == 'transports')
                                            <td data-label="Category">{{ $order->transport_type }}</td>
                                            @endif
                                            <td data-label="Price">
                                                @if ($order->product_type == 'tour')
                                                    {{ currency_symbol() . number_format($order->total_with_tax, 2) }}
                                                @elseif ($order->product_type == 'hotel')
                                                {{ currency_symbol() . number_format($order->total_with_tax, 2) }}
                                                @elseif ($order->product_type == 'activities')
                                                    {{ currency_symbol() . number_format($order->total_with_tax, 2) }}
                                                @elseif ($order->product_type == 'transports')
                                                    {{ currency_symbol() . number_format($order->total_with_tax, 2) }}
                                                @endif
                                            </td>
                                            @if (Request::get('type') == 'hotel')
                                                <td data-label="Room Type">
                                                    {{ $order->hotels?->room_type }}
                                                </td>
                                                <td data-label="Room Type">
                                                    {{ $order->hotels?->guest_capability . ' Person' }}
                                                </td>
                                            @endif
                                            <td data-label="Status">
                                                @if ($order->product_type == 'tour')
                                                    @if ($order->status == 1)
                                                        <span class="pending">{{ translate('Pending') }}</span>
                                                    @elseif($order->status == 2)
                                                        <span class="processing">{{ translate('Processing') }}</span>
                                                    @elseif($order->status == 3)
                                                        <span class="confirmed">{{ translate('Approved') }}</span>
                                                    @elseif($order->status == 4)
                                                        <span class="rejected">{{ translate('Cancelled') }}</span>
                                                    @endif
                                                @elseif ($order->product_type == 'hotel')
                                                    @if ($order->status == 1)
                                                        <span class="pending">{{ translate('Pending') }}</span>
                                                    @elseif($order->status == 2)
                                                        <span class="processing">{{ translate('Processing') }}</span>
                                                    @elseif($order->status == 3)
                                                        <span class="confirmed">{{ translate('Approved') }}</span>
                                                    @elseif($order->status == 4)
                                                        <span class="rejected">{{ translate('Cancelled') }}</span>
                                                    @endif
                                                @elseif ($order->product_type == 'activities')
                                                    @if ($order->status == 1)
                                                        <span class="pending">{{ translate('Pending') }}</span>
                                                    @elseif($order->status == 2)
                                                        <span class="processing">{{ translate('Processing') }}</span>
                                                    @elseif($order->status == 3)
                                                        <span class="confirmed">{{ translate('Approved') }}</span>
                                                    @elseif($order->status == 4)
                                                        <span class="rejected">{{ translate('Cancelled') }}</span>
                                                    @endif
                                                @elseif ($order->product_type == 'transports')
                                                    @if ($order->status == 1)
                                                        <span class="pending">{{ translate('Pending') }}</span>
                                                    @elseif($order->status == 2)
                                                        <span class="processing">{{ translate('Processing') }}</span>
                                                    @elseif($order->status == 3)
                                                        <span class="confirmed">{{ translate('Approved') }}</span>
                                                    @elseif($order->status == 4)
                                                        <span class="rejected">{{ translate('Cancelled') }}</span>
                                                    @endif
                                                @endif
                                            </td>
                                            <td data-label="Timeline">
                                                @if ($order->product_type == 'tour')
                                                    {{ \Carbon\Carbon::parse($order->start_date)->format('F j, Y') }}
                                                @elseif ($order->product_type == 'hotel')
                                                    {{ \Carbon\Carbon::parse($order->start_date)->format('F j, Y') }} -
                                                    {{ \Carbon\Carbon::parse($order->end_date)->format('F j, Y') }}
                                                @elseif ($order->product_type == 'activities')
                                                    {{ \Carbon\Carbon::parse($order->start_date)->format('F j, Y') }}
                                                @elseif ($order->product_type == 'transports')
                                                    {{ \Carbon\Carbon::parse($order->start_date)->format('F j, Y') }}
                                                @endif

                                            </td>
                                            <td><a class="btn btn-outline-success" href="{{route('customer.booking.details',$order->order_number)}}"><i class="bi bi-receipt"></i></a></td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" style="text-align: center;">
                                            <h5 class="data-not-found">{{ translate('No Data Found') }}
                                            </h5>
                                        </td>
                                    </tr>
                                @endif

                            </tbody>
                        </table>
                        <div class="pagination-area">
                            {!! $orders->links('vendor.pagination.custom') !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
