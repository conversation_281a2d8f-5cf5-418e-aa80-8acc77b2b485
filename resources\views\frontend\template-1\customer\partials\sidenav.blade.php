<div class="dashboard-sidebar-wrapper">
    <div class="dashboard-sidebar-menu">
        <ul>
            <li class="{{ Request::routeIs('customer.dashboard') ? 'active' : '' }}">
                <a href="{{ route('customer.dashboard') }}">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                        <g>
                            <path
                                d="M8.42631 0.0843773C8.31381 0.130079 8.16264 0.210939 8.09233 0.260159C8.0185 0.309376 6.24311 2.0707 4.14076 4.17305C1.06108 7.25977 0.305216 8.0332 0.231388 8.18086C-0.106112 8.86289 0.00638798 9.6082 0.523185 10.1285C0.846623 10.4484 1.25795 10.6172 1.72904 10.6172H1.96811V13.4297C1.96811 15.3316 1.98217 16.2984 2.00678 16.425C2.14037 17.0648 2.65717 17.6449 3.29701 17.8664C3.52905 17.9473 3.54662 17.9473 5.24467 17.9473C6.91811 17.9473 6.95678 17.9473 7.0517 17.8734C7.10444 17.8348 7.18178 17.7574 7.22045 17.7047C7.29428 17.6098 7.29428 17.5641 7.31186 15.2613L7.32944 12.9129L7.43491 12.7406C7.5685 12.5262 7.8392 12.3539 8.09936 12.3223C8.1978 12.3082 8.68998 12.3047 9.19272 12.3117L10.1068 12.3223L10.2931 12.4242C10.4689 12.5227 10.5814 12.6387 10.6904 12.832C10.729 12.9059 10.7431 13.3383 10.7572 15.2648C10.7747 17.5641 10.7747 17.6098 10.8486 17.7047C10.8872 17.7574 10.9646 17.8348 11.0173 17.8734C11.1122 17.9473 11.1509 17.9473 12.8244 17.9473C14.5224 17.9473 14.54 17.9473 14.772 17.8664C15.4119 17.6449 15.9287 17.0648 16.0623 16.425C16.0869 16.2984 16.1009 15.3316 16.1009 13.4297V10.6172H16.34C17.0009 10.6172 17.5775 10.2656 17.8623 9.68555C17.9818 9.44297 17.9818 9.43594 17.9818 8.96484C17.9818 8.16328 18.3861 8.63789 13.872 4.12735C9.38608 -0.362108 9.83608 0.0246105 9.06967 0.00703239C8.67241 1.90735e-06 8.61264 0.00703239 8.42631 0.0843773ZM9.29467 1.08985C9.50209 1.18828 16.8498 8.54648 16.9271 8.72578C17.0009 8.90508 16.9939 9.05625 16.906 9.23203C16.7759 9.50274 16.6669 9.54141 15.9884 9.5625C15.3521 9.58008 15.3064 9.59414 15.1376 9.82266C15.0638 9.91758 15.0638 9.95625 15.0462 13.1063L15.0287 16.2949L14.9302 16.4602C14.8775 16.5516 14.7826 16.6676 14.7193 16.7133C14.4732 16.9031 14.3994 16.9102 13.0564 16.9102H11.8119V14.9414C11.8119 13.6512 11.7978 12.9129 11.7732 12.7898C11.6361 12.1289 11.1052 11.5594 10.4232 11.3449C10.1912 11.2711 10.1138 11.2676 9.03451 11.2676C7.77592 11.2676 7.71264 11.2746 7.26264 11.5031C6.81615 11.7281 6.40131 12.2836 6.29584 12.7898C6.27123 12.9129 6.25717 13.6512 6.25717 14.9414V16.9102H5.01264C3.66967 16.9102 3.59584 16.9031 3.34975 16.7133C3.28647 16.6676 3.19155 16.5516 3.13881 16.4602L3.04037 16.2949L3.02279 13.1063C3.00522 9.95625 3.00522 9.91758 2.93139 9.82266C2.76264 9.59414 2.71694 9.58008 2.08061 9.5625C1.40209 9.54141 1.29311 9.50274 1.16303 9.23203C1.07514 9.05625 1.06811 8.90508 1.14194 8.72578C1.21576 8.55 8.56694 1.1918 8.77084 1.08985C8.95014 1.00196 9.11537 1.00196 9.29467 1.08985Z" />
                        </g>
                    </svg>
                    <h6>{{ translate('Dashboard') }}</h6>
                </a>
            </li>
            <li class="{{ Request::routeIs('customer.booking') ? 'active' : '' }}">
                <a href="{{ route('customer.booking') }}">
                    <svg width="18" height="18" viewBox="0 0 20 20" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M19.1196 14.1266H3.66036C2.63685 14.1266 1.80701 13.2969 1.80701 12.2732V0.956753C1.80719 0.816847 1.82312 0.677302 1.85443 0.541016C1.01336 0.734857 0.417173 1.48362 0.416992 2.34677V13.6633C0.416992 14.6869 1.24684 15.5166 2.27035 15.5166H17.7296C18.5929 15.5164 19.3416 14.9204 19.5355 14.0792C19.399 14.1107 19.2597 14.1266 19.1196 14.1266Z" />
                        <path
                            d="M13.475 6.05345H10.8803C10.7013 6.05345 10.556 5.9083 10.556 5.72912C10.556 5.55012 10.7013 5.40479 10.8803 5.40479H13.475C13.6542 5.40479 13.7994 5.55012 13.7994 5.72912C13.7994 5.9083 13.6542 6.05345 13.475 6.05345Z" />
                        <path
                            d="M13.475 7.21177H10.8803C10.7013 7.21177 10.556 7.06662 10.556 6.88744C10.556 6.70844 10.7013 6.56311 10.8803 6.56311H13.475C13.6542 6.56311 13.7994 6.70844 13.7994 6.88744C13.7994 7.06662 13.6542 7.21177 13.475 7.21177Z" />
                        <path
                            d="M16.2087 7.67517H15.0504C14.8714 7.67517 14.726 7.53001 14.726 7.35083C14.726 7.17183 14.8714 7.02649 15.0504 7.02649H16.2087C16.3879 7.02649 16.5331 7.17183 16.5331 7.35083C16.5331 7.53001 16.3879 7.67517 16.2087 7.67517Z" />
                        <path
                            d="M16.2087 8.64812H15.0504C14.8714 8.64812 14.726 8.50296 14.726 8.32378C14.726 8.14478 14.8714 7.99945 15.0504 7.99945H16.2087C16.3879 7.99945 16.5331 8.14478 16.5331 8.32378C16.5331 8.50296 16.3879 8.64812 16.2087 8.64812Z" />
                        <path
                            d="M16.2087 9.66746H15.0504C14.8714 9.66746 14.726 9.52231 14.726 9.34313C14.726 9.16413 14.8714 9.0188 15.0504 9.0188H16.2087C16.3879 9.0188 16.5331 9.16413 16.5331 9.34313C16.5331 9.52231 16.3879 9.66746 16.2087 9.66746Z" />
                        <path
                            d="M5.32878 8.13993C3.91668 8.13993 2.77191 6.99534 2.77191 5.58324C2.77191 4.17096 3.91668 3.02619 5.32878 3.02637C6.74088 3.02637 7.88547 4.17114 7.88547 5.58324C7.88547 6.99516 6.74088 8.13993 5.32878 8.13993ZM5.32878 3.67504C4.27487 3.67504 3.42059 4.52932 3.42059 5.58324C3.42059 6.63697 4.27487 7.49125 5.32878 7.49125C6.38252 7.49125 7.2368 6.63697 7.2368 5.58324C7.23951 5.07628 7.03934 4.58941 6.68097 4.23105C6.32243 3.8725 5.83556 3.67233 5.32878 3.67504Z" />
                        <path
                            d="M7.49796 9.34314H3.09625C2.91725 9.34314 2.77191 9.19798 2.77191 9.0188C2.77191 8.8398 2.91725 8.69446 3.09625 8.69446H7.49796C7.67714 8.69446 7.8223 8.8398 7.8223 9.0188C7.8223 9.19798 7.67714 9.34314 7.49796 9.34314Z" />
                        <path
                            d="M5.69094 10.7332H3.09625C2.91725 10.7332 2.77191 10.588 2.77191 10.4088C2.77191 10.2298 2.91725 10.0845 3.09625 10.0845H5.69094C5.87013 10.0845 6.01528 10.2298 6.01528 10.4088C6.01528 10.588 5.87013 10.7332 5.69094 10.7332Z" />
                        <path
                            d="M17.7296 0.076416H2.27036C1.01699 0.0778639 0.00144793 1.09359 0 2.34677V13.6633C0.00144793 14.9166 1.01699 15.9322 2.27036 15.9336H9.58296V19.0733H6.17163C5.94123 19.0733 5.75463 19.2601 5.75463 19.4903C5.75463 19.7207 5.94123 19.9073 6.17163 19.9073H14.05C14.2804 19.9073 14.467 19.7207 14.467 19.4903C14.467 19.2601 14.2804 19.0733 14.05 19.0733H10.417V15.9336H17.7296C18.9829 15.9322 19.9985 14.9166 19.9999 13.6633V2.34677C19.9985 1.09359 18.9829 0.0778639 17.7296 0.076416V0.076416ZM2.27036 0.910425H17.7296C18.5225 0.91133 19.165 1.55385 19.1659 2.34677V11.6097H0.834009V2.34677C0.834914 1.55385 1.47743 0.91133 2.27036 0.910425ZM17.7296 15.0996H2.27036C1.47743 15.0987 0.834914 14.4562 0.834009 13.6633V12.4438H19.1659V13.6633C19.165 14.4562 18.5225 15.0987 17.7296 15.0996Z" />
                        <path
                            d="M10.0323 10.6869H16.9176C17.0968 10.6869 17.242 10.5417 17.242 10.3625V6.2401C17.242 6.06092 17.0968 5.91576 16.9176 5.91576H14.6735V4.01698C14.6735 3.83798 14.5282 3.69265 14.3492 3.69265H13.9182V3.14786C13.9182 2.96886 13.7731 2.82352 13.5939 2.82352H12.2703V2.02246C12.2703 1.84346 12.1252 1.69812 11.946 1.69812C11.767 1.69812 11.6216 1.84346 11.6216 2.02246V2.82352H10.788C10.6088 2.82352 10.4637 2.96886 10.4637 3.14786V3.69265H10.0327C9.85353 3.69265 9.70837 3.83798 9.70837 4.01698V10.3625C9.70837 10.5415 9.85335 10.6867 10.0323 10.6869V10.6869ZM16.5933 6.56444V10.0382H14.6735V6.56444H16.5933ZM12.7298 10.0382H11.6519V9.12637H12.7298V10.0382ZM11.1123 3.4722H13.2694V3.69265H11.1123V3.4722ZM10.357 4.34132H14.0248V10.0382H13.3785V8.80203C13.3785 8.62285 13.2334 8.4777 13.0542 8.4777H11.3275C11.1483 8.4777 11.0032 8.62285 11.0032 8.80203V10.0382H10.3567L10.357 4.34132Z" />
                        <path
                            d="M4.72603 6.30141C4.84042 6.43534 5.04077 6.45344 5.17742 6.34231L6.42518 5.32713C6.56418 5.21419 6.58517 5.00985 6.47205 4.87085C6.35893 4.73185 6.15477 4.71086 6.01577 4.82397L5.01362 5.63952L4.6835 5.25292C4.5673 5.11664 4.36242 5.10053 4.22631 5.21673C4.09003 5.3331 4.07392 5.53781 4.1903 5.67409L4.72603 6.30141Z" />
                    </svg>
                    <h6>{{ translate('Booking') }}</h6>
                </a>
            </li>

            <li class="{{ Request::routeIs('customer.deposit') ? 'active' : '' }}">
                <a href="{{ route('customer.deposit') }}">
                    <svg width="18" height="18" viewBox="0 0 640 512" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M535 41c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l64 64c4.5 4.5 7 10.6 7 17s-2.5 12.5-7 17l-64 64c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l23-23L384 112c-13.3 0-24-10.7-24-24s10.7-24 24-24l174.1 0L535 41zM105 377l-23 23L256 400c13.3 0 24 10.7 24 24s-10.7 24-24 24L81.9 448l23 23c9.4 9.4 9.4 24.6 0 33.9s-24.6 9.4-33.9 0L7 441c-4.5-4.5-7-10.6-7-17s2.5-12.5 7-17l64-64c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9zM96 64l241.9 0c-3.7 7.2-5.9 15.3-5.9 24c0 28.7 23.3 52 52 52l117.4 0c-4 17 .6 35.5 13.8 48.8c20.3 20.3 53.2 20.3 73.5 0L608 169.5 608 384c0 35.3-28.7 64-64 64l-241.9 0c3.7-7.2 5.9-15.3 5.9-24c0-28.7-23.3-52-52-52l-117.4 0c4-17-.6-35.5-13.8-48.8c-20.3-20.3-53.2-20.3-73.5 0L32 342.5 32 128c0-35.3 28.7-64 64-64zm64 64l-64 0 0 64c35.3 0 64-28.7 64-64zM544 320c-35.3 0-64 28.7-64 64l64 0 0-64zM320 352a96 96 0 1 0 0-192 96 96 0 1 0 0 192z" />

                    </svg>
                    <h6>{{ translate('Deposits') }}</h6>
                </a>
            </li>


            <li class="{{ Request::routeIs('customer.transaction') ? 'active' : '' }}">
                <a href="{{ route('customer.transaction') }}">
                    <svg width="18" height="18" viewBox="0 0 576 512" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M488.1 6.2c-9.9-8.9-25-8.1-33.9 1.8s-8.1 25 1.8 33.9L489.5 72 86.5 72l33.5-30.2c9.9-8.9 10.7-24 1.8-33.9S97.8-2.7 87.9 6.2l-80 72C2.9 82.7 0 89.2 0 96s2.9 13.3 7.9 17.8l80 72c9.9 8.9 25 8.1 33.9-1.8s8.1-25-1.8-33.9L86.5 120l402.9 0-33.5 30.2c-9.9 8.9-10.7 24-1.8 33.9s24 10.7 33.9 1.8l80-72c5.1-4.6 7.9-11 7.9-17.8s-2.9-13.3-7.9-17.8l-80-72zM307.4 166.5c-11.5-8.7-27.3-8.7-38.8 0l-168 128c-6.6 5-11 12.5-12.3 20.7l-24 160c-1.4 9.2 1.3 18.6 7.4 25.6S86.7 512 96 512l144 0 16 0c17.7 0 32-14.3 32-32l0-118.1c0-5.5 4.4-9.9 9.9-9.9c3.7 0 7.2 2.1 8.8 5.5l68.4 136.8c5.4 10.8 16.5 17.7 28.6 17.7l60.2 0 16 0c9.3 0 18.2-4.1 24.2-11.1s8.8-16.4 7.4-25.6l-24-160c-1.2-8.2-5.6-15.7-12.3-20.7l-168-128z">
                        </path>
                        <path
                            d="M488.1 6.2c-9.9-8.9-25-8.1-33.9 1.8s-8.1 25 1.8 33.9L489.5 72 86.5 72l33.5-30.2c9.9-8.9 10.7-24 1.8-33.9S97.8-2.7 87.9 6.2l-80 72C2.9 82.7 0 89.2 0 96s2.9 13.3 7.9 17.8l80 72c9.9 8.9 25 8.1 33.9-1.8s8.1-25-1.8-33.9L86.5 120l402.9 0-33.5 30.2c-9.9 8.9-10.7 24-1.8 33.9s24 10.7 33.9 1.8l80-72c5.1-4.6 7.9-11 7.9-17.8s-2.9-13.3-7.9-17.8l-80-72zM307.4 166.5c-11.5-8.7-27.3-8.7-38.8 0l-168 128c-6.6 5-11 12.5-12.3 20.7l-24 160c-1.4 9.2 1.3 18.6 7.4 25.6S86.7 512 96 512l144 0 16 0c17.7 0 32-14.3 32-32l0-118.1c0-5.5 4.4-9.9 9.9-9.9c3.7 0 7.2 2.1 8.8 5.5l68.4 136.8c5.4 10.8 16.5 17.7 28.6 17.7l60.2 0 16 0c9.3 0 18.2-4.1 24.2-11.1s8.8-16.4 7.4-25.6l-24-160c-1.2-8.2-5.6-15.7-12.3-20.7l-168-128z">
                        </path>
                    </svg>
                    <h6>{{ translate('Transaction') }}</h6>
                </a>
            </li>
            <li class="{{ Request::routeIs('customer.profile') ? 'active' : '' }}">
                <a href="{{ route('customer.profile') }}">
                    <svg width="18" height="18" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M29.5 17.5001C29.6326 17.5001 29.7598 17.4474 29.8536 17.3537C29.9473 17.2599 30 17.1327 30 17.0001V13.0001C30 12.8675 29.9473 12.7403 29.8536 12.6466C29.7598 12.5528 29.6326 12.5001 29.5 12.5001H26.7335C26.4296 11.0662 25.864 9.70053 25.065 8.47162L27.019 6.51312C27.1126 6.41938 27.1651 6.29233 27.1651 6.15987C27.1651 6.02741 27.1126 5.90037 27.019 5.80662L24.194 2.98162C24.1003 2.88804 23.9732 2.83548 23.8407 2.83548C23.7083 2.83548 23.5812 2.88804 23.4875 2.98162L21.5285 4.93512C20.2996 4.13615 18.9339 3.57054 17.5 3.26662V0.500122C17.5 0.367514 17.4473 0.240337 17.3536 0.146569C17.2598 0.0528005 17.1326 0.00012207 17 0.00012207H13C12.8674 0.00012207 12.7402 0.0528005 12.6464 0.146569C12.5527 0.240337 12.5 0.367514 12.5 0.500122V3.26662C11.0661 3.57054 9.70041 4.13615 8.4715 4.93512L6.513 2.98112C6.41926 2.88754 6.29221 2.83498 6.15975 2.83498C6.02729 2.83498 5.90024 2.88754 5.8065 2.98112L2.9815 5.80612C2.88792 5.89987 2.83536 6.02691 2.83536 6.15937C2.83536 6.29183 2.88792 6.41888 2.9815 6.51262L4.9355 8.47112C4.1363 9.70015 3.57051 11.066 3.2665 12.5001H0.5C0.367392 12.5001 0.240215 12.5528 0.146447 12.6466C0.0526784 12.7403 0 12.8675 0 13.0001V17.0001C0 17.1327 0.0526784 17.2599 0.146447 17.3537C0.240215 17.4474 0.367392 17.5001 0.5 17.5001H3.2665C3.57042 18.9341 4.13603 20.2997 4.935 21.5286L2.981 23.4871C2.88742 23.5809 2.83486 23.7079 2.83486 23.8404C2.83486 23.9728 2.88742 24.0999 2.981 24.1936L5.806 27.0186C5.89974 27.1122 6.02679 27.1648 6.15925 27.1648C6.29171 27.1648 6.41876 27.1122 6.5125 27.0186L8.471 25.0646C9.70002 25.8638 11.0658 26.4296 12.5 26.7336V29.5001C12.5 29.6327 12.5527 29.7599 12.6464 29.8537C12.7402 29.9474 12.8674 30.0001 13 30.0001H17C17.1326 30.0001 17.2598 29.9474 17.3536 29.8537C17.4473 29.7599 17.5 29.6327 17.5 29.5001V26.7336C18.9335 26.4285 20.2986 25.8621 21.527 25.0626L25.6065 29.1421C26.076 29.6076 26.7108 29.8681 27.372 29.8666C28.0331 29.8652 28.6668 29.6019 29.1343 29.1344C29.6018 28.6669 29.8651 28.0332 29.8665 27.3721C29.868 26.711 29.6074 26.0762 29.142 25.6066L25.0625 21.5271C25.8619 20.2987 26.4284 18.9336 26.7335 17.5001H29.5ZM28.435 28.4351C28.2957 28.5744 28.1303 28.6849 27.9483 28.7603C27.7663 28.8357 27.5713 28.8746 27.3743 28.8746C27.1772 28.8746 26.9822 28.8357 26.8002 28.7603C26.6182 28.6849 26.4528 28.5744 26.3135 28.4351L16.7395 18.8616C16.507 18.6294 16.2308 18.4455 15.9268 18.3205C15.6228 18.1956 15.2971 18.1321 14.9685 18.1336C14.8642 18.1336 14.76 18.1398 14.6565 18.1521C14.0078 18.23 13.3499 18.1477 12.7404 17.9124C12.1309 17.6771 11.5884 17.2959 11.1604 16.8022C10.7324 16.3086 10.4319 15.7176 10.2853 15.0809C10.1387 14.4442 10.1505 13.7812 10.3195 13.1501L12.525 15.3561C12.6187 15.4499 12.7459 15.5026 12.8785 15.5026H15C15.1326 15.5026 15.2598 15.4499 15.3536 15.3562C15.4473 15.2624 15.5 15.1352 15.5 15.0026V12.8786C15.5 12.746 15.4473 12.6189 15.3535 12.5251L13.15 10.3196C13.781 10.151 14.4437 10.1395 15.0801 10.2863C15.7165 10.4331 16.3073 10.7336 16.8006 11.1616C17.294 11.5895 17.675 12.1319 17.9102 12.7412C18.1455 13.3505 18.2278 14.0081 18.15 14.6566C18.1018 15.0349 18.1406 15.4193 18.2636 15.7803C18.3865 16.1413 18.5904 16.4694 18.8595 16.7396L28.435 26.3136C28.5743 26.4529 28.6848 26.6183 28.7602 26.8003C28.8356 26.9823 28.8744 27.1774 28.8744 27.3744C28.8744 27.5714 28.8356 27.7665 28.7602 27.9485C28.6848 28.1305 28.5743 28.2958 28.435 28.4351ZM19.5685 16.0351C19.4068 15.8717 19.2845 15.6734 19.2112 15.4554C19.1379 15.2374 19.1154 15.0056 19.1455 14.7776C19.2526 13.8889 19.1191 12.9877 18.7587 12.1682C18.3983 11.3488 17.8244 10.6412 17.097 10.1195C16.3696 9.59772 15.5154 9.28099 14.6237 9.20238C13.7319 9.12378 12.8355 9.28619 12.028 9.67262C11.9559 9.70721 11.8932 9.75857 11.8451 9.82239C11.797 9.88622 11.7649 9.96065 11.7515 10.0394C11.738 10.1182 11.7437 10.1991 11.768 10.2753C11.7923 10.3514 11.8345 10.4206 11.891 10.4771L14.5 13.0856V14.5001H13.0855L10.477 11.8911C10.4205 11.8346 10.3513 11.7924 10.2751 11.7681C10.199 11.7438 10.1181 11.7382 10.0393 11.7516C9.96053 11.765 9.8861 11.7971 9.82227 11.8452C9.75845 11.8933 9.70709 11.9561 9.6725 12.0281C9.28618 12.8354 9.12379 13.7315 9.20228 14.623C9.28078 15.5145 9.59729 16.3685 10.1187 17.0958C10.6402 17.8231 11.3474 18.397 12.1665 18.7575C12.9856 19.118 13.8865 19.2519 14.775 19.1451C15.003 19.1152 15.2349 19.1377 15.4528 19.2111C15.6708 19.2845 15.8691 19.4068 16.0325 19.5686L18.6045 22.1401C16.9054 22.9985 14.956 23.2232 13.1061 22.774C11.2562 22.3249 9.62694 21.2311 8.51067 19.6891C7.39441 18.1471 6.8642 16.2578 7.0152 14.3601C7.1662 12.4625 7.98851 10.6808 9.33459 9.33471C10.6807 7.98863 12.4624 7.16632 14.36 7.01532C16.2577 6.86432 18.147 7.39453 19.689 8.5108C21.231 9.62706 22.3247 11.2563 22.7739 13.1062C23.2231 14.9561 22.9984 16.9055 22.14 18.6046L19.5685 16.0351ZM25.8275 16.9181C25.5864 18.2981 25.0815 19.6187 24.3405 20.8076L22.8785 19.3456C23.9274 17.4448 24.2548 15.2292 23.8004 13.1063C23.3461 10.9833 22.1405 9.09583 20.4054 7.79091C18.6703 6.48599 16.5224 5.85141 14.3567 6.00388C12.1911 6.15634 10.1533 7.0856 8.61812 8.62074C7.08298 10.1559 6.15372 12.1937 6.00125 14.3593C5.84879 16.525 6.48337 18.6729 7.78829 20.408C9.0932 22.1431 10.9807 23.3487 13.1036 23.8031C15.2266 24.2575 17.4422 23.9301 19.343 22.8811L20.805 24.3431C19.6161 25.0841 18.2955 25.589 16.9155 25.8301C16.7997 25.85 16.6946 25.91 16.6186 25.9996C16.5427 26.0892 16.5007 26.2026 16.5 26.3201V29.0001H13.5V26.3201C13.4999 26.2022 13.4582 26.0881 13.3822 25.998C13.3061 25.9079 13.2007 25.8475 13.0845 25.8276C11.5072 25.5515 10.0102 24.9311 8.7 24.0106C8.60371 23.943 8.48664 23.9114 8.36939 23.9216C8.25214 23.9317 8.14223 23.9829 8.059 24.0661L6.163 25.9586L4.044 23.8396L5.9365 21.9436C6.01971 21.8604 6.0709 21.7505 6.08105 21.6332C6.0912 21.516 6.05967 21.3989 5.992 21.3026C5.07114 19.9917 4.45074 18.4938 4.175 16.9156C4.155 16.799 4.09429 16.6932 4.00365 16.6172C3.91301 16.5411 3.79833 16.4996 3.68 16.5001H1V13.5001H3.68C3.7979 13.5 3.91199 13.4583 4.00211 13.3823C4.09222 13.3063 4.15257 13.2008 4.1725 13.0846C4.44859 11.5074 5.06898 10.0103 5.9895 8.70012C6.05717 8.60383 6.0887 8.48676 6.07855 8.36951C6.0684 8.25226 6.01721 8.14235 5.934 8.05912L4.0415 6.16312L6.1605 4.04412L8.0565 5.93662C8.13973 6.01983 8.24964 6.07102 8.36689 6.08117C8.48414 6.09132 8.60121 6.05979 8.6975 5.99212C10.0085 5.07127 11.5064 4.45086 13.0845 4.17512C13.2011 4.15513 13.3069 4.09441 13.383 4.00377C13.4591 3.91314 13.5005 3.79845 13.5 3.68012V1.00012H16.5V3.68012C16.5001 3.79803 16.5418 3.91211 16.6178 4.00223C16.6939 4.09235 16.7993 4.1527 16.9155 4.17262C18.4936 4.44836 19.9916 5.06877 21.3025 5.98962C21.3988 6.05729 21.5159 6.08882 21.6331 6.07867C21.7504 6.06852 21.8603 6.01733 21.9435 5.93412L23.8395 4.04162L25.9585 6.16062L24.066 8.05662C23.9828 8.13985 23.9316 8.24976 23.9215 8.36701C23.9113 8.48426 23.9428 8.60133 24.0105 8.69762C24.9314 10.0086 25.5518 11.5065 25.8275 13.0846C25.8474 13.2008 25.9078 13.3063 25.9979 13.3823C26.088 13.4583 26.2021 13.5 26.32 13.5001H29V16.5001H26.32C26.2021 16.5002 26.088 16.5419 25.9979 16.618C25.9078 16.694 25.8474 16.7994 25.8275 16.9156V16.9181Z">
                        </path>
                        <path
                            d="M18.182 17.4751C18.1359 17.4274 18.0807 17.3893 18.0197 17.3631C17.9587 17.3369 17.8931 17.3231 17.8267 17.3225C17.7603 17.3219 17.6945 17.3346 17.6331 17.3597C17.5716 17.3849 17.5158 17.422 17.4688 17.4689C17.4219 17.5159 17.3848 17.5717 17.3596 17.6331C17.3345 17.6946 17.3218 17.7604 17.3224 17.8268C17.323 17.8932 17.3368 17.9588 17.363 18.0198C17.3892 18.0808 17.4273 18.136 17.475 18.1821L17.8285 18.5356C17.9228 18.6267 18.0491 18.6771 18.1802 18.676C18.3113 18.6748 18.4367 18.6222 18.5294 18.5295C18.6221 18.4368 18.6747 18.3114 18.6759 18.1803C18.677 18.0492 18.6266 17.9229 18.5355 17.8286L18.182 17.4751ZM25.253 25.9601C25.3473 26.0512 25.4736 26.1016 25.6047 26.1005C25.7358 26.0993 25.8612 26.0467 25.9539 25.954C26.0466 25.8613 26.0992 25.7359 26.1004 25.6048C26.1015 25.4737 26.0511 25.3474 25.96 25.2531L19.95 19.2426C19.8562 19.1488 19.729 19.0961 19.5963 19.0961C19.4636 19.0961 19.3364 19.1488 19.2425 19.2426C19.1487 19.3364 19.096 19.4637 19.096 19.5964C19.096 19.7291 19.1487 19.8563 19.2425 19.9501L25.253 25.9601ZM27.021 27.7281C27.1153 27.8192 27.2416 27.8696 27.3727 27.8685C27.5038 27.8673 27.6292 27.8147 27.7219 27.722C27.8146 27.6293 27.8672 27.5039 27.8684 27.3728C27.8695 27.2417 27.8191 27.1154 27.728 27.0211L27.3745 26.6671C27.2807 26.5733 27.1535 26.5206 27.0208 26.5206C26.8881 26.5206 26.7609 26.5733 26.667 26.6671C26.5732 26.7609 26.5205 26.8882 26.5205 27.0209C26.5205 27.1536 26.5732 27.2808 26.667 27.3746L27.021 27.7281Z">
                        </path>
                    </svg>
                    <h6>{{ translate('My Profile') }}</h6>
                </a>
            </li>
            <li>
                <a href="{{ route('logout') }}"
                    onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
<form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
    @csrf
</form>
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                        <g clip-path="url(#clip0_998_2016)">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.75 14.0625C6.75 14.2117 6.80926 14.3548 6.91475 14.4602C7.02024 14.5657 7.16332 14.625 7.3125 14.625H16.3125C16.4617 14.625 16.6048 14.5657 16.7102 14.4602C16.8157 14.3548 16.875 14.2117 16.875 14.0625V3.9375C16.875 3.78832 16.8157 3.64524 16.7102 3.53975C16.6048 3.43426 16.4617 3.375 16.3125 3.375H7.3125C7.16332 3.375 7.02024 3.43426 6.91475 3.53975C6.80926 3.64524 6.75 3.78832 6.75 3.9375V6.1875C6.75 6.33668 6.69074 6.47976 6.58525 6.58525C6.47976 6.69074 6.33668 6.75 6.1875 6.75C6.03832 6.75 5.89524 6.69074 5.78975 6.58525C5.68426 6.47976 5.625 6.33668 5.625 6.1875V3.9375C5.625 3.48995 5.80279 3.06072 6.11926 2.74426C6.43572 2.42779 6.86495 2.25 7.3125 2.25H16.3125C16.7601 2.25 17.1893 2.42779 17.5057 2.74426C17.8222 3.06072 18 3.48995 18 3.9375V14.0625C18 14.5101 17.8222 14.9393 17.5057 15.2557C17.1893 15.5722 16.7601 15.75 16.3125 15.75H7.3125C6.86495 15.75 6.43572 15.5722 6.11926 15.2557C5.80279 14.9393 5.625 14.5101 5.625 14.0625V11.8125C5.625 11.6633 5.68426 11.5202 5.78975 11.4148C5.89524 11.3093 6.03832 11.25 6.1875 11.25C6.33668 11.25 6.47976 11.3093 6.58525 11.4148C6.69074 11.5202 6.75 11.6633 6.75 11.8125V14.0625Z" />
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M0.164279 9.39823C0.111895 9.34598 0.0703346 9.2839 0.0419773 9.21557C0.0136201 9.14723 -0.000976563 9.07397 -0.000976562 8.99998C-0.000976563 8.92599 0.0136201 8.85273 0.0419773 8.78439C0.0703346 8.71605 0.111895 8.65398 0.164279 8.60173L3.53928 5.22673C3.59158 5.17443 3.65367 5.13294 3.722 5.10464C3.79033 5.07634 3.86357 5.06177 3.93753 5.06177C4.01149 5.06177 4.08473 5.07634 4.15306 5.10464C4.22139 5.13294 4.28348 5.17443 4.33578 5.22673C4.38808 5.27903 4.42956 5.34111 4.45787 5.40945C4.48617 5.47778 4.50074 5.55102 4.50074 5.62498C4.50074 5.69894 4.48617 5.77218 4.45787 5.84051C4.42956 5.90884 4.38808 5.97093 4.33578 6.02323L1.9204 8.43748H11.8125C11.9617 8.43748 12.1048 8.49674 12.2103 8.60223C12.3158 8.70772 12.375 8.85079 12.375 8.99998C12.375 9.14916 12.3158 9.29224 12.2103 9.39773C12.1048 9.50322 11.9617 9.56248 11.8125 9.56248H1.9204L4.33578 11.9767C4.38808 12.029 4.42956 12.0911 4.45787 12.1594C4.48617 12.2278 4.50074 12.301 4.50074 12.375C4.50074 12.4489 4.48617 12.5222 4.45787 12.5905C4.42956 12.6588 4.38808 12.7209 4.33578 12.7732C4.28348 12.8255 4.22139 12.867 4.15306 12.8953C4.08473 12.9236 4.01149 12.9382 3.93753 12.9382C3.86357 12.9382 3.79033 12.9236 3.722 12.8953C3.65367 12.867 3.59158 12.8255 3.53928 12.7732L0.164279 9.39823Z" />
                        </g>
                    </svg>
                    <h6>{{ translate('Log Out') }}</h6>
                </a>
            </li>
        </ul>

    </div>
</div>
