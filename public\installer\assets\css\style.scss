@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100;0,9..40,200;0,9..40,300;0,9..40,400;0,9..40,500;0,9..40,600;0,9..40,700;0,9..40,800;0,9..40,900;0,9..40,1000;1,9..40,100;1,9..40,200;1,9..40,300;1,9..40,400;1,9..40,500;1,9..40,600;1,9..40,700;1,9..40,800;1,9..40,900;1,9..40,1000&family=Lexend:wght@100;200;300;400;500;600;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

:root {
  --title-color: #000;
  --primary-color: #8E44EC;
  --text-color: #595959;
  --white:#FFFFFF;

  --font-lexend: 'Lexend', sans-serif;
  --font-dm-sans: 'DM Sans', sans-serif;
}


/*================================================
1. Mixins Css
=================================================*/

// xl-device=====

@mixin xxl-down-device {
  @media (max-width: 1399px) {
    @content;
  }
}

@mixin xl-down-device {
  @media (max-width: 1199px) {
    @content;
  }
}

@mixin xxl-device {
  @media (min-width: 1400px) and (max-width: 1599px) {
    @content;
  }
}

@mixin xl-device {
  @media (min-width: 1200px) and (max-width: 1399px) {
    @content;
  }
}

@mixin lg-device {
  @media (min-width: 992px) and (max-width: 1199px) {
    @content;
  }
}

@mixin lg-up-device {
  @media (min-width: 992px) {
    @content;
  }
}

@mixin lg-down-device {
  @media (max-width: 991px) {
    @content;
  }
}

// md-device============
@mixin md-device {
  @media (min-width: 768px) and (max-width: 991px) {
    @content;
  }
}

@mixin xxl-up-device {
  @media (min-width: 1600px) {
    @content;
  }
}

@mixin md-up-device {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin md-down-device {
  @media (max-width: 767px) {
    @content;
  }
}

// sm-device
@mixin sm-device {
  @media (min-width: 576px) and (max-width: 768px) {
    @content;
  }
}

@mixin sm-down-device {
  @media (max-width: 576px) {
    @content;
  }
}

@mixin threefifty-down-device() {
  @media (max-width: 350px) {
    @content;
  }
}


/*================================================
2. Global Css
=================================================*/
html {
  font-size: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  color: var(--title-color);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  box-sizing: border-box;
}

h1,h2,h3,h4,h5,h6{
  font-weight: 700;
  color: var(--title-color);
  font-family: var(--font-lexend);
}

button {
  outline: none;
  border: none;
}

i.bx {
  vertical-align: middle;
}

img{
  max-width: 100%;
  height: auto;
}

a{
  text-decoration: none;
}

.pt-40{
  padding-top: 40px;
}
.pb-40{
  padding-bottom: 40px;
}
.pt-70{
  padding-top: 70px;
}
.mb-140{
  margin-bottom: 140px;

  @include lg-down-device(){
    margin-bottom: 30px;
  }
}

.pt-50 {
  padding-top: 50px;
}
.pt-55 {
  padding-top: 55px;
}
.pb-55 {
  padding-bottom: 55px;
}
.pt-100 {
  padding-top: 100px;
}
.pb-100 {
  padding-bottom: 100px;
}
.pb-70 {
  padding-bottom: 70px;
}
.mb-100 {
  margin-bottom: 100px;

  @include md-down-device(){
    margin-bottom: 40px;
  }
}
.mb-60 {
  margin-bottom: 60px;
  @include md-down-device() {
    margin-bottom: 40px;
  }
}
.mb-65 {
  margin-bottom: 65px;
  @include md-down-device() {
    margin-bottom: 40px;
  }
}



.mt-120 {
  margin-top: 120px;

  @include lg-device() {
    margin-top: 100px;
  }

  @include lg-down-device() {
    margin-top: 90px;
  }
}
.mb-120 {
  margin-bottom: 120px;
}
.mb-100 {
  margin-bottom: 100px;
  @include md-down-device(){
    margin-bottom: 40px;
  }
}
.mb-15 {
  margin-bottom: 15px;
}




.mb-70 {
  margin-bottom: 70px;
  @include md-down-device() {
    margin-bottom: 40px;
  }
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-44 {
  margin-bottom: 44px;

  @include lg-down-device(){
    margin-bottom: 0px;
  }
}
.mb-35 {
  margin-bottom: 35px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mt-15 {
  margin-top: 15px;
}
.mt-30 {
  margin-top: 30px !important;
}

.mt-40 {
  margin-top: 40px;
}
.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-30 {
  margin-bottom: 30px;
}
.mb-25 {
  margin-bottom: 25px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mt-60 {
  margin-top: 60px;
  @include md-down-device() {
    margin-top: 40px;
  }
}

.mt-65 {
  margin-top: 65px;
  @include md-down-device() {
    margin-top: 45px;
  }
}

.mt-70 {
  margin-top: 70px;
  @include md-down-device() {
    margin-top: 40px;
  }
}
.padding-left-right{
  padding: 0 8%;
  @include md-down-device(){
    padding: 0;
  }
}



.step-btn-group{
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding-top: 50px;
  @include xl-down-device(){
    padding-top: 30px;
  }
  .previous-btn{
    border-radius: 10px;
    background-color: var(--white);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    font-family: var(--font-dm-sans);
    font-size: 16px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 38px;
    cursor: pointer;
    svg{
      fill: var(--primary-color);
    }
  }
  .next-btn{
    border-radius: 10px;
    background: #151515;
    color: var(--white);
    font-family: var(--font-dm-sans);
    font-size: 16px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 16px 38px;
    cursor: pointer;
    svg{
      fill: var(--white);
    }
  }
}
.installment-section{
  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.25) 100%), url(../images/bg.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: right;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  .installment-wrap{
    max-width: 1170px;
    width: 100%;
    background-color: #F7F7F7;
    border-radius: 10px;
    padding: 35px;
    display: flex;
    gap: 30px;
    @include xl-down-device(){
      padding: 10px;
    }
    @include lg-down-device(){
      flex-wrap: wrap;
    }
    .installment-form-step{
      margin: 0;
      padding: 0;
      list-style: none;
      width: 290px;
      background-color: var(--white);
      border-radius: 5px;
      padding: 50px 30px;
      @include xl-down-device(){
        padding: 30px 20px;
      }
      li{
        line-height: 1;
        display: flex;
        align-items: center;
        gap: 15px;
        width: 100%;
        margin-bottom: 40px;
        &:last-child{
          margin-bottom: 0;
          .icon{
            &::after{
              display: none;
              visibility: hidden;
            }
          }
        }
        .icon{
          height: 40px;
          min-width: 40px;
          border-radius: 50%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          justify-content: center;
          background-color: #F5E6FF;
          position: relative;
          svg{
            fill: #151515;
          }
          &::after{
            content: '';
            height: 40px;
            width: 2px;
            background-color: #eee;
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
        p{
          margin-bottom: 0;
          color: var(--text-color);
          font-family: var(--font-lexend);
          font-size: 16px;
          font-weight: 500;
          width: 100%;
        }
        .check{
          min-width: 8px;
          max-width: 8px;
          height: 8px;
          border-radius: 50%;
          border: 5px solid #BDBDBD;
          svg{
            opacity: 0;
          }
        }
        &.processing{
          .icon{
            background-color: var(--title-color);
            svg{
              fill: var(--white);
            }
            &::after{
              background-color: var(--title-color);
            }
          }
        }
        &.active{
          .icon{
            background-color: var(--primary-color);
            svg{
              fill: var(--white);
            }
            &::after{
              background-color: var(--primary-color);
            }
          }
          p{
            color: var(--title-color);
          }
          .check{
            min-width: 18px;
            max-width: 18px;
            height: 18px;
            border: 1px solid #63AB45;
            background-color: #63AB45;
            display: flex;
            align-items: center;
            justify-content: center;
            svg{
              opacity: 1;
              fill: var(--white);
            }
            &.red{
              background-color: red;
              border-color: red;
            }
          }
        }
      }
    }
    .installment-form{
      height: 100%;
      width: 100%;
      border-radius: 5px;
      background: var(--white);
      padding: 50px 50px;
      min-height: 540px;
      @include xl-down-device(){
        padding: 30px 25px;
      }
    }

    fieldset{
      opacity: 0;
      display: none;
      border: none;
      height: 100%;
      width: 100%;
      flex-direction: column;
      justify-content: space-between;
      &.active{
        opacity: 1;
        display: flex;
      }
      .step-title{
        h3{
          color: var(--title-color);
          font-family: var(--font-lexend);
          font-size: 36px;
          font-weight: 700;
          margin-bottom: 0;
        }
        p{
          color: var(--text-color);
          font-family: var(--font-dm-sans);
          font-size: 16px;
          font-weight: 500;
          line-height: 26px;
          margin-bottom: 0;
          padding-top: 10px;
        }
      }
      .installment-table{
        border-radius: 10px;
        border: 1px solid #EEE;
        padding: 5px;
        border-spacing: 0;
        width:100%;

        tbody{
          tr{
            background: #FFF6FF;

            td{
              color: var(--text-color);
              font-family: var(--font-lexend);
              font-size: 15px;
              font-weight: 400;
              border: unset;
              padding: 16px 25px;
              border-bottom: 5px solid #fff;
              strong{
                color: #151515;
                font-weight: 500;
              }
              span{
                color: #151515;
              }
              .vertion-and-stutas{
                display: flex;
                align-items: center;
                justify-content: end;
                gap: 10px;
                .check{
                  height: 14px;
                  width: 14px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: #63AB45;
                  svg{
                    fill: var(--white);
                  }
                  &.red{
                    background-color: red;
                  }
                }
              }
              &:first-child{
                border-radius: 5px 0 0 5px;
              }
              :last-child{
                border-radius: 0 5px 5px 0;
              }
            }
            &:last-child{
              td{
                border-bottom: none;
              }
            }
          }
        }
      }
      .enviroment-wrap{
        border-radius: 10px;
        border: 1px solid #EEE;
        padding: 20px;
        .app-debug{
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 20px;
          margin-bottom: 35px;
          h6{
            margin-bottom: 0;
            color: #151515;
            font-family: var(--font-lexend);
            font-size: 15px;
            font-weight: 400;
          }
          .form-check-wrap{
            display: flex;
            align-items: center;
            gap: 15px;
            .form-check{
              position: relative;
              .form-check-input{
                appearance: none;
              }
              .form-check-label{
                color: #151515;
                font-family: var(--font-lexend);
                font-size: 14px;
                font-weight: 500;
                border-radius: 22px;
                background: #F7F7F7;
                padding: 6px 16px 6px 40px;
                line-height: 1;
                position: relative;
                &::before{
                  content: '';
                  height: 14px;
                  width: 14px;
                  border-radius: 50%;
                  border: 1px solid var(--primary-color);
                  position: absolute;
                  left: 16px;
                  top: 50%;
                  transform: translateY(-50%);
                }
              }
              .form-check-input:checked[type=radio] ~ .form-check-label{
                background-color: var(--primary-color);
                color: var(--white);
                cursor: pointer;
                &::before{
                  content: url(../images/check.svg);
                  border: 1px solid #fff;
                  background-color: var(--white);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  line-height: 2px;
                }
              }
            }
          }
        }
        .form-input-area{
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          gap: 30px;
          .form-inner{
            border: 1px solid #eee;
            position: relative;
            border-radius: 5px;
            label{
              color: #151515;
              font-family: var(--font-lexend);
              font-size: 15px;
              font-weight: 400;
              background-color: #fff;
              line-height: 1;
              position: absolute;
              left: 15px;
              top: -7.5px;
              padding: 0 15px;
              &::before{
                content: '';
                clip-path: polygon(0 0, 0% 100%, 100% 50%);
                background-color: #eee;
                width: 10px;
                height: 10px;
                display: inline-block;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                left: 0;
              }
              &:after{
                content: '';
                clip-path: polygon(100% 0, 100% 100%, 0 50%);
                background-color: #eee;
                width: 10px;
                height: 10px;
                display: inline-block;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                right: 0;
              }
            }
            input{
              border: none;
              outline: none;
              height: 50px;
              padding: 0px 20px;
              background-color: transparent;
              width: 100%;
              color: var(--text-color);
              font-family: var(--font-dm-sans);
              font-size: 14px;
              font-weight: 500;
                &::placeholder{
                  color: #a09c9c;
                }
            }
            textarea{
              border: none;
              outline: none;
              min-height: 150px;
              padding: 20px 20px;
              background-color: transparent;
              width: 100%;
              color: var(--text-color);
              font-family: var(--font-dm-sans);
              font-size: 14px;
              font-weight: 500;
                &::placeholder{
                  color: #a09c9c;
                }
            }
            select{
              border: none;
              outline: none;
              height: 50px;
              padding: 0px 20px;
              background-color: transparent;
              width: 100%;
              color: var(--text-color);
              font-family: var(--font-dm-sans);
              font-size: 14px;
              font-weight: 500;
              position: relative;
            }
          }
          .w-50{
            width: 47%;
            @include xl-down-device(){
              width: 46%;
            }
            @include lg-down-device(){
              width: 100%;
            }
          }
          .w-100{
            width: 100%;
          }
        }
      }
    }
  }
}
