
<div class="blog-standard-wrapper">
    <?php if(isset($blogs) && $blogs->count() > 0): ?>
        <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <article class="blog-card-modern mb-60">
                
                <div class="blog-image-container">
                    <?php if($blog->image): ?>
                        <img src="<?php echo e(asset('uploads/blog/' . $blog->image)); ?>" alt="<?php echo e($blog->title); ?>" class="blog-featured-image">
                    <?php else: ?>
                        <div class="blog-image-placeholder">
                            <i class="bi bi-image"></i>
                            <span><?php echo e(translate('No Image')); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    
                    <div class="date-badge">
                        <div class="date-day"><?php echo e($blog->created_at->format('d')); ?></div>
                        <div class="date-month"><?php echo e($blog->created_at->format('M')); ?></div>
                    </div>
                </div>
                
                
                <div class="blog-content-area">
                    
                    <div class="blog-stats">
                        <span class="stat-item">
                            <i class="bi bi-eye"></i>
                            <?php echo e(rand(500, 2000)); ?> <?php echo e(translate('Views')); ?>

                        </span>
                        <span class="stat-separator">|</span>
                        <span class="stat-item">
                            <i class="bi bi-clock"></i>
                            <?php echo e(rand(1, 10)); ?> <?php echo e(translate('Min Read')); ?>

                        </span>
                        <span class="stat-separator">|</span>
                        <span class="stat-item">
                            <i class="bi bi-chat"></i>
                            <?php
                                $commentCount = App\Models\BlogComment::where('blog_id', $blog->id)->count();
                            ?>
                            (<?php echo e($commentCount); ?>) <?php echo e(translate('Comments')); ?>

                        </span>
                    </div>
                    
                    
                    <h2 class="blog-title-modern">
                        <a href="<?php echo e(route('blog.details', $blog->slug)); ?>">
                            <?php echo e($blog->getTranslation('title')); ?>

                        </a>
                    </h2>
                    
                    
                    <div class="blog-excerpt-modern">
                        <?php echo e(Str::limit(strip_tags($blog->getTranslation('description')), 200, '...')); ?>

                    </div>
                    
                    
                    <div class="blog-read-more">
                        <a href="<?php echo e(route('blog.details', $blog->slug)); ?>" class="read-more-link">
                            <?php echo e(translate('View Post')); ?>

                            <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </article>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
        
        <?php if($blogs->hasPages()): ?>
            <div class="blog-pagination">
                <?php echo e($blogs->links()); ?>

            </div>
        <?php endif; ?>
    <?php else: ?>
        
        <div class="no-blogs-found">
            <div class="empty-state">
                <i class="bi bi-journal-x"></i>
                <h4><?php echo e(translate('No Blogs Found')); ?></h4>
                <p><?php echo e(translate('There are no blog posts available at the moment.')); ?></p>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/frontend/template-1/partials/blog-standard.blade.php ENDPATH**/ ?>