@extends('backend.layouts.master')
@section('content')
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4>{{ $page_title ?? '' }}</h4>
            <a href="{{ route('activities.list') }}" class="eg-btn btn--primary back-btn"> <img
                    src="{{ asset('backend/images/icons/back.svg') }}" alt="{{ translate('Go Back') }}">
                {{ translate('Go Back') }}</a>
        </div>
    </div>
    <form action="{{route('advertisement.store')}}" method="post" enctype="multipart/form-data">
        @csrf
        <div class="row">

            <div class="col-lg-12">
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4>{{ translate('Advertisement Content') }}</h4>
                    </div>
                    <div class="form-inner mb-35">
                        <label>{{ translate('Text') }} <span class="text-danger">*</span></label>
                        <input type="text" class="username-input" value="{{ old('text') }}" name="text"
                            placeholder="{{ translate('Text of the Advertisement') }}">
                        @error('text')
                            <div class="error text-danger">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-inner mb-35">
                        <label>{{ translate('Phone') }} <span class="text-danger">*</span></label>
                        <input type="text" class="username-input" value="{{ old('phone') }}" name="phone"
                            placeholder="{{ translate('Enter your phone number') }}">
                        @error('phone')
                            <div class="error text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4>{{ translate('Advertise Image') }}</h4>
                    </div>

                    <div class="form-inner file-upload mb-35">
                        <div class="dropzone-wrapper">
                            <div class="dropzone-desc">
                                <i class="glyphicon glyphicon-download-alt"></i>
                                <p>{{ translate('Choose an image file or drag it here') }}</p>
                            </div>
                            <input type="file" name="features_image" class="dropzone featues_image">

                        </div>


                        <div class="preview-zone hidden">
                            <div class="box box-solid">
                                <div class="box-header with-border">
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-danger btn-xs remove-preview"
                                            style="display:none;">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="box-body"></div>
                            </div>
                        </div>
                    </div>
                    @error('features_image')
                        <div class="error text-danger">{{ $message }}</div>
                    @enderror
                </div>


                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4>{{ translate('Select Page') }} <span class="text-danger">*</span></h4>
                    </div>

                    <div class="form-inner">
                        <select class="js-example-basic-single @error('page') is-invalid @enderror" name="page" required>
                            <option value="">{{ translate('Select Option') }}</option>
                            <option value="{{ translate('Tour') }}">{{ translate('Tour') }}</option>
                            <option value="{{ translate('Activities') }}">{{ translate('Activities') }}</option>
                            <option value="{{ translate('Hotel') }}">{{ translate('Hotel') }}</option>
                             <option value="{{ translate('Visa') }}">{{ translate('Visa') }}</option>
                        </select>
                        @error('page')
                            <div class="error text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                </div>

<div class="col-lg-4">
                <div class="eg-card product-card pb-70">
                    <div class="button-group">
                        <button type="submit" class="radio-button">
                            <input type="radio" id="status1" name="status" value="1" />
                            <label class="eg-btn btn--green sm-medium-btn"
                                for="status1">{{ translate('Published') }}</label>
                        </button>
                        <button type="submit" class="radio-button">
                            <input type="radio" id="status2" name="status" value="2" />
                            <label class="eg-btn orange--btn sm-medium-btn"
                                for="status2">{{ translate('Save as Draft') }}</label>
                        </button>
                    </div>
                </div>


                 
        </div>

        </div>
    </form>

@endsection
