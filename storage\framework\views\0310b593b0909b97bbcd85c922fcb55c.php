<?php $__env->startSection('content'); ?>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4><?php echo e($page_title ?? ''); ?></h4>
            <a href="<?php echo e(route('languages.create')); ?>" class="eg-btn btn--primary back-btn float-end mb-3" title="Create"><img
                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>" alt="Add New"> <?php echo e(translate('Add New')); ?></a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table category-table language-table">
                    <thead>
                        <tr>
                            <th><?php echo e(translate('S.N')); ?></th>
                            <th><?php echo e(translate('Name')); ?></th>
                            <th><?php echo e(translate('Code')); ?></th>
                            <th><?php echo e(translate('RTL')); ?></th>
                            <th><?php echo e(translate('Default')); ?></th>
                            <th><?php echo e(translate('Option')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td data-label="S.N"><?php echo e(($languages->currentpage() - 1) * $languages->perpage() + $key + 1); ?>

                                </td>
                                <td data-label="Language Name"><?php echo e($lang->name); ?></td>
                                <td data-label="Language Code">
                                    <img src="<?php echo e(asset('assets/img/flags/' . $lang->code . '.png')); ?>" alt="">
                                    <?php echo e($lang->code); ?>

                                </td>
                                <td data-label="Rtl">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input flexSwitchCheckStatus"
                                            data-activations-status="<?php echo e($lang->rtl); ?>"
                                            data-id="<?php echo e($lang->id); ?>" data-type="languages" type="checkbox"
                                            id="flexSwitchCheckStatus<?php echo e($lang->id); ?>"
                                            <?php echo e($lang->rtl === 1 ? 'checked' : ''); ?>>
                                    </div>
                                </td>
                                <td data-label="Default">
                                    <form action="<?php echo e(route('backend.settings.store')); ?>" method="post">
                                        <?php echo csrf_field(); ?>
                                        <div class="form-check form-switch">
                                            <input onChange="this.form.submit()"
                                                class="form-check-input languageSwitchDefault" name="DEFAULT_LANGUAGE"
                                                value="<?php echo e($lang->code); ?>" type="checkbox"
                                                id="languageSwitchDefault<?php echo e($lang->id); ?>"
                                                <?php echo e($lang->code === get_setting('DEFAULT_LANGUAGE', 'en') ? 'checked' : ''); ?>>
                                        </div>
                                    </form>
                                </td>
                                <td data-label="Option">
                                    <div
                                        class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                        <a href="<?php echo e(route('languages.translations', $lang->id)); ?>"
                                            class="eg-btn account--btn"><i class="bi bi-info-lg"></i></i></a>
                                        <a href="<?php echo e(route('languages.edit', $lang->id)); ?>" class="eg-btn add--btn"
                                            title="Edit"><i class="bi bi-pencil-square"></i></a>
                                        <form method="POST" action="<?php echo e(route('languages.delete', $lang->id)); ?>">
                                            <?php echo csrf_field(); ?>
                                            <input name="_method" type="hidden" value="DELETE">
                                            <button type="submit" class="eg-btn delete--btn show_confirm"
                                                data-toggle="tooltip" title='Delete'><i class="bi bi-trash"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <?php $__env->startPush('footer'); ?>
        <div class="d-flex justify-content-center custom-pagination">
            <?php echo $languages->links(); ?>

        </div>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/language/index.blade.php ENDPATH**/ ?>