<?php $__env->startSection('content'); ?>
    <div class="row mb-35 g-4">
        <div class=" col-md-3">
            <div class="page-title text-md-start">
                <h4><?php echo e($page_title ?? ''); ?></h4>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
               <form id="bulkDeleteForm" method="POST" action="<?php echo e(route('transactions.bulk-delete')); ?>">
    <?php echo csrf_field(); ?>
    <?php echo method_field('DELETE'); ?>

    <button
        type="submit"
        id="bulkDeleteBtn"
        style="display: none;"
        class="eg-btn red-light--btn mb-3"
    >
        <?php echo e(translate('Delete Selected')); ?>

    </button>

    <table class="eg-table table customer-table prod-details-table">
        <thead>
            <tr>
                <th><input type="checkbox" id="select-all"></th>
                <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                    <th><?php echo e(translate('Customer Name')); ?></th>
                <?php endif; ?>
                <th><?php echo e(translate('Details')); ?></th>
                <th><?php echo e(translate('Amount')); ?></th>
                <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                    <th><?php echo e(translate('Commission')); ?></th>
                <?php endif; ?>
                <?php if (\Illuminate\Support\Facades\Blade::check('merchant')): ?>
                    <th><?php echo e(translate('Admin Commission')); ?></th>
                <?php endif; ?>
                <th><?php echo e(translate('Transaction Date')); ?></th>
                <th><?php echo e(translate('Status')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if($transactions->count() > 0): ?>
                <?php $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <input
                                type="checkbox"
                                name="selected_transactions[]"
                                value="<?php echo e($transaction->id); ?>"
                                class="select-row"
                            >
                        </td>
                        <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                            <td data-label="User">
                                <a href="<?php echo e(route('customer.view', $transaction->user_id)); ?>" target="_blank">
                                    <?php echo e($transaction->users->fname ? $transaction->users->fname . ' ' . $transaction->users->lname : ''); ?>

                                </a>
                            </td>
                        <?php endif; ?>
                        <td data-label="Details"><?php echo e(ucfirst($transaction->payment_details)); ?></td>
                        <td data-label="Amount"><?php echo e(currency_symbol() . $transaction->amount); ?></td>
                        <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                            <td data-label="Commission">
                                <?php echo e($transaction->admin_commission ? currency_symbol() . $transaction->admin_commission : ''); ?>

                            </td>
                        <?php endif; ?>
                        <?php if (\Illuminate\Support\Facades\Blade::check('merchant')): ?>
                            <td data-label="Admin Commission">
                                <?php echo e($transaction->admin_commission ? currency_symbol() . $transaction->admin_commission : ''); ?>

                            </td>
                        <?php endif; ?>
                        <td data-label="Date">
                            <p><?php echo e(dateFormat($transaction->created_at)); ?></p>
                        </td>
                        <td data-label="Status">
                            <?php if($transaction->status == 1): ?>
                                <button class="eg-btn primary-light--btn"><?php echo e(translate('Processing')); ?></button>
                            <?php elseif($transaction->status == 2): ?>
                                <button class="eg-btn green-light--btn"><?php echo e(translate('Completed')); ?></button>
                            <?php else: ?>
                                <button class="eg-btn red-light--btn"><?php echo e(translate('Cancel')); ?></button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <tr>
                    <td colspan="10" class="text-center">
                        <h5 class="data-not-found"><?php echo e(translate('No Data Found')); ?></h5>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</form>
            </div>
        </div>
    </div>

    <?php $__env->startPush('footer'); ?>
        <div class="d-flex justify-content-center custom-pagination">
            <?php echo $transactions->links(); ?>

        </div>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/dashboard/transaction.blade.php ENDPATH**/ ?>