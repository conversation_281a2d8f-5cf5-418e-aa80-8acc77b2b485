<?php $__env->startSection('content'); ?>
    <div class="row mb-35 g-4">
        <div class="col-md-3">
            <div class="page-title text-md-start">
                <h4><?php echo e($page_title ?? ''); ?></h4>
            </div>
        </div>
        <div
            class="col-md-9 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
            <form action="" method="get">
                <div class="input-with-btn d-flex jusify-content-start align-items-strech">
                    <input type="text" name="search" placeholder="<?php echo e(translate('Search your blog')); ?>...">
                    <button type="submit"><i class="bi bi-search"></i></button>
                </div>
            </form>
            <a href="<?php echo e(route('blog.create')); ?>" class="eg-btn btn--primary back-btn"><img
                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>" alt="<?php echo e(translate('Add New')); ?>">
                <?php echo e(translate('Add New')); ?></a>
            <a href="<?php echo e(route('blog.category.list')); ?>" class="eg-btn btn--primary back-btn"><img
                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>" alt="<?php echo e(translate('Category')); ?>">
                <?php echo e(translate('Category')); ?></a>
        </div>
    </div>
    <?php
        $locale = get_setting('DEFAULT_LANGUAGE', 'en');
    ?>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
               <form id="bulkDeleteForm" method="POST" action="<?php echo e(route('blogs.bulk-delete')); ?>">
    <?php echo csrf_field(); ?>
    <?php echo method_field('DELETE'); ?>

    <button
        type="submit"
        id="bulkDeleteBtn"
        style="display: none;"
        class="eg-btn red-light--btn mb-3"
    >
        <?php echo e(translate('Delete Selected')); ?>

    </button>

    <table class="eg-table table customer-table">
        <thead>
            <tr>
                <th><input type="checkbox" id="select-all"></th>
                <th><?php echo e(translate('S.N')); ?></th>
                <th><?php echo e(translate('Image')); ?></th>
                <th><?php echo e(translate('Title')); ?></th>
                <th><?php echo e(translate('Category')); ?></th>
                <th><?php echo e(translate('Date')); ?></th>
                <th><?php echo e(translate('Published')); ?></th>
                <th>
                    <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <img src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>" class="mr-2">
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </th>
                <th><?php echo e(translate('Option')); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php if($blogs->count() > 0): ?>
                <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <input
                                type="checkbox"
                                name="ids[]"
                                class="select-row"
                                value="<?php echo e($blog->id); ?>"
                            >
                        </td>
                        <td data-label="S.N">
                            <?php echo e(($blogs->currentPage() - 1) * $blogs->perPage() + $key + 1); ?>

                        </td>
                        <td data-label="Image">
                            <?php if($blog->image): ?>
                                <img src="<?php echo e(asset('uploads/blog/' . $blog->image)); ?>" alt="<?php echo e($blog->title); ?>">
                            <?php endif; ?>
                        </td>
                        <td data-label="Title"><?php echo e($blog->getTranslation('title', $lang)); ?></td>
                        <td data-label="Category"><?php echo e($blog->blog_categories->getTranslation('name', $lang)); ?></td>
                        <td data-label="Date"><?php echo e(dateFormat($blog->created_at)); ?></td>
                        <td data-label="Published">
                            <div class="form-check form-switch">
                                <input
                                    class="form-check-input flexSwitchCheckStatus"
                                    type="checkbox"
                                    data-activations-status="<?php echo e($blog->status); ?>"
                                    data-id="<?php echo e($blog->id); ?>"
                                    data-type="blogs"
                                    id="flexSwitchCheckStatus<?php echo e($blog->id); ?>"
                                    <?php echo e($blog->status == 1 ? 'checked' : ''); ?>

                                >
                            </div>
                        </td>
                        <td data-label="Language">
                            <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($locale == $language->code): ?>
                                    <i class="text-success bi bi-check-lg"></i>
                                <?php else: ?>
                                    <a href="<?php echo e(route('blog.edit', ['id'=>$blog->id,'lang'=>$language->code])); ?>">
                                        <i class="text--primary bi bi-pencil-square"></i>
                                    </a>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </td>
                        <td data-label="Option">
                            <div class="d-flex gap-2 justify-content-end">
                                <a target="_blank" class="eg-btn account--btn" href="<?php echo e(url('blog/' . $blog->slug)); ?>">
                                    <i class="bi bi-box-arrow-up-right"></i>
                                </a>
                                <a class="eg-btn add--btn" href="<?php echo e(route('blog.edit', ['id'=>$blog->id,'lang'=>get_setting('DEFAULT_LANGUAGE','en')])); ?>">
                                    <i class="bi bi-pencil-square"></i>
                                </a>
                                <form method="POST" action="<?php echo e(route('blog.delete', $blog->id)); ?>">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="eg-btn delete--btn show_confirm" title="<?php echo e(translate('Delete')); ?>">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <tr>
                    <td colspan="9" class="text-center">
                        <h5 class="data-not-found"><?php echo e(translate('No Data Found')); ?></h5>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</form>
            </div>
        </div>
    </div>
    <?php $__env->startPush('footer'); ?>
        <div class="d-flex justify-content-center custom-pagination">
            <?php echo $blogs->links(); ?>

        </div>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/blog/index.blade.php ENDPATH**/ ?>