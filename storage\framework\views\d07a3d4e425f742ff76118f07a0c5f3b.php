<?php $__env->startSection('content'); ?>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4><?php echo e($page_title ?? ''); ?></h4>
            <button type="button" class="eg-btn btn--primary back-btn" data-bs-toggle="modal"
                data-bs-target="#staticBackdrop"><img src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>" alt="Add New">
                <?php echo e(translate('Add New')); ?></button>
        </div>
    </div>
    <?php
        $locale = get_setting('DEFAULT_LANGUAGE', 'en');
    ?>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table category-table">
                    <thead>
                        <tr>
                            <th><?php echo e(translate('S.N')); ?></th>
                            <th><?php echo e(translate('Image')); ?></th>
                            <th><?php echo e(translate('Category Name')); ?></th>
                           
                            <th><?php echo e(translate('Status')); ?></th>
                            <th><?php echo e(translate('Published')); ?></th>
                            <th>
                                <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <img src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>" class="mr-2">
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </th>
                            <th><?php echo e(translate('Option')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td data-label="S.N">
                                    <?php echo e(($categories->currentpage() - 1) * $categories->perpage() + $key + 1); ?></td>
                                <td data-label="Image">
                                    <?php if(!empty($category->image)): ?>
                                        <img src="<?php echo e(asset('uploads/blog/' . $category->image)); ?>"
                                            alt="<?php echo e($category->name); ?>" width="20">
                                    <?php endif; ?>
                                </td>
                                <td data-label="Category Name"><?php echo e($category->getTranslation('name')); ?></td>
                              
                                <td data-label="Status">
                                    <span id="statusBlock<?php echo e($category->id); ?>">
                                        <?php if($category->status == 1): ?>
                                            <button class="eg-btn green-light--btn"><?php echo e(translate('Active')); ?></button>
                                        <?php else: ?>
                                            <button class="eg-btn red-light--btn"><?php echo e(translate('Deactive')); ?></button>
                                        <?php endif; ?>
                                    </span>
                                </td>
                                <td data-label="Published">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input flexSwitchCheckStatus" type="checkbox"
                                            data-activations-status="<?php echo e($category->status); ?>"
                                            data-id="<?php echo e($category->id); ?>" data-type="blogcategory"
                                            id="flexSwitchCheckStatus<?php echo e($category->id); ?>"
                                            <?php echo e($category->status == 1 ? 'checked' : ''); ?>>
                                    </div>
                                </td>
                                <td data-label="Language">
                                    <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($locale == $language->code): ?>
                                            <i class="text-success bi bi-check-lg"></i>
                                        <?php else: ?>
                                            <a
                                                href="<?php echo e(route('blog.category.edit', ['id' => $category->id, 'lang' => $language->code])); ?>"><i
                                                    class="text--primary bi bi-pencil-square"></i></a>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </td>
                                <td data-label="Option">
                                    <div
                                        class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                        <a class="eg-btn add--btn"
                                            href="<?php echo e(route('blog.category.edit', ['id' => $category->id, 'lang' => get_setting('DEFAULT_LANGUAGE', 'en')])); ?>"><i
                                                class="bi bi-pencil-square"></i></a>
                                        <form method="POST" action="<?php echo e(route('blog.category.delete', $category->id)); ?>">
                                            <?php echo csrf_field(); ?>
                                            <input name="_method" type="hidden" value="DELETE">
                                            <button type="submit" class="eg-btn delete--btn show_confirm"
                                                data-toggle="tooltip" title='Delete'><i class="bi bi-trash"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php echo $__env->make('backend.blog.category_modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <?php $__env->startPush('footer'); ?>
        <div class="d-flex justify-content-center custom-pagination">
            <?php echo $categories->links(); ?>

        </div>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/blog/category.blade.php ENDPATH**/ ?>