<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('backend/css/nestable.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('backend/css/menu.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <div class="breadcrumb-area">
        <h5><?php echo e(translate('Dashboard')); ?></h5>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo e(route('backend.dashboard')); ?>"><?php echo e(translate('Dashboard')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(translate('Menu')); ?></li>
            </ol>
        </nav>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row mb-35">
        <div class="page-title d-flex gap-3 align-items-center page-menu-list">
            <?php if(count($menus) > 0): ?>
                <h6 class="text-sucess"><?php echo e(translate('Select a menu')); ?> </h6>
                <div class="page-menu-item-list d-flex">
                    <form class="d-flex gap-3" id="menu-form" method="GET" action="<?php echo e(route('menu.list')); ?>">
                        <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="form-check">
                                <input
                                    class="form-check-input menu-change <?php echo e($selectedMenu ? ($menu->id == $selectedMenu ? '' : '') : ($loop->first ? 'default-checked' : '')); ?>"
                                    type="checkbox"
                                    <?php if($selectedMenu): ?> <?php echo e($menu->id == $selectedMenu ? 'checked' : ''); ?>


                                     <?php else: ?>
                                     <?php echo e($loop->first ? 'checked' : ''); ?> <?php endif; ?>
                                    value="<?php echo e($menu->id); ?>" id="menu<?php echo e($menu->id); ?>" name="id">
                                <label class="form-check-label" for="menu<?php echo e($menu->id); ?>">
                                    <?php echo e($menu->getTranslation('name')); ?></label>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div class="eg-card product-card add-menu-item">
                <h4><span><?php echo e(translate('Add Menu Item')); ?></span></h4>
                <div class="row">
                    <div class="col-12">
                        <div class="accordion item-area-list" id="accordionMenu">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="pagesList">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#pages-list" aria-expanded="true" aria-controls="pages-list">
                                        <?php echo e(translate('Pages')); ?>

                                    </button>
                                </h2>
                                <div id="pages-list" class="accordion-collapse collapse pages-list"
                                    aria-labelledby="pagesList" data-bs-parent="#accordionMenu">
                                    <div class="accordion-body">
                                        <input type="hidden" name="menu_type" value="page" id="menu_type">
                                        <?php $__currentLoopData = $pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" value="<?php echo e($page->id); ?>"
                                                    name="pages[]" id="page<?php echo e($page->id); ?>">
                                                <label class="form-check-label cursor-pointer "
                                                    for="page<?php echo e($page->id); ?>">
                                                    <?php echo e($page->getTranslation('page_name')); ?>

                                                </label>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mt-2"></div>
                                        <div class="justify-content-between">
                                            <label class="btn btn-sm btn-default border-3 border-secondary"><input
                                                    class="form-check-input checked-all-item" type="checkbox">
                                                <?php echo e(translate('Select All')); ?></label>
                                            <button type="button"
                                                class="pull-right btn btn-default btn-sm border-3 border-secondary add-menu"
                                                id="add-pages"><?php echo e(translate('Add to Menu')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="blogsList">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#blogs-list" aria-expanded="true" aria-controls="blogs-list">
                                        <?php echo e(translate('Blogs')); ?>

                                    </button>
                                </h2>
                                <div id="blogs-list" class="accordion-collapse collapse pages-list"
                                    aria-labelledby="blogsList" data-bs-parent="#accordionMenu">
                                    <div class="accordion-body">
                                        <input type="hidden" name="menu_type" value="blog" id="menu_type">
                                        <?php $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                    value="<?php echo e($blog->id); ?>" id="blog<?php echo e($blog->id); ?>"
                                                    name="blogs[]">
                                                <label class="form-check-label" for="blog<?php echo e($blog->id); ?>">
                                                    <?php echo e(Str::limit($blog->getTranslation('title'), 15)); ?>...
                                                </label>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mt-2"></div>
                                        <div class="justify-content-between">
                                            <label class="btn btn-sm btn-default border-3 border-secondary ">
                                                <input class="form-check-input checked-all-item" type="checkbox">
                                                <?php echo e(translate('Select All')); ?></label>
                                            <button type="button"
                                                class="pull-right btn btn-default btn-sm border-3 border-secondary add-menu"
                                                id="add-blogs"> <?php echo e(translate('Add to Menu')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="customLink">
                                    <button class="accordion-button" type="button"
                                        data-bs-toggle="collapse"data-bs-target="#custom-link" aria-expanded="true"
                                        aria-controls="custom-link"><?php echo e(translate('Custom Link')); ?>

                                    </button>
                                </h2>
                                <div id="custom-link" class="accordion-collapse collapse" aria-labelledby="customLink"
                                    data-bs-parent="#accordionMenu">
                                    <div class="accordion-body">
                                        <input type="hidden" name="menu_type" value="custom" id="menu_type">
                                        <div class="form-inner mb-3">
                                            <label><?php echo e(translate('Menu Name')); ?><span>*</span></label>
                                            <input type="text" class="username-input" name="custom_menu_name"
                                                id="custom_menu_name" placeholder="Menu Name">
                                            <div class="text-danger"></div>

                                        </div>
                                        <div class="form-inner mb-3">
                                            <label> <?php echo e(translate('Link')); ?></label>
                                            <input type="text" class="username-input" name="custom_link"
                                                id="custom_link" placeholder="https://">
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="newTap"
                                                name="new_tap">
                                            <label class="form-check-label"
                                                for="newTap"><?php echo e(translate('Open In New Window')); ?> ?</label>
                                        </div>


                                        <div class="justify-content-between">
                                            <button type="button"
                                                class="pull-right btn btn-default btn-sm border-3 border-secondary add-menu"
                                                id="add-custom-menu"> <?php echo e(translate('Add to Menu')); ?></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <div class="col-lg-8">
            <div class="eg-card product-card menu-structure">
                <h4><span><?php echo e(translate('Menu Structure')); ?></span></h4>

                <input type="hidden" name="lang" id="lang" value="<?php echo e(\App::getLocale()); ?>">

                <p> <?php echo e(translate('add')); ?>

                    <?php echo e(translate('Categories')); ?>,<?php echo e(translate('Pages')); ?>,<?php echo e(translate('Blogs')); ?> or
                    <?php echo e(translate('Custom Link')); ?> <?php echo e(translate('to the menu')); ?> </p>

                <div class="dd" id="nestable">
                    <ol class="dd-list">

                        <?php if(!empty($itemsWithChildrens)): ?>
                            <?php $__currentLoopData = $itemsWithChildrens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="dd-item" data-id="<?php echo e($item->id); ?>">
                                    <div class="dd-handle">
                                        <?php if($item->menu_type == 'page'): ?>
                                            <?php echo e($item?->page?->getTranslation('page_name')); ?>

                                            (<b><?php echo e(translate("$item->menu_type")); ?></b>)
                                        <?php elseif($item->menu_type == 'category'): ?>
                                            <?php echo e($item->category->getTranslation('name')); ?>

                                            (<b><?php echo e(translate("$item->menu_type")); ?></b>)
                                        <?php elseif($item->menu_type == 'blog'): ?>
                                            <?php echo e($item?->blog?->getTranslation('title')); ?>

                                            (<b><?php echo e(translate("$item->menu_type")); ?></b>)
                                        <?php else: ?>
                                            <?php echo e($item->getTranslation('title')); ?>

                                            (<b><?php echo e(translate("$item->menu_type")); ?></b>)
                                        <?php endif; ?>
                                    </div>
                                    <div class='action-area'>
                                        <?php if($item->menu_type == 'custom'): ?>
                                            <a href="#" class="btn add--btn shadow me-2 edit-menu-item"
                                                data-bs-toggle="modal" data-bs-target="#editMenuItemModal"
                                                data-id="<?php echo e($item->id); ?>">
                                                <i class="bi bi-pencil-square"></i>
                                            </a>
                                        <?php endif; ?>
                                        <a href='#' class='btn btn-danger delete cs-danger shadow '
                                            data-id="<?php echo e($item->id); ?>">
                                            <i class="bi bi-trash"></i>

                                        </a>
                                    </div>
                                    <?php if($item->childrens->count() > 0): ?>
                                        <ol class="dd-list">
                                            <?php $__currentLoopData = $item->childrens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li class="dd-item" data-id="<?php echo e($childItem->id); ?>">
                                                    <div class="dd-handle">
                                                        <?php if($childItem->menu_type == 'page'): ?>
                                                            <?php echo e($childItem?->page?->getTranslation('page_name')); ?>

                                                            (<b><?php echo e(translate("$childItem->menu_type")); ?></b>)
                                                        <?php elseif($childItem->menu_type == 'category'): ?>
                                                            <?php echo e($childItem->category->getTranslation('name')); ?>

                                                            (<b><?php echo e(translate("$childItem->menu_type")); ?></b>)
                                                        <?php elseif($childItem->menu_type == 'blog'): ?>
                                                            <?php echo e($childItem?->blog?->getTranslation('title')); ?>

                                                            (<b><?php echo e(translate("$childItem->menu_type")); ?></b>)
                                                        <?php else: ?>
                                                            <?php echo e($childItem->getTranslation('title')); ?>

                                                            (<b><?php echo e(translate("$childItem->menu_type")); ?></b>)
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class='action-area'>

                                                        <?php if($childItem->menu_type == 'custom'): ?>
                                                            <a href="#"
                                                                class="btn add--btn shadow me-2 edit-menu-item"
                                                                data-bs-toggle="modal" data-bs-target="#editMenuItemModal"
                                                                data-id="<?php echo e($childItem->id); ?>">
                                                                <i class="bi bi-pencil-square"></i>
                                                            </a>
                                                        <?php endif; ?>


                                                        <a href='#' class='btn btn-danger delete cs-danger shadow '
                                                            data-id="<?php echo e($childItem->id); ?>">
                                                            <i class="bi bi-trash"></i>
                                                        </a>
                                                    </div>

                                                    <?php if($childItem->childrens->count() > 0): ?>
                                                        <ol class="dd-list">
                                                            <?php $__currentLoopData = $childItem->childrens; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <li class="dd-item" data-id="<?php echo e($child->id); ?>">
                                                                    <div class="dd-handle">

                                                                        <div class="dd-handle">
                                                                            <?php if($child->menu_type == 'page'): ?>
                                                                                <?php echo e($child?->page?->getTranslation('page_name')); ?>

                                                                                (<b><?php echo e(translate("$child->menu_type")); ?></b>)
                                                                            <?php elseif($child->menu_type == 'category'): ?>
                                                                                <?php echo e($child->category->getTranslation('name')); ?>

                                                                                (<b><?php echo e(translate("$child->menu_type")); ?></b>)
                                                                            <?php elseif($child->menu_type == 'blog'): ?>
                                                                                <?php echo e($child?->blog?->getTranslation('title')); ?>

                                                                                (<b><?php echo e(translate("$child->menu_type")); ?></b>)
                                                                            <?php else: ?>
                                                                                <?php echo e($child->getTranslation('title')); ?>

                                                                                (<b><?php echo e(translate("$child->menu_type")); ?></b>)
                                                                            <?php endif; ?>
                                                                        </div>

                                                                    </div>

                                                                    <div class='action-area'>
                                                                        <?php if($child->menu_type == 'custom'): ?>
                                                                            <a href="#"
                                                                                class="btn add--btn shadow me-2 edit-menu-item"
                                                                                data-bs-toggle="modal"
                                                                                data-bs-target="#editMenuItemModal" d
                                                                                data-id="<?php echo e($child->id); ?>">
                                                                                <i class="bi bi-pencil-square"></i>
                                                                            </a>
                                                                        <?php endif; ?>
                                                                        <a href='#'
                                                                            class='btn btn-danger delete cs-danger shadow '
                                                                            data-id="<?php echo e($child->id); ?>">
                                                                            <i class="bi bi-trash"></i>
                                                                        </a>
                                                                    </div>
                                                                </li>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </ol>
                                                    <?php endif; ?>
                                                </li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ol>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                    </ol>
                </div>

            </div>
        </div>
    </div>


    <div class="modal fade menu-modal" id="editMenuItemModal" tabindex="-1" aria-labelledby="editMenuItemModal"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <p class="modal-title" id="exampleModalLabel"><?php echo e(translate('Menu Edit')); ?></p>
                    <a type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"> </a>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('menu.item.update')); ?>" method="POST" class="add-form">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" id="menuItemId" name="id" />
                        <div class="form-inner mb-3">
                            <label> <?php echo e(translate('Menu Name Item')); ?> <span>*</span></label>
                            <input type="text" class="username-input" id="menuItemName" name="title"
                                placeholder="Menu Item Name">
                            <span class="text-danger error-text title_err"></span>
                        </div>
                        <div class="custom-field">

                        </div>

                        <div class="button-group d-flex justify-content-end mt-30">
                            <button type="button" class="btn btn-danger sm-btn shadow me-2"
                                data-bs-dismiss="modal"><?php echo e(translate('Close')); ?></button>
                            <button type="submit"
                                class="eg-btn btn--green sm-btn shadow"><?php echo e(translate('Update')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('backend/js/nestable.min.js')); ?>"></script>
    <script src="<?php echo e(asset('backend/js/sweetalert2.js')); ?>"></script>
    <?php echo $__env->make('js.admin.menu', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/menu/index.blade.php ENDPATH**/ ?>