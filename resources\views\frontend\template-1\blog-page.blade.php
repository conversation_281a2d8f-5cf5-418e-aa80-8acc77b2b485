@extends('frontend.template-'.$templateId.'.partials.master')

@push('styles')
<link rel="stylesheet" href="{{ asset('frontend/css/blog-standard.css') }}">
@endpush

@section('content')
@include('frontend.template-'.$templateId.'.breadcrumb.breadcrumb')

<div class="blog-standard-section pt-120 mb-120">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                @include('frontend.template-'.$templateId.'.partials.blog-standard', ['blogs' => $blogs])
            </div>
           <div class="col-lg-4">
                    <div class="sidebar-area">
                        <div class="single-widget mb-30">
                            <h5 class="widget-title">{{ translate('Search From Blog') }}</h5>
                            <form action="{{ route('blog.page') }}" method="GET">
                                <div class="search-box">
                                    <input type="text" name="search" placeholder="{{ translate('Search Here') }}" value="{{ request('search') }}">
                                    <button type="submit"><i class="bx bx-search"></i></button>
                                </div>
                            </form>
                        </div>
                        @php
                            $categories = App\Models\BlogCategory::where('status', 1)->withCount('blogs')->get();
                        @endphp
                        @if ($categories->count() > 0)
                            <div class="single-widget mb-30">
                                <h5 class="widget-title">{{ translate('Post Categories') }}</h5>
                                <ul class="category-list">
                                    @foreach ($categories as $category)
                                        <li>
                                            <a href="{{ route('blog.category', $category->slug) }}">{{ $category->getTranslation('name') }}
                                                <span>({{ $category->blogs_count ?? 0 }})</span>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <div class="single-widget mb-30">
                            <h5 class="widget-title">{{ translate('Recent Post') }}</h5>
                            @php
                                $recentBlogs = App\Models\Blog::where('status', 1)->latest()->take(5)->get();
                            @endphp
                            @foreach ($recentBlogs as $recentBlog)
                                <div class="recent-post-widget mb-20">
                                    <div class="recent-post-img">
                                        <a href="{{ route('blog.details', $recentBlog->slug) }}">
                                            @if ($recentBlog->image)
                                                <img alt="{{ $recentBlog->title }}"
                                                    src="{{ asset('uploads/blog/' . $recentBlog->image) }}">
                                            @else
                                                <img alt="{{ $recentBlog->title }}"
                                                    src="{{ asset('uploads/placeholder.jpg') }}">
                                            @endif
                                        </a>
                                    </div>
                                    <div class="recent-post-content">
                                        <a
                                            href="{{ route('blog.details', $recentBlog->slug) }}">{{ $recentBlog->created_at->format('F d, Y') }}</a>
                                        <h6><a
                                                href="{{ route('blog.details', $recentBlog->slug) }}">{{ $recentBlog->getTranslation('title') }}</a>
                                        </h6>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>
@endsection
