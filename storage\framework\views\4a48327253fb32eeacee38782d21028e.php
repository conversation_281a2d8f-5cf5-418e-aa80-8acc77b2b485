<?php $__env->startSection('content'); ?>
    <div class="row mb-35 g-4">
        <div class=" col-md-3">
            <div class="page-title text-md-start">
                <h4><?php echo e($page_title ?? ''); ?></h4>
            </div>
        </div>
        <div
            class=" col-md-9 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
            <form action="" method="get">
                <div class="input-with-btn d-flex jusify-content-start align-items-strech">
                    <input type="text" name="search" placeholder="<?php echo e(translate('Search your country')); ?>...">
                    <button type="submit"><i class="bi bi-search"></i></button>
                </div>
            </form>
        </div>
    </div>

    <form action="<?php echo e(route('country.store')); ?>" method="post">
        <?php echo csrf_field(); ?>
        <div class="row">
            <div class="col-lg-8 offset-lg-2">
                <div class="eg-card product-card">
                    <div class="form-inner mb-35">
                        <label><?php echo e(translate('Name')); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="username-input" value="<?php echo e(old('name')); ?>" name="name"
                            placeholder="<?php echo e(translate('Enter Name')); ?>">
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="form-inner mb-35">
                        <label><?php echo e(translate('Code')); ?> <span class="text-danger">*</span></label>
                        <?php
                            $languagesArray = \App\Models\Location::whereNull('country_id')
                                ->whereNull('state_id')
                                ->pluck('country_code')
                                ->toarray();
                        ?>
                        <select id="language_code" class="form-control js-example-basic-single mb-2 mb-md-0"
                            name="country_code">
                            <option value=""><?php echo e(translate('Select Option')); ?></option>
                            <?php $__currentLoopData = \File::files(public_path('assets/img/flags')); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $path): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if(!in_array(pathinfo($path)['filename'], $languagesArray)): ?>
                                    <option value="<?php echo e(pathinfo($path)['filename']); ?>"
                                        <?php echo e(old('country_code') == pathinfo($path)['filename'] ? 'selected' : ''); ?>>
                                        <?php echo e(strtoupper(pathinfo($path)['filename'])); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['country_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="button-group mt-15 text-center  ">
                        <input type="submit" class="eg-btn btn--green back-btn me-3" value="<?php echo e(translate('Save')); ?>">
                    </div>
                </div>
            </div>


        </div>
    </form>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table">
                    <thead>
                        <tr>
                            <th><?php echo e(translate('S.N')); ?></th>
                            <th><?php echo e(translate('Name')); ?></th>
                            <th><?php echo e(translate('Flag')); ?></th>
                            <th><?php echo e(translate('Code')); ?></th>
                            <th><?php echo e(translate('Option')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td data-label="S.N"><?php echo e(($countries->currentpage() - 1) * $countries->perpage() + $key + 1); ?>

                                </td>
                                <td data-label="Name"><?php echo e($country->name); ?></td>
                                <td>
                                    <?php if($country->country_code): ?>
                                        <img src="<?php echo e(asset('assets/img/flags/' . $country->country_code . '.png')); ?>"
                                            alt="<?php echo e($country->name); ?>" width="20">
                                    <?php endif; ?>
                                </td>
                                <td data-label="Country Code"><?php echo e($country->country_code); ?></td>
                                <td data-label="Action">
                                    <div
                                        class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                        <a href="<?php echo e(route('state.create', $country->id)); ?>"
                                            title="<?php echo e(translate('State')); ?>" class="eg-btn account--btn"><i
                                                class="bi bi-info-lg"></i></i></a>
                                        <a href="<?php echo e(route('country.edit', $country->id)); ?>"
                                            title="<?php echo e(translate('Edit')); ?>" class="eg-btn add--btn"><i
                                                class="bi bi-pencil-square"></i></a>
                                        <form method="POST" action="<?php echo e(route('country.delete', $country->id)); ?>">
                                            <?php echo csrf_field(); ?>
                                            <input name="_method" type="hidden" value="DELETE">
                                            <button type="submit" class="eg-btn delete--btn show_confirm"
                                                data-toggle="tooltip" title="<?php echo e(translate('Delete')); ?>"><i
                                                    class="bi bi-trash"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php $__env->startPush('footer'); ?>
        <div class="d-flex justify-content-center custom-pagination">
            <?php echo $countries->links(); ?>

        </div>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/location/index.blade.php ENDPATH**/ ?>