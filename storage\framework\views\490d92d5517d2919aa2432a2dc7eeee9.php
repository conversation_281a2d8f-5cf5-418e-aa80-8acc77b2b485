<?php $__env->startSection('content'); ?>

    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4><?php echo e($page_title ?? ''); ?></h4>
            <div class="language-changer">
                <span><?php echo e(translate('Language Translation')); ?>: </span>
                <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($lang == $language->code): ?>
                        <img src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>" class="mr-3" height="16">
                    <?php else: ?>
                        <a href="<?php echo e(route('tours.edit', ['id' => $tourSingle->id, 'lang' => $language->code])); ?>"><img
                                src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>" class="mr-3"
                                height="16"></a>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <a href="<?php echo e(route('tours.list')); ?>" class="eg-btn btn--primary back-btn"> <img
                    src="<?php echo e(asset('backend/images/icons/back.svg')); ?>" alt="<?php echo e(translate('Go Back')); ?>">
                <?php echo e(translate('Go Back')); ?></a>
        </div>
    </div>
    <form action="<?php echo e(route('tours.update', $tourSingle->id)); ?>" method="post" enctype="multipart/form-data">
        <input name="_method" type="hidden" value="PATCH">
        <input type="hidden" name="lang" value="<?php echo e($lang); ?>">
        <?php echo csrf_field(); ?>
        <div class="row">

            <div class="col-lg-8">
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Tour Content')); ?></h4>
                    </div>
                    <div class="form-inner mb-35">
                        <label><?php echo e(translate('Title')); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="username-input"
                            value="<?php echo e(old('title', $tourSingle->getTranslation('title', $lang))); ?>" name="title"
                            placeholder="<?php echo e(translate('Name of the tour')); ?>">
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> 
                    </div>
                    <div class="form-inner mb-35">
                        <label><?php echo e(translate('Label')); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="username-input" value="<?php echo e(old('shoulder', $tourSingle->getTranslation('shoulder', $lang))); ?>"
                            name="shoulder" placeholder="<?php echo e(translate('Name of the Label')); ?>">
                        <?php $__errorArgs = ['shoulder'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="form-inner mb-25">
                        <label><?php echo e(translate('Content')); ?> <span class="text-danger">*</span></label>
                        <textarea id="summernote" name="content"><?php echo e(old('content', $tourSingle->getTranslation('content', $lang))); ?></textarea>
                        <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('YouTube Video Thumbnail')); ?></label>
                                <input type="file" class="username-input" name="youtube_image">
                                <?php $__errorArgs = ['youtube_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <?php if($tourSingle->youtube_image): ?>
                                <img class="mt-3" src="<?php echo e(asset('uploads/tour/youtube/'.$tourSingle->youtube_image)); ?>" alt="<?php echo e($tourSingle->youtube_image); ?>" width="100">
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('YouTube Video URL')); ?></label>
                                <input type="text" class="username-input"
                                    value="<?php echo e(old('youtube_video', $tourSingle->youtube_video)); ?>" name="youtube_video"
                                    placeholder="<?php echo e(translate('Youtube Video Link')); ?>">
                                <?php $__errorArgs = ['youtube_video'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    
                        <div class="form-inner mb-35">
                            <label> <?php echo e(translate('Sub Destination')); ?></label>
                            <select class="username-input sub_destination" name="sub_destination[]"
                                multiple="multiple">
                                <?php if(isset($tourSingle->sub_destination) && count($tourSingle?->sub_destination) > 0): ?>

                                    <?php $__currentLoopData = $tourSingle->sub_destination; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $sub_des): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($sub_des); ?>" selected><?php echo e($sub_des); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                        </div>
                </div>
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Minimum/Maximum People')); ?></h4>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Tour Min People')); ?></label>
                                <input type="text" id="min_people" class="username-input"
                                    value="<?php echo e(old('min_people', $tourSingle->min_people)); ?>" name="min_people"
                                    placeholder="<?php echo e(translate('Tour Min People')); ?>">
                                <?php $__errorArgs = ['min_people'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Minimum advance reservations')); ?></label>
                                <input type="number" class="username-input"
                                    value="<?php echo e(old('min_advance_reservations', $tourSingle->min_advance_reservations)); ?>"
                                    name="min_advance_reservations" placeholder="<?php echo e(translate('Ex: 3')); ?>">
                                <small><?php echo e(translate('Leave blank if you dont need to use the min day option')); ?></small>
                                <?php $__errorArgs = ['min_advance_reservations'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Tour Max People')); ?></label>
                                <input type="text" id="max_people" class="username-input"
                                    value="<?php echo e(old('max_people', $tourSingle->max_people)); ?>" name="max_people"
                                    placeholder="<?php echo e(translate('Tour Max People')); ?>">
                                <?php $__errorArgs = ['max_people'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <div class="form-inner mb-35 position-relative">
                                <label><?php echo e(translate('Cancellation')); ?></label>
                                <input id="cancellation" name="cancellation" placeholder="Cancellation"
                                    value="<?php echo e(old('cancellation', $tourSingle->cancellation)); ?>">
                                <span class="duration-icon"><?php echo e(translate('hours')); ?></span>
                            </div>

                        </div>

                         <div class="col-md-12">
                            <div class="form-inner mb-35">
                                    <label><?php echo e(translate('Tour Duration')); ?></label>
                                    <input type="text" id="min_people" class="username-input"
                                        value="<?php echo e(old('cancellation', $tourSingle->duration)); ?>" name="duration"
                                        placeholder="<?php echo e(translate('e.g : 4 Days/3 Nights')); ?>">
                                    <?php $__errorArgs = ['min_people'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="error text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    <div class="form-inner mb-35">
                        <div class="row">
                            <div class="col-md-12">
                                <label> <b><?php echo e(translate('FAQs')); ?></b></label>
                                <input type="text" name="faq_title" value="<?php echo e(old('faq_title', $tourSingle->faq_title)); ?>" placeholder="<?php echo e(translate('Enter FAQ Title')); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-35 mt-3">
                        <div id="faqs">
                            <?php if($data['faqs']): ?>
                                <?php $__currentLoopData = $data['faqs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="row faqsFormRow">
                                        <div class="col-md-5">
                                            <div class="form-inner mb-25">
                                                <input type="text" value="<?php echo e($faq->title); ?>"
                                                    name="faqs[<?php echo e($key); ?>][title]" class="m-input"
                                                    placeholder="Enter Title" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-inner mb-25">
                                                <textarea name="faqs[<?php echo e($key); ?>][content]" class="n-input" placeholder="Enter Content"><?php echo e($faq->content); ?></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button id="faqsRemoveRow" type="button"
                                                class="eg-btn btn--red rounded px-3">
                                                <i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="input-group-append">
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                        <div class="add-btn-area d-flex jusify-content-center pt-4">
                            <button id="faqsAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                    </div>
                    <div class="form-inner mb-35">
                        <div class="row">
                            <div class="col-md-12">
                                <label> <b><?php echo e(translate('Include')); ?></b></label>
                                <input type="text" name="include_title" value="<?php echo e(old('include_title', $tourSingle->include_title)); ?>" placeholder="<?php echo e(translate('Enter Include Title')); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-35 mt-3">
                        <div id="includes">
                            <?php if($data['includes']): ?>
                                <?php $__currentLoopData = $data['includes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $include): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="row includeFormRow">
                                        <div class="col-md-11">
                                            <div class="form-inner mb-25">
                                                <input type="text" value="<?php echo e($include->title); ?>"
                                                    name="includes[<?php echo e($key); ?>][title]" class="m-input"
                                                    placeholder="Enter Title" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button id="includeRemoveRow" type="button"
                                                class="eg-btn btn--red rounded px-3">
                                                <i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="input-group-append">
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>

                        </div>
                        <div class="add-btn-area d-flex jusify-content-center pt-4">
                            <button id="includeAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                    </div>
                    <div class="form-inner mb-35">
                        <div class="row">
                            <div class="col-md-12">
                                <label> <b><?php echo e(translate('Exclude')); ?></b></label>
                                <input type="text" name="exclude_title" value="<?php echo e(old('exclude_title', $tourSingle->exclude_title)); ?>" placeholder="<?php echo e(translate('Enter Exclude Title')); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-35 mt-3">
                        <div id="excludes">
                            <?php if($data['excludes']): ?>
                                <?php $__currentLoopData = $data['excludes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $exclude): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="row excludeFormRow">
                                        <div class="col-md-11">
                                            <div class="form-inner mb-25">
                                                <input type="text" value="<?php echo e($exclude->title); ?>"
                                                    name="excludes[<?php echo e($key); ?>][title]" class="m-input"
                                                    placeholder="Enter Title" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button id="excludeRemoveRow" type="button"
                                                class="eg-btn btn--red rounded px-3">
                                                <i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="input-group-append">
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                        <div class="add-btn-area d-flex jusify-content-center pt-4">
                            <button id="excludeAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                    </div>
                    <div class="form-inner mb-35">
                        <div class="row">
                            <div class="col-md-12">
                                <label> <b><?php echo e(translate('Highlights')); ?></b></label>
                                <input type="text" name="highlight_title" value="<?php echo e(old('highlight_title', $tourSingle->highlight_title)); ?>" placeholder="<?php echo e(translate('Enter Highlight Title')); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-35 mt-3">
                        <div id="highlights">
                            <?php if($data['highlights']): ?>
                                <?php $__currentLoopData = $data['highlights']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $highlight): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="row highlightFormRow">
                                        <div class="col-md-11">
                                            <div class="form-inner mb-25">
                                                <input type="text" name="highlights[<?php echo e($key); ?>][title]"
                                                    value="<?php echo e($highlight->title); ?>" class="m-input"
                                                    placeholder="Enter Title" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button id="highlightRemoveRow" type="button"
                                                class="eg-btn btn--red rounded px-3">
                                                <i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="input-group-append">
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>

                        </div>
                        <div class="add-btn-area d-flex jusify-content-center pt-4">
                            <button id="highlightAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                    </div>
                    <div class="form-inner mb-35">
                        <div class="row">
                            <div class="col-md-12">
                                <label> <b><?php echo e(translate('Itinerary')); ?></b></label>
                                <input type="text" name="itinerary_title" value="<?php echo e(old('itinerary_title', $tourSingle->itinerary_title)); ?>" placeholder="<?php echo e(translate('Enter Itinerary Title')); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-35 mt-3">
                        <div id="itinerary">
                            <?php if($data['itinerary']): ?>
                                <?php $__currentLoopData = $data['itinerary']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="row itineraryFormRow">
                                        <div class="col-md-5">
                                            <div class="form-inner mb-25">
                                                <input type="text" name="itinerary[<?php echo e($key); ?>][title]"
                                                    value="<?php echo e($itinerary->title); ?>" class="m-input mb-2"
                                                    placeholder="Enter Title" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-inner mb-25">
                                                <textarea name="itinerary[<?php echo e($key); ?>][content]" class="n-input" placeholder="Enter Content"><?php echo e($itinerary->content); ?></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button id="itineraryRemoveRow" type="button"
                                                class="eg-btn btn--red rounded px-3">
                                                <i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="input-group-append">
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                        <div class="add-btn-area d-flex jusify-content-center pt-4">
                            <button id="itineraryAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                    </div>
                </div>
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Pricing')); ?></h4>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Tour Price')); ?> <span class="text-danger">*</span></label>
                                <input type="number" class="username-input"
                                    value="<?php echo e(old('price', $tourSingle->price)); ?>" name="price"
                                    placeholder="<?php echo e(translate('Tour Price')); ?>">
                                <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Tour Sale Price')); ?></label>
                                <input type="number" class="username-input"
                                    value="<?php echo e(old('sale_price', $tourSingle->sale_price)); ?>" name="sale_price"
                                    placeholder="<?php echo e(translate('Tour Sale Price')); ?>">
                                <?php $__errorArgs = ['sale_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Tour Child Price')); ?></label>
                                <input type="number" class="username-input"
                                    value="<?php echo e(old('child_price', $tourSingle->child_price)); ?>" name="child_price"
                                    placeholder="<?php echo e(translate('Tour Child Price')); ?>">
                                <?php $__errorArgs = ['child_price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Tour Starts From')); ?></label>
                                <input type="text" class="username-input <?php $__errorArgs = ['starts_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       value="<?php echo e(old('starts_from', $tourSingle->starts_from)); ?>" name="starts_from"
                                       placeholder="<?php echo e(translate('e.g., City Center, Airport, Hotel')); ?>">
                                <?php $__errorArgs = ['starts_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-check mb-35">
                        <label class="form-check-label" for="enable_service_fee">
                            <input class="form-check-input enable_service_fee" name="enable_service_fee" type="checkbox"
                                id="enable_service_fee" value="1"
                                <?php echo e($tourSingle->enable_service_fee == 1 ? 'checked' : ''); ?>>
                            <b><?php echo e(translate('Enable Service Fee')); ?></b>
                        </label>
                    </div>
                    <div
                        class="form-inner mb-25 <?php echo e($tourSingle->enable_service_fee == 1 ? '' : 'd-none'); ?> service_fee_show">
                        <div class="mb-3 row">
                            <div class="col-md-6">
                                <label> <b><?php echo e(translate('Service Fee')); ?></b></label>
                            </div>
                        </div>
                    </div>

                    <div class="<?php echo e($tourSingle->enable_service_fee == 1 ? '' : 'd-none'); ?> service_fee_show mb-3">
                        <div id="hotel_service_fee">
                            <?php if($data['service_fees']): ?>
                                <?php $__currentLoopData = $data['service_fees']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="mb-3 row g-3 serviceFormRow">
                                        <div class="col-md-7">
                                            <div class="form-inner mb-25">
                                                <input type="text" name="service_fee[<?php echo e($key); ?>][name]"
                                                    value="<?php echo e($service->name); ?>" class="m-input" placeholder="Fee Name"
                                                    autocomplete="off">
                                            </div>
                                            <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <input type="number"
                                                        name="service_fee[<?php echo e($key); ?>][price]"
                                                        value="<?php echo e($service->price); ?>" class="n-input" placeholder="Price">
                                                </div>
                                            </div>
                                                <div class="col-md-6">
                                                <div class="form-inner mb-25">
                                                    <select name="service_fee[<?php echo e($key); ?>][unit]">
                                                        <option value="fixed"
                                                            <?php echo e($service->unit == 'fixed' ? 'selected' : ''); ?>><?php echo e(translate('Fixed')); ?></option>
                                                        <option value="percent"
                                                            <?php echo e($service->unit == 'percent' ? 'selected' : ''); ?>><?php echo e(translate('Percent')); ?>

                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <label class="form-check-label" for="price_type<?php echo e($key); ?>1">
                                                    <input class="form-check-input" type="radio"
                                                        name="service_fee[<?php echo e($key); ?>][price_type]"
                                                        value="one_time" id="price_type<?php echo e($key); ?>1"
                                                        <?php echo e($service->price_type == 'one_time' ? 'checked' : ''); ?>>
                                                    <?php echo e(translate('Price One Time')); ?>

                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <label class="form-check-label" for="price_type<?php echo e($key); ?>2">
                                                    <input class="form-check-input" type="radio"
                                                        name="service_fee[<?php echo e($key); ?>][price_type]"
                                                        value="per_person" id="price_type<?php echo e($key); ?>2"
                                                        <?php echo e($service->price_type == 'per_person' ? 'checked' : ''); ?>>
                                                    <?php echo e(translate('Price Per Person')); ?>

                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <button id="serviceFeeRemoveRow" type="button"
                                                class="eg-btn btn--red rounded px-3">
                                                <i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="input-group-append">
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>

                        </div>
                        <div class="add-btn-area d-flex jusify-content-center pt-4">
                            <button id="serviceFeeAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                    </div>
      
                </div>
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Location')); ?></h4>
                    </div>
                    <div class="form-inner mb-35">
                        <label><?php echo e(translate('Address')); ?></label>
                        <input type="text" class="username-input" value="<?php echo e(old('address', $tourSingle->address)); ?>"
                            name="address" placeholder="<?php echo e(translate('Address')); ?>">
                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="row">
                        <div class="col-xl-6 col-lg-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Country')); ?> <span class="text-danger">*</span></label>
                                <select class="js-example-basic-single country_id" name="country_id">
                                    <option value=""><?php echo e(translate('Select Option')); ?></option>
                                    <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($country->id); ?>"
                                            <?php echo e(old('country_id', $tourSingle->country_id) == $country->id ? 'selected' : ''); ?>>
                                            <?php echo e($country->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['country_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('State')); ?> <span class="text-danger">*</span></label>
                                <select class="js-example-basic-single state_id" name="state_id">
                                    <option value=""><?php echo e(translate('Select Option')); ?></option>
                                    <?php if($tourSingle->state_id): ?>
                                        <option value="<?php echo e($tourSingle->state_id); ?>" selected>
                                            <?php echo e($tourSingle->states?->name); ?></option>
                                    <?php endif; ?>
                                </select>
                                <?php $__errorArgs = ['state_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('City')); ?> <span class="text-danger">*</span></label>
                                <select class="js-example-basic-single city_id" name="city_id">
                                    <option value=""><?php echo e(translate('Select Option')); ?></option>
                                    <?php if($tourSingle->state_id): ?>
                                        <option value="<?php echo e($tourSingle->state_id); ?>" selected>
                                            <?php echo e($tourSingle->states?->name); ?></option>
                                    <?php endif; ?>
                                </select>
                                <?php $__errorArgs = ['city_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Zip/Postal')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="zip_code"
                                    value="<?php echo e(old('zip_code', $tourSingle->zip_code)); ?>" class="username-input"
                                    placeholder="<?php echo e(translate('Zip/Postal')); ?>">
                                <?php $__errorArgs = ['zip_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Latitude')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="map_lat" value="<?php echo e(old('map_lat', $tourSingle->map_lat)); ?>"
                                    class="username-input" placeholder="<?php echo e(translate('Latitude')); ?>">
                                <a class="text-primary" href="https://www.latlong.net/" target="_blank"><?php echo e(translate('Go Here to get
                                    Latitude from
                                    address')); ?></a>
                                <?php $__errorArgs = ['map_lat'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6">
                            <div class="form-inner mb-35">
                                <label><?php echo e(translate('Longitude')); ?> <span class="text-danger">*</span></label>
                                <input type="text" name="map_lng" value="<?php echo e($tourSingle->map_lng); ?>"
                                    class="username-input" placeholder="<?php echo e(translate('Longitude')); ?>">
                                <a class="text-primary" href="https://www.latlong.net/" target="_blank">Go Here to get
                                    Longitude from
                                    address</a>
                                <?php $__errorArgs = ['map_lng'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error text-danger"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Availability')); ?></h4>
                    </div>

                    <div class="form-check mb-35">
                        <label class="form-check-label" for="enable_fixed_date">
                            <input class="form-check-input enable_fixed_date" name="enable_fixed_dates" type="checkbox"
                                id="enable_fixed_date" value="1"
                                <?php echo e($tourSingle->enable_fixed_dates == 1 ? 'checked' : ''); ?>>
                            <b><?php echo e(translate('Enable Fixed Date')); ?></b>
                        </label>
                    </div>
                    <div class="<?php echo e($tourSingle->enable_fixed_dates == 1 ? '' : 'd-none'); ?> fixed_date_show">
                        <div class="form-inner mb-25" id="fixed_date_show">
                            <?php if($data['fixed_dates']): ?>
                            <?php $__currentLoopData = $data['fixed_dates']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$fixed_date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="mb-3 row dateFormRow">
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <input type="text" class="username-input datepicker" name="fixed_date[<?php echo e($key); ?>][start_date]" value="<?php echo e($fixed_date->start_date); ?>" placeholder="Start Date">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <input type="text" class="username-input datepicker" name="fixed_date[<?php echo e($key); ?>][end_date]" value="<?php echo e($fixed_date->end_date); ?>" placeholder="End Date">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <input type="text" class="username-input datepicker" name="fixed_date[<?php echo e($key); ?>][booking_date]" value="<?php echo e($fixed_date->booking_date); ?>" placeholder="Last Booking Date">
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                        <div class="add-btn-area d-flex jusify-content-center mb-35">
                            <button id="dateAddRow" type="button" class="eg-btn btn--dark mx-auto"> <img
                                    src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>"
                                    alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></button>
                        </div>
                        </div>

                    <div class="form-check mb-35">
                        <label class="form-check-label" for="enable_open_hours">
                            <input class="form-check-input enable_open_hours" name="enable_open_hours" type="checkbox"
                                id="enable_open_hours" value="1"
                                <?php echo e($tourSingle->enable_open_hours == 1 ? 'checked' : ''); ?>>
                            <b><?php echo e(translate('Enable Open Hours')); ?></b>
                        </label>
                    </div>
                    <div class="mb-25 <?php echo e($tourSingle->enable_open_hours == 1 ? '' : 'd-none'); ?> open_hours_show">
                        <?php
                            $weeks = [
                                'Monday' => 1,
                                'Tuesday' => 2,
                                'Wednesday' => 3,
                                'Thursday' => 4,
                                'Friday' => 5,
                                'Saturday' => 6,
                                'Sunday' => 7,
                            ];
                        ?>
                        <?php $__currentLoopData = $data['open_hours'] ?? $weeks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="mb-3 row">
                                <div class="col-md-4">
                                    <div class="form-check mb-35">
                                        <label class="form-check-label" for="enable_open_hours_day<?php echo e($key); ?>">
                                            <input class="form-check-input enable_open_hours_day"
                                                name="open_hours[<?php echo e($key); ?>][day]" type="checkbox"
                                                id="enable_open_hours_day<?php echo e($key); ?>"
                                                value="<?php echo e($key); ?>" <?php echo e(isset($value->day) ? 'checked' : ''); ?>>
                                            <b><?php echo e($key); ?></b>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <select name="open_hours[<?php echo e($key); ?>][open]">
                                            <option value="00:00"
                                                <?php echo e(isset($value->open) && $value->open == '00:00' ? 'selected' : ''); ?>>
                                                00:00</option>
                                            <option value="01:00"
                                                <?php echo e(isset($value->open) && $value->open == '01:00' ? 'selected' : ''); ?>>
                                                01:00</option>
                                            <option value="02:00"
                                                <?php echo e(isset($value->open) && $value->open == '02:00' ? 'selected' : ''); ?>>
                                                02:00</option>
                                            <option value="03:00"
                                                <?php echo e(isset($value->open) && $value->open == '03:00' ? 'selected' : ''); ?>>
                                                03:00</option>
                                            <option value="04:00"
                                                <?php echo e(isset($value->open) && $value->open == '04:00' ? 'selected' : ''); ?>>
                                                04:00</option>
                                            <option value="05:00"
                                                <?php echo e(isset($value->open) && $value->open == '05:00' ? 'selected' : ''); ?>>
                                                05:00</option>
                                            <option value="06:00"
                                                <?php echo e(isset($value->open) && $value->open == '06:00' ? 'selected' : ''); ?>>
                                                06:00</option>
                                            <option value="07:00"
                                                <?php echo e(isset($value->open) && $value->open == '07:00' ? 'selected' : ''); ?>>
                                                07:00</option>
                                            <option value="08:00"
                                                <?php echo e(isset($value->open) && $value->open == '08:00' ? 'selected' : ''); ?>>
                                                08:00</option>
                                            <option value="09:00"
                                                <?php echo e(isset($value->open) && $value->open == '09:00' ? 'selected' : ''); ?>>
                                                09:00</option>
                                            <option value="10:00"
                                                <?php echo e(isset($value->open) && $value->open == '10:00' ? 'selected' : ''); ?>>
                                                10:00</option>
                                            <option value="11:00"
                                                <?php echo e(isset($value->open) && $value->open == '11:00' ? 'selected' : ''); ?>>
                                                11:00</option>
                                            <option value="12:00"
                                                <?php echo e(isset($value->open) && $value->open == '12:00' ? 'selected' : ''); ?>>
                                                12:00</option>
                                            <option value="13:00"
                                                <?php echo e(isset($value->open) && $value->open == '13:00' ? 'selected' : ''); ?>>
                                                13:00</option>
                                            <option value="14:00"
                                                <?php echo e(isset($value->open) && $value->open == '14:00' ? 'selected' : ''); ?>>
                                                14:00</option>
                                            <option value="15:00"
                                                <?php echo e(isset($value->open) && $value->open == '15:00' ? 'selected' : ''); ?>>
                                                15:00</option>
                                            <option value="16:00"
                                                <?php echo e(isset($value->open) && $value->open == '16:00' ? 'selected' : ''); ?>>
                                                16:00</option>
                                            <option value="17:00"
                                                <?php echo e(isset($value->open) && $value->open == '17:00' ? 'selected' : ''); ?>>
                                                17:00</option>
                                            <option value="18:00"
                                                <?php echo e(isset($value->open) && $value->open == '18:00' ? 'selected' : ''); ?>>
                                                18:00</option>
                                            <option value="19:00"
                                                <?php echo e(isset($value->open) && $value->open == '19:00' ? 'selected' : ''); ?>>
                                                19:00</option>
                                            <option value="20:00"
                                                <?php echo e(isset($value->open) && $value->open == '20:00' ? 'selected' : ''); ?>>
                                                20:00</option>
                                            <option value="21:00"
                                                <?php echo e(isset($value->open) && $value->open == '21:00' ? 'selected' : ''); ?>>
                                                21:00</option>
                                            <option value="22:00"
                                                <?php echo e(isset($value->open) && $value->open == '22:00' ? 'selected' : ''); ?>>
                                                22:00</option>
                                            <option value="23:00"
                                                <?php echo e(isset($value->open) && $value->open == '23:00' ? 'selected' : ''); ?>>
                                                23:00</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-inner mb-35">
                                        <select name="open_hours[<?php echo e($key); ?>][close]">
                                            <option value="00:00"
                                                <?php echo e(isset($value->close) && $value->close == '00:00' ? 'selected' : ''); ?>>
                                                00:00</option>
                                            <option value="01:00"
                                                <?php echo e(isset($value->close) && $value->close == '01:00' ? 'selected' : ''); ?>>
                                                01:00</option>
                                            <option value="02:00"
                                                <?php echo e(isset($value->close) && $value->close == '02:00' ? 'selected' : ''); ?>>
                                                02:00</option>
                                            <option value="03:00"
                                                <?php echo e(isset($value->close) && $value->close == '03:00' ? 'selected' : ''); ?>>
                                                03:00</option>
                                            <option value="04:00"
                                                <?php echo e(isset($value->close) && $value->close == '04:00' ? 'selected' : ''); ?>>
                                                04:00</option>
                                            <option value="05:00"
                                                <?php echo e(isset($value->close) && $value->close == '05:00' ? 'selected' : ''); ?>>
                                                05:00</option>
                                            <option value="06:00"
                                                <?php echo e(isset($value->close) && $value->close == '06:00' ? 'selected' : ''); ?>>
                                                06:00</option>
                                            <option value="07:00"
                                                <?php echo e(isset($value->close) && $value->close == '07:00' ? 'selected' : ''); ?>>
                                                07:00</option>
                                            <option value="08:00"
                                                <?php echo e(isset($value->close) && $value->close == '08:00' ? 'selected' : ''); ?>>
                                                08:00</option>
                                            <option value="09:00"
                                                <?php echo e(isset($value->close) && $value->close == '09:00' ? 'selected' : ''); ?>>
                                                09:00</option>
                                            <option value="10:00"
                                                <?php echo e(isset($value->close) && $value->close == '10:00' ? 'selected' : ''); ?>>
                                                10:00</option>
                                            <option value="11:00"
                                                <?php echo e(isset($value->close) && $value->close == '11:00' ? 'selected' : ''); ?>>
                                                11:00</option>
                                            <option value="12:00"
                                                <?php echo e(isset($value->close) && $value->close == '12:00' ? 'selected' : ''); ?>>
                                                12:00</option>
                                            <option value="13:00"
                                                <?php echo e(isset($value->close) && $value->close == '13:00' ? 'selected' : ''); ?>>
                                                13:00</option>
                                            <option value="14:00"
                                                <?php echo e(isset($value->close) && $value->close == '14:00' ? 'selected' : ''); ?>>
                                                14:00</option>
                                            <option value="15:00"
                                                <?php echo e(isset($value->close) && $value->close == '15:00' ? 'selected' : ''); ?>>
                                                15:00</option>
                                            <option value="16:00"
                                                <?php echo e(isset($value->close) && $value->close == '16:00' ? 'selected' : ''); ?>>
                                                16:00</option>
                                            <option value="17:00"
                                                <?php echo e(isset($value->close) && $value->close == '17:00' ? 'selected' : ''); ?>>
                                                17:00</option>
                                            <option value="18:00"
                                                <?php echo e(isset($value->close) && $value->close == '18:00' ? 'selected' : ''); ?>>
                                                18:00</option>
                                            <option value="19:00"
                                                <?php echo e(isset($value->close) && $value->close == '19:00' ? 'selected' : ''); ?>>
                                                19:00</option>
                                            <option value="20:00"
                                                <?php echo e(isset($value->close) && $value->close == '20:00' ? 'selected' : ''); ?>>
                                                20:00</option>
                                            <option value="21:00"
                                                <?php echo e(isset($value->close) && $value->close == '21:00' ? 'selected' : ''); ?>>
                                                21:00</option>
                                            <option value="22:00"
                                                <?php echo e(isset($value->close) && $value->close == '22:00' ? 'selected' : ''); ?>>
                                                22:00</option>
                                            <option value="23:00"
                                                <?php echo e(isset($value->close) && $value->close == '23:00' ? 'selected' : ''); ?>>
                                                23:00</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="row mt-3">
                        <div class="col-xl-12">
                            <div class="form-check">
                                <label class="form-check-label" for="seoProduct">
                                    <input class="form-check-input seo-page-checkbox" name="enable_seo" type="checkbox"
                                        id="seoProduct"
                                        <?php echo e(old('enable_seo', $tourSingle->enable_seo) == 1 ? 'checked' : ''); ?>>
                                    <b><?php echo e(translate('Allow SEO')); ?></b>
                                </label>
                            </div>
                        </div>

                        <div class="row mt-3 seo-content">
                            <div class="col-xl-12">
                                <div class="form-inner mb-35">
                                    <label> <?php echo e(translate('Meta Title')); ?> <span class="text-danger">*</span></label>
                                    <input type="text" class="username-input" name="meta_title"
                                        value="<?php echo e(old('meta_title', $tourSingle->meta_title)); ?>">
                                    <?php $__errorArgs = ['meta_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="error text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-xl-12">
                                <div class="form-inner mb-35">
                                    <label> <?php echo e(translate('Meta Description')); ?></label>
                                    <textarea name="meta_description"><?php echo e(old('meta_description', $tourSingle->meta_desc)); ?></textarea>
                                    <?php $__errorArgs = ['meta_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="error text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-xl-12">
                                <div class="form-inner mb-35">
                                    <label> <?php echo e(translate('Meta Keyward')); ?></label>
                                    <select class="username-input meta-keyward" name="meta_keyward[]"
                                        multiple="multiple">
                                        <?php if(isset($tourSingle->meta_keyward) && count($tourSingle?->meta_keyward) > 0): ?>

                                            <?php $__currentLoopData = $tourSingle->meta_keyward; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $keyward): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($keyward); ?>" selected><?php echo e($keyward); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-xl-12">
                                <div class="form-inner mb-35">
                                    <label> <?php echo e(translate('Meta Image')); ?></label>
                                    <input type="file" class="username-input" name="meta_img">
                                    <?php $__errorArgs = ['meta_img'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="error text-danger"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                    <?php if($tourSingle->meta_img): ?>
                                        <img src="<?php echo e(asset('uploads/tour/meta/' . $tourSingle->meta_img)); ?>"
                                            alt="<?php echo e($tourSingle->meta_title); ?>" width="80">
                                    <?php endif; ?>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="eg-card product-card pb-70">
                    <div class="button-group">
                        <button type="submit" class="radio-button">
                            <input type="radio" id="status1" name="status" value="1" />
                            <label class="eg-btn btn--green sm-medium-btn"
                                for="status1"><?php echo e(translate('Update')); ?></label>
                        </button>
                        <button type="submit" class="radio-button">
                            <input type="radio" id="status2" name="status" value="2" />
                            <label class="eg-btn orange--btn sm-medium-btn"
                                for="status2"><?php echo e(translate('Save as Draft')); ?></label>
                        </button>
                    </div>
                </div>
                
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Category')); ?></h4>
                    </div>

                    <div class="form-inner">
                        <select class="js-example-basic-single" name="category_id" required>
                            <option value=""><?php echo e(translate('Select Option')); ?></option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>"
                                    <?php echo e(old('category_id', $tourSingle->category_id) == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['category_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="error text-danger"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                </div>
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm d-flex justify-content-between align-items-center">
                        <h4><?php echo e(translate('Destinations')); ?></h4>
                        <button type="button" class="btn btn-sm btn-primary add-destination-btn">
                            <i class="bi bi-plus"></i> <?php echo e(translate('Add Destination')); ?>

                        </button>
                    </div>

                    <div id="destination-repeater-container">
                        <?php
                            // Combine main destination and additional destinations array
                            $allDestinations = [];
                            if($tourSingle->destination_id) {
                                $allDestinations[] = $tourSingle->destination_id;
                            }
                            if($tourSingle->destinations_array && is_array($tourSingle->destinations_array)) {
                                $allDestinations = array_merge($allDestinations, $tourSingle->destinations_array);
                            }
                            // If no destinations exist, add an empty one for the form
                            if(empty($allDestinations)) {
                                $allDestinations = [null];
                            }
                        ?>
                        
                        <?php $__currentLoopData = $allDestinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $destinationId): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="destination-repeater-item" data-index="<?php echo e($index); ?>">
                            <div class="form-inner mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">
                                        <label><?php echo e(translate('Destination')); ?> <span class="text-danger">*</span></label>
                                        <select class="js-example-basic-single destination-select <?php $__errorArgs = ['destinations.'.$index.'.destination_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="destinations[<?php echo e($index); ?>][destination_id]" required>
                                            <option value=""><?php echo e(translate('Select Option')); ?></option>
                                            <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $destination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($destination->id); ?>"
                                                    <?php echo e(old('destinations.'.$index.'.destination_id', $destinationId) == $destination->id ? 'selected' : ''); ?>>
                                                    <?php echo e($destination->destination); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['destinations.'.$index.'.destination_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="error text-danger"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="ms-2">
                                        <button type="button" class="btn btn-sm btn-danger remove-destination-btn" style="<?php echo e(count($allDestinations) <= 1 ? 'display: none;' : ''); ?>">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                </div>
                <?php if (\Illuminate\Support\Facades\Blade::check('admin')): ?>
                    <div class="eg-card product-card">
                        <div class="eg-card-title-sm">
                            <h4><?php echo e(translate('Agent Setting')); ?></h4>
                        </div>

                        <div class="form-inner">
                            <select class="js-example-basic-single" name="author_id" required>
                                <option value=""><?php echo e(translate('Select Option')); ?></option>
                                <?php $__currentLoopData = $authors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $author): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($author->id); ?>"
                                        <?php echo e(old('author_id', $tourSingle->author_id) == $author->id ? 'selected' : ''); ?>>
                                        <?php echo e($author->username); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['author_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="error text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                    </div>
                <?php endif; ?>

                <?php if($attributes->count() > 0): ?>
                    <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attribute): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $terms = App\Models\TourAttributeTerm::where('attribute_id', $attribute->id)
                                ->orderBy('name', 'asc')
                                ->get();
                        ?>
                        <?php if($terms->count() > 0): ?>
                            <div class="eg-card product-card">
                                <div class="eg-card-title-sm">
                                    <h4><?php echo e(translate('Attribute')); ?>: <?php echo e($attribute->getTranslation('name')); ?></h4>
                                </div>
                                <?php $__currentLoopData = $terms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $term): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="form-check">
                                        <label class="form-check-label" for="term<?php echo e($term->id); ?>">
                                            <input class="form-check-input" name="term[]" type="checkbox"
                                                id="term<?php echo e($term->id); ?>" value="<?php echo e($term->id); ?>"
                                                <?php echo e($data['attribute_terms'] && in_array($term->id, $data['attribute_terms']) ? 'checked' : ''); ?>>
                                            <b><?php echo e($term->getTranslation('name')); ?></b>
                                        </label>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Feature Image')); ?></h4>
                    </div>
                    <div class="form-inner file-upload mb-35">
                        <div class="dropzone-wrapper">
                            <div class="dropzone-desc">
                                <i class="glyphicon glyphicon-download-alt"></i>
                                <p><?php echo e(translate('Choose an image file or drag it here')); ?></p>
                            </div>
                            <input type="file" name="features_image" class="dropzone featues_image">

                        </div>


                        <div class="preview-zone hidden">
                            <div class="box box-solid">
                                <div class="box-header with-border">
                                    <div class="box-tools pull-right">
                                        <button type="button" class="btn btn-danger btn-xs remove-preview"
                                            style="display:none;">
                                            <i class="fa fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <?php if($tourSingle->features_image): ?>
                                        <img src="<?php echo e(asset('uploads/tour/features/' . $tourSingle->features_image)); ?>"
                                            alt="<?php echo e($tourSingle->title); ?>" width="100">
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php $__errorArgs = ['features_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="error text-danger"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                <div class="eg-card product-card">
                    <div class="eg-card-title-sm">
                        <h4><?php echo e(translate('Image Gallery')); ?></h4>
                    </div>
                    <div class="form-inner img-upload mb-35">
                        <div class="dropzone-wrapper">
                            <div class="dropzone-desc">
                                <i class="glyphicon glyphicon-download-alt"></i>
                                <p><?php echo e(translate('Choose image files or drag it here')); ?></p>
                            </div>
                            <input type="file" id="files" name="image[]" class="dropzone image_gal" multiple>

                        </div>

                        <div class="gallery-preview-zone hidden">
                            <div class="box box-solid">
                                <div class="box-body">
                                    <?php if($galleries->count() > 0): ?>
                                        <?php $__currentLoopData = $galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="img-thumb-wrapper card shadow" id="gallery<?php echo e($gallery->id); ?>">
                                                <span class="tour_exist_remove exist_remove_btn"
                                                    data-gellery_id="<?php echo e($gallery->id); ?>">X</span>
                                                <img class="img-thumb"
                                                    src="<?php echo e(asset('uploads/tour/gallery/' . $gallery->image)); ?>"
                                                    title="<?php echo e($gallery->image); ?>">
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="error text-danger"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

            </div>
        </div>

    </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
<style>
.destination-repeater-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.destination-repeater-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.add-destination-btn {
    font-size: 14px;
    padding: 8px 15px;
}

.remove-destination-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 25px;
}

.destination-repeater-item .form-inner {
    margin-bottom: 0;
}

#destination-repeater-container {
    max-height: 400px;
    overflow-y: auto;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    let destinationIndex = <?php echo e(count($allDestinations ?? [])); ?>;

    // Initialize select2 for existing selects
    $('.destination-select').select2();

    // Add destination functionality
    $('.add-destination-btn').on('click', function() {
        const container = $('#destination-repeater-container');
        const newItem = createDestinationItem(destinationIndex);
        container.append(newItem);
        
        // Initialize select2 for the new select element
        $(`#destination-select-${destinationIndex}`).select2();
        
        // Show remove buttons if more than one item
        updateRemoveButtons();
        
        destinationIndex++;
    });

    // Remove destination functionality
    $(document).on('click', '.remove-destination-btn', function() {
        $(this).closest('.destination-repeater-item').remove();
        updateRemoveButtons();
        reindexDestinations();
    });

    function createDestinationItem(index) {
        const destinationOptions = <?php echo json_encode($destinations->map(function($dest) {
            return ['id' => $dest->id, 'destination' => $dest->destination];
        }), 512) ?>;

        let optionsHtml = '<option value=""><?php echo e(translate("Select Option")); ?></option>';
        destinationOptions.forEach(function(destination) {
            optionsHtml += `<option value="${destination.id}">${destination.destination}</option>`;
        });

        return `
            <div class="destination-repeater-item" data-index="${index}">
                <div class="form-inner mb-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <label><?php echo e(translate('Destination')); ?> <span class="text-danger">*</span></label>
                            <select class="form-control destination-select" id="destination-select-${index}" name="destinations[${index}][destination_id]" required>
                                ${optionsHtml}
                            </select>
                        </div>
                        <div class="ms-2">
                            <button type="button" class="btn btn-sm btn-danger remove-destination-btn">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    function updateRemoveButtons() {
        const items = $('.destination-repeater-item');
        if (items.length > 1) {
            $('.remove-destination-btn').show();
        } else {
            $('.remove-destination-btn').hide();
        }
    }

    function reindexDestinations() {
        $('.destination-repeater-item').each(function(index) {
            $(this).attr('data-index', index);
            $(this).find('select').attr('name', `destinations[${index}][destination_id]`);
            $(this).find('select').attr('id', `destination-select-${index}`);
        });
    }

    // Initial setup
    updateRemoveButtons();
});
</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/tours/edit.blade.php ENDPATH**/ ?>