<?php $__env->startSection('content'); ?>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4><?php echo e($page_title ?? ''); ?></h4>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table category-table">
                    <thead>
                        <tr>
                            <th><?php echo e(translate('S.N')); ?></th>
                            <th><?php echo e(translate('Template Name')); ?></th>
                            <th><?php echo e(translate('Subject')); ?></th>
                            <th><?php echo e(translate('Option')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if($email_templates->count() > 0): ?>
                            <?php $__currentLoopData = $email_templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $email_template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td data-label="S.N">
                                        <?php echo e($key + 1); ?>

                                    </td>
                                    <td data-label="Template Name"><b><?php echo e($email_template->name); ?></b></td>
                                    <td data-label="Subject"><b><?php echo e($email_template->subject); ?></b></td>
                                    <td data-label="Option">
                                        <div
                                            class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                            <a href="<?php echo e(route('email.template.edit', $email_template->id)); ?>"
                                                class="eg-btn add--btn"><i class="bi bi-pencil-square"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" data-label="Not Found">
                                    <h5 class="data-not-found"><?php echo e(translate('No Data Found')); ?></h5>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/email_template/index.blade.php ENDPATH**/ ?>