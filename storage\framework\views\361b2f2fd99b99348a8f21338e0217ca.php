 <!-- Modal -->
 <div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
     aria-labelledby="staticBackdropLabel" aria-hidden="true">
     <div class="modal-dialog modal-dialog-centered">
         <div class="modal-content">

             <div class="modal-header">
                 <h5 class="modal-title" id="staticBackdropLabel"><?php echo e(translate('Add Blog Category')); ?></h5>
                 <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
             </div>

             <form action="<?php echo e(route('blog.category.store')); ?>" method="POST" enctype="multipart/form-data">
                 <?php echo csrf_field(); ?>
                 <div class="modal-body">
                     <div class="form-inner mb-35">
                         <label><?php echo e(translate('Category Name')); ?>*</label>
                         <input type="text" name="name" id="category_name" value="<?php echo e(old('name')); ?>"
                             class="username-input" placeholder="<?php echo e(translate('Enter Category Name')); ?>" required>
                         <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                             <div class="error text-danger"><?php echo e($message); ?></div>
                         <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                     </div>
                     <div class="form-inner mb-25">
                         <label><?php echo e(translate('Image')); ?>*</label>
                         <input type="file" name="image" class="password" accept="image/*">
                         <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                             <div class="error text-danger"><?php echo e($message); ?></div>
                         <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                     </div>
                 </div>
                 <div class="modal-footer border-white">
                     <button type="button" class="eg-btn btn--red py-1 px-3 rounded"
                         data-bs-dismiss="modal"><?php echo e(translate('Close')); ?></button>
                     <button type="submit"
                         class="eg-btn btn--primary py-1 px-3 rounded"><?php echo e(translate('Save')); ?></button>
                 </div>
             </form>
         </div>
     </div>
 </div>
<?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/blog/category_modal.blade.php ENDPATH**/ ?>