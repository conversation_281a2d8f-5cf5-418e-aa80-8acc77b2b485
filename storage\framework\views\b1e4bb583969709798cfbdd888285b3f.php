<?php
$limit = 3;
$orderBy = 'asc';
if (isset($singelWidgetData->widget_content)) {
    $widgetContent = $singelWidgetData->getTranslation('widget_content');
    $limit = isset($widgetContent['display_per_page']) ? $widgetContent['display_per_page'] : 9;
    $orderBy = isset($widgetContent['order_by']) ? $widgetContent['order_by'] : 'asc';
}
$destinations = destinations('', $perPage = $limit, $orderBy);
?>

<?php if($destinations): ?> 
<div class="destination-section pt-120 mb-120">
    <div class="container">
        <div class="row g-lg-4 gy-5 mb-70">
            <?php if($destinations->count()>0): ?>
            <?php $__currentLoopData = $destinations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xl-3 col-lg-4 col-sm-6">
                <?php echo $__env->make('frontend.template-'.$templateId.'.partials.destination_card', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
            <div class="col-xl-12 col-lg-12 col-sm-12"><h2 class="text-center"><?php echo e(translate('No Data Found')); ?></h2></div>
            <?php endif; ?>
        </div>
        <div class="row">
            <?php echo $destinations->links('vendor.pagination.custom'); ?>

        </div>
    </div>
</div>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/frontend/template-1/all-destination.blade.php ENDPATH**/ ?>