<?php $__env->startSection('content'); ?>

<div class="installment-section">
    <div class="installment-wrap">
        <input type="hidden" id="final" value="<?php echo e(request()->is('install/final') || request()->is('install/import-demo') ? 'final' : ''); ?>" />
        <ul class="installment-form-step" id="progressbar">
            <li class=" <?php echo e(request()->is('install/final')   ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <path
                            d="M7.61863 1.31246C7.45454 1.14843 7.23202 1.05627 7 1.05627C6.76798 1.05627 6.54546 1.14843 6.38138 1.31246L0.565253 7.12771C0.524576 7.16839 0.49231 7.21668 0.470295 7.26983C0.448281 7.32297 0.436951 7.37994 0.436951 7.43746C0.436951 7.49499 0.448281 7.55195 0.470295 7.6051C0.49231 7.65825 0.524576 7.70654 0.565253 7.74721C0.647404 7.82936 0.758824 7.87551 0.875003 7.87551C0.932529 7.87551 0.989492 7.86418 1.04264 7.84217C1.09579 7.82016 1.14408 7.78789 1.18475 7.74721L7 1.93109L12.8153 7.74721C12.8974 7.82936 13.0088 7.87551 13.125 7.87551C13.2412 7.87551 13.3526 7.82936 13.4348 7.74721C13.5169 7.66506 13.5631 7.55364 13.5631 7.43746C13.5631 7.32128 13.5169 7.20986 13.4348 7.12771L11.375 5.06884V2.18746C11.375 2.07143 11.3289 1.96015 11.2469 1.8781C11.1648 1.79606 11.0535 1.74996 10.9375 1.74996H10.0625C9.94647 1.74996 9.83519 1.79606 9.75314 1.8781C9.6711 1.96015 9.625 2.07143 9.625 2.18746V3.31884L7.61863 1.31246Z" />
                        <path
                            d="M7 2.88135L12.25 8.13135V11.8125C12.25 12.1606 12.1117 12.4944 11.8656 12.7406C11.6194 12.9867 11.2856 13.125 10.9375 13.125H3.0625C2.7144 13.125 2.38056 12.9867 2.13442 12.7406C1.88828 12.4944 1.75 12.1606 1.75 11.8125V8.13135L7 2.88135Z" />
                    </svg>
                </div>
                <p>Installation</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>

            <li class=" <?php echo e(request()->is('install/final') ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <g>
                            <path
                                d="M1.75 1.75C1.28587 1.75 0.840752 1.93437 0.512563 2.26256C0.184374 2.59075 0 3.03587 0 3.5L0 4.375C0 4.83913 0.184374 5.28425 0.512563 5.61244C0.840752 5.94063 1.28587 6.125 1.75 6.125H2.625V7.875H1.75C1.28587 7.875 0.840752 8.05937 0.512563 8.38756C0.184374 8.71575 0 9.16087 0 9.625L0 10.5C0 10.9641 0.184374 11.4092 0.512563 11.7374C0.840752 12.0656 1.28587 12.25 1.75 12.25H12.25C12.7141 12.25 13.1592 12.0656 13.4874 11.7374C13.8156 11.4092 14 10.9641 14 10.5V9.625C14 9.16087 13.8156 8.71575 13.4874 8.38756C13.1592 8.05937 12.7141 7.875 12.25 7.875H11.375V6.125H12.25C12.7141 6.125 13.1592 5.94063 13.4874 5.61244C13.8156 5.28425 14 4.83913 14 4.375V3.5C14 3.03587 13.8156 2.59075 13.4874 2.26256C13.1592 1.93437 12.7141 1.75 12.25 1.75H1.75ZM2.1875 4.375C2.07147 4.375 1.96019 4.32891 1.87814 4.24686C1.79609 4.16481 1.75 4.05353 1.75 3.9375C1.75 3.82147 1.79609 3.71019 1.87814 3.62814C1.96019 3.54609 2.07147 3.5 2.1875 3.5C2.30353 3.5 2.41481 3.54609 2.49686 3.62814C2.57891 3.71019 2.625 3.82147 2.625 3.9375C2.625 4.05353 2.57891 4.16481 2.49686 4.24686C2.41481 4.32891 2.30353 4.375 2.1875 4.375ZM3.9375 4.375C3.82147 4.375 3.71019 4.32891 3.62814 4.24686C3.54609 4.16481 3.5 4.05353 3.5 3.9375C3.5 3.82147 3.54609 3.71019 3.62814 3.62814C3.71019 3.54609 3.82147 3.5 3.9375 3.5C4.05353 3.5 4.16481 3.54609 4.24686 3.62814C4.32891 3.71019 4.375 3.82147 4.375 3.9375C4.375 4.05353 4.32891 4.16481 4.24686 4.24686C4.16481 4.32891 4.05353 4.375 3.9375 4.375ZM2.1875 10.5C2.07147 10.5 1.96019 10.4539 1.87814 10.3719C1.79609 10.2898 1.75 10.1785 1.75 10.0625C1.75 9.94647 1.79609 9.83519 1.87814 9.75314C1.96019 9.67109 2.07147 9.625 2.1875 9.625C2.30353 9.625 2.41481 9.67109 2.49686 9.75314C2.57891 9.83519 2.625 9.94647 2.625 10.0625C2.625 10.1785 2.57891 10.2898 2.49686 10.3719C2.41481 10.4539 2.30353 10.5 2.1875 10.5ZM3.9375 10.5C3.82147 10.5 3.71019 10.4539 3.62814 10.3719C3.54609 10.2898 3.5 10.1785 3.5 10.0625C3.5 9.94647 3.54609 9.83519 3.62814 9.75314C3.71019 9.67109 3.82147 9.625 3.9375 9.625C4.05353 9.625 4.16481 9.67109 4.24686 9.75314C4.32891 9.83519 4.375 9.94647 4.375 10.0625C4.375 10.1785 4.32891 10.2898 4.24686 10.3719C4.16481 10.4539 4.05353 10.5 3.9375 10.5ZM10.5 6.125V7.875H3.5V6.125H10.5Z" />
                        </g>
                    </svg>
                </div>
                <p>Server Requirement</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>
            <li class="<?php echo e(request()->is('install/final') ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <g>
                            <path
                                d="M13.8483 4.41536C13.8483 3.81928 13.7311 3.24161 13.5017 2.69576C13.279 2.17001 12.9608 1.69783 12.5556 1.29263C12.1504 0.887425 11.6783 0.569292 11.1525 0.346598C10.6083 0.115533 10.029 0 9.43291 0C8.83683 0 8.25917 0.117207 7.71332 0.346598C7.18758 0.569292 6.71538 0.887425 6.31018 1.29263C5.90498 1.69783 5.58685 2.17001 5.36416 2.69576C5.13309 3.23994 5.01756 3.81928 5.01756 4.41536C5.01756 5.16046 5.20509 5.89049 5.56173 6.54016L0.275694 11.8212C0.153464 11.9434 0.114953 12.1293 0.190301 12.3051C0.20537 12.3419 0.230486 12.3754 0.25895 12.4038L1.44274 13.5876C1.47121 13.6161 1.50469 13.6396 1.54153 13.6563C1.71734 13.7316 1.9032 13.6948 2.02543 13.5709L2.75714 12.8392L3.81032 13.8924C3.83879 13.9208 3.87228 13.9443 3.9091 13.961C4.08491 14.0364 4.27078 13.9995 4.39299 13.8756L5.56004 12.7086C5.58851 12.6801 5.61195 12.6466 5.62869 12.6098C5.70404 12.434 5.6672 12.2481 5.5433 12.1259L4.50685 11.0895L7.30643 8.28821C7.95441 8.64486 8.68612 8.83239 9.43122 8.83239C10.0273 8.83239 10.605 8.71518 11.1508 8.48579C11.6766 8.2631 12.1488 7.94497 12.554 7.53976C12.9591 7.13458 13.2773 6.6624 13.5 6.13663C13.7311 5.5891 13.8483 5.01144 13.8483 4.41536ZM10.9801 5.96417C10.5665 6.37774 10.0156 6.60546 9.43126 6.60546C8.8469 6.60546 8.29601 6.37774 7.88245 5.96417C7.46888 5.55059 7.23949 4.99972 7.23949 4.41536C7.23949 3.82932 7.4672 3.28012 7.88077 2.86655C8.29435 2.45298 8.84524 2.22526 9.42958 2.22526C10.0139 2.22526 10.5648 2.45298 10.9784 2.86655C11.392 3.28012 11.6197 3.831 11.6197 4.41536C11.623 4.99972 11.3953 5.55059 10.9801 5.96417Z" />
                        </g>
                    </svg>
                </div>
                <p>Permission</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>
            <li class=" <?php echo e(request()->is('install/final') ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <g>
                            <path
                                d="M8.22937 0.91875C7.868 -0.30625 6.132 -0.30625 5.77062 0.91875L5.68313 1.21625C5.62913 1.39965 5.53477 1.56863 5.40697 1.71081C5.27916 1.85299 5.12116 1.96476 4.94453 2.03793C4.76791 2.1111 4.57715 2.1438 4.38624 2.13365C4.19533 2.1235 4.00912 2.07074 3.84125 1.97925L3.57 1.8305C2.44737 1.21975 1.21975 2.44738 1.83137 3.56912L1.97925 3.84125C2.3695 4.55875 1.99937 5.45213 1.21625 5.68313L0.91875 5.77062C-0.30625 6.132 -0.30625 7.868 0.91875 8.22937L1.21625 8.31688C1.39965 8.37087 1.56863 8.46523 1.71081 8.59303C1.85299 8.72084 1.96476 8.87884 2.03793 9.05547C2.1111 9.23209 2.1438 9.42285 2.13365 9.61376C2.1235 9.80467 2.07074 9.99088 1.97925 10.1587L1.8305 10.43C1.21975 11.5526 2.44738 12.7802 3.56912 12.1686L3.84125 12.0208C4.00912 11.9293 4.19533 11.8765 4.38624 11.8664C4.57715 11.8562 4.76791 11.8889 4.94453 11.9621C5.12116 12.0352 5.27916 12.147 5.40697 12.2892C5.53477 12.4314 5.62913 12.6004 5.68313 12.7837L5.77062 13.0813C6.132 14.3062 7.868 14.3062 8.22937 13.0813L8.31688 12.7837C8.37087 12.6004 8.46523 12.4314 8.59303 12.2892C8.72084 12.147 8.87884 12.0352 9.05547 11.9621C9.23209 11.8889 9.42285 11.8562 9.61376 11.8664C9.80467 11.8765 9.99088 11.9293 10.1587 12.0208L10.43 12.1695C11.5526 12.7802 12.7802 11.5526 12.1686 10.4309L12.0208 10.1587C11.9293 9.99088 11.8765 9.80467 11.8664 9.61376C11.8562 9.42285 11.8889 9.23209 11.9621 9.05547C12.0352 8.87884 12.147 8.72084 12.2892 8.59303C12.4314 8.46523 12.6004 8.37087 12.7837 8.31688L13.0813 8.22937C14.3062 7.868 14.3062 6.132 13.0813 5.77062L12.7837 5.68313C12.6004 5.62913 12.4314 5.53477 12.2892 5.40697C12.147 5.27916 12.0352 5.12116 11.9621 4.94453C11.8889 4.76791 11.8562 4.57715 11.8664 4.38624C11.8765 4.19533 11.9293 4.00912 12.0208 3.84125L12.1695 3.57C12.7802 2.44737 11.5526 1.21975 10.4309 1.83137L10.1587 1.97925C9.99088 2.07074 9.80467 2.1235 9.61376 2.13365C9.42285 2.1438 9.23209 2.1111 9.05547 2.03793C8.87884 1.96476 8.72084 1.85299 8.59303 1.71081C8.46523 1.56863 8.37087 1.39965 8.31688 1.21625L8.22937 0.91875ZM7 9.56375C6.32005 9.56375 5.66795 9.29364 5.18716 8.81285C4.70636 8.33205 4.43625 7.67995 4.43625 7C4.43625 6.32005 4.70636 5.66795 5.18716 5.18716C5.66795 4.70636 6.32005 4.43625 7 4.43625C7.67972 4.43625 8.33159 4.70627 8.81223 5.1869C9.29286 5.66753 9.56287 6.31941 9.56287 6.99913C9.56287 7.67884 9.29286 8.33072 8.81223 8.81135C8.33159 9.29198 7.67972 9.562 7 9.562V9.56375Z" />
                        </g>
                    </svg>
                </div>
                <p>Environment Settings</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>
            <li class=" <?php echo e(request()->is('install/final') ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-patch-check" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M10.354 6.146a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7 8.793l2.646-2.647a.5.5 0 0 1 .708 0"/>
                        <path d="m10.273 2.513-.921-.944.715-.698.622.637.89-.011a2.89 2.89 0 0 1 2.924 2.924l-.01.89.636.622a2.89 2.89 0 0 1 0 4.134l-.637.622.011.89a2.89 2.89 0 0 1-2.924 2.924l-.89-.01-.622.636a2.89 2.89 0 0 1-4.134 0l-.622-.637-.89.011a2.89 2.89 0 0 1-2.924-2.924l.01-.89-.636-.622a2.89 2.89 0 0 1 0-4.134l.637-.622-.011-.89a2.89 2.89 0 0 1 2.924-2.924l.89.01.622-.636a2.89 2.89 0 0 1 4.134 0l-.715.698a1.89 1.89 0 0 0-2.704 0l-.92.944-1.32-.016a1.89 1.89 0 0 0-1.911 1.912l.016 1.318-.944.921a1.89 1.89 0 0 0 0 2.704l.944.92-.016 1.32a1.89 1.89 0 0 0 1.912 1.911l1.318-.016.921.944a1.89 1.89 0 0 0 2.704 0l.92-.944 1.32.016a1.89 1.89 0 0 0 1.911-1.912l-.016-1.318.944-.921a1.89 1.89 0 0 0 0-2.704l-.944-.92.016-1.32a1.89 1.89 0 0 0-1.912-1.911z"/>
                      </svg>
                </div>
                <p>Verify Purchase Code </p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>
            <li class=" <?php echo e(request()->is('install/final') ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <path
                            d="M3.416 1.55487C4.35575 1.12787 5.62363 0.875 7 0.875C8.37638 0.875 9.64425 1.12787 10.584 1.55487C11.4844 1.96437 12.25 2.61888 12.25 3.5C12.25 4.38112 11.4844 5.03562 10.584 5.44513C9.64425 5.87213 8.37638 6.125 7 6.125C5.62363 6.125 4.35575 5.87213 3.416 5.44513C2.51562 5.03562 1.75 4.38112 1.75 3.5C1.75 2.61888 2.51562 1.96437 3.416 1.55487Z" />
                        <path
                            d="M1.75 5.39087V6.12499C1.75 7.00612 2.51562 7.66062 3.416 8.07012C4.35575 8.49624 5.62363 8.74999 7 8.74999C8.37638 8.74999 9.64425 8.49712 10.584 8.07012C11.4844 7.66062 12.25 7.00612 12.25 6.12499V5.39087C11.8501 5.76887 11.3715 6.04799 10.9462 6.24137C9.86825 6.73137 8.47175 6.99999 7 6.99999C5.52825 6.99999 4.13175 6.73137 3.05375 6.24137C2.6285 6.04887 2.14987 5.76887 1.75 5.39087Z" />
                        <path
                            d="M1.75 8.01587V8.74999C1.75 9.63112 2.51562 10.2856 3.416 10.6951C4.35575 11.1221 5.62363 11.375 7 11.375C8.37638 11.375 9.64425 11.1221 10.584 10.6951C11.4844 10.2856 12.25 9.63112 12.25 8.74999V8.01587C11.8501 8.39387 11.3715 8.67299 10.9462 8.86637C9.86825 9.35637 8.47175 9.62499 7 9.62499C5.52825 9.62499 4.13175 9.35637 3.05375 8.86637C2.6285 8.67387 2.14987 8.39387 1.75 8.01587Z" />
                        <path
                            d="M1.75 10.6409V11.375C1.75 12.2561 2.51562 12.9106 3.416 13.3201C4.35575 13.7471 5.62363 14 7 14C8.37638 14 9.64425 13.7471 10.584 13.3201C11.4844 12.9106 12.25 12.2561 12.25 11.375V10.6409C11.8501 11.0189 11.3715 11.298 10.9462 11.4914C9.86825 11.9814 8.47175 12.25 7 12.25C5.52825 12.25 4.13175 11.9814 3.05375 11.4914C2.6285 11.2989 2.14987 11.0189 1.75 10.6409Z" />
                    </svg>
                </div>
                <p>Database</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>

            <li class=" <?php echo e(request()->is('install/final') ||  request()->is('install/import-demo')  ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <g>
                            <path
                                d="M1.75 1.75C1.28587 1.75 0.840752 1.93437 0.512563 2.26256C0.184374 2.59075 0 3.03587 0 3.5L0 4.375C0 4.83913 0.184374 5.28425 0.512563 5.61244C0.840752 5.94063 1.28587 6.125 1.75 6.125H2.625V7.875H1.75C1.28587 7.875 0.840752 8.05937 0.512563 8.38756C0.184374 8.71575 0 9.16087 0 9.625L0 10.5C0 10.9641 0.184374 11.4092 0.512563 11.7374C0.840752 12.0656 1.28587 12.25 1.75 12.25H12.25C12.7141 12.25 13.1592 12.0656 13.4874 11.7374C13.8156 11.4092 14 10.9641 14 10.5V9.625C14 9.16087 13.8156 8.71575 13.4874 8.38756C13.1592 8.05937 12.7141 7.875 12.25 7.875H11.375V6.125H12.25C12.7141 6.125 13.1592 5.94063 13.4874 5.61244C13.8156 5.28425 14 4.83913 14 4.375V3.5C14 3.03587 13.8156 2.59075 13.4874 2.26256C13.1592 1.93437 12.7141 1.75 12.25 1.75H1.75ZM2.1875 4.375C2.07147 4.375 1.96019 4.32891 1.87814 4.24686C1.79609 4.16481 1.75 4.05353 1.75 3.9375C1.75 3.82147 1.79609 3.71019 1.87814 3.62814C1.96019 3.54609 2.07147 3.5 2.1875 3.5C2.30353 3.5 2.41481 3.54609 2.49686 3.62814C2.57891 3.71019 2.625 3.82147 2.625 3.9375C2.625 4.05353 2.57891 4.16481 2.49686 4.24686C2.41481 4.32891 2.30353 4.375 2.1875 4.375ZM3.9375 4.375C3.82147 4.375 3.71019 4.32891 3.62814 4.24686C3.54609 4.16481 3.5 4.05353 3.5 3.9375C3.5 3.82147 3.54609 3.71019 3.62814 3.62814C3.71019 3.54609 3.82147 3.5 3.9375 3.5C4.05353 3.5 4.16481 3.54609 4.24686 3.62814C4.32891 3.71019 4.375 3.82147 4.375 3.9375C4.375 4.05353 4.32891 4.16481 4.24686 4.24686C4.16481 4.32891 4.05353 4.375 3.9375 4.375ZM2.1875 10.5C2.07147 10.5 1.96019 10.4539 1.87814 10.3719C1.79609 10.2898 1.75 10.1785 1.75 10.0625C1.75 9.94647 1.79609 9.83519 1.87814 9.75314C1.96019 9.67109 2.07147 9.625 2.1875 9.625C2.30353 9.625 2.41481 9.67109 2.49686 9.75314C2.57891 9.83519 2.625 9.94647 2.625 10.0625C2.625 10.1785 2.57891 10.2898 2.49686 10.3719C2.41481 10.4539 2.30353 10.5 2.1875 10.5ZM3.9375 10.5C3.82147 10.5 3.71019 10.4539 3.62814 10.3719C3.54609 10.2898 3.5 10.1785 3.5 10.0625C3.5 9.94647 3.54609 9.83519 3.62814 9.75314C3.71019 9.67109 3.82147 9.625 3.9375 9.625C4.05353 9.625 4.16481 9.67109 4.24686 9.75314C4.32891 9.83519 4.375 9.94647 4.375 10.0625C4.375 10.1785 4.32891 10.2898 4.24686 10.3719C4.16481 10.4539 4.05353 10.5 3.9375 10.5ZM10.5 6.125V7.875H3.5V6.125H10.5Z" />
                        </g>
                    </svg>
                </div>
                <p>Demo Import</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>
            <li class=" <?php echo e(request()->is('install/final') ? 'active' : ''); ?>">
                <div class="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
                        <g>
                            <path
                                d="M1.75 1.75C1.28587 1.75 0.840752 1.93437 0.512563 2.26256C0.184374 2.59075 0 3.03587 0 3.5L0 4.375C0 4.83913 0.184374 5.28425 0.512563 5.61244C0.840752 5.94063 1.28587 6.125 1.75 6.125H2.625V7.875H1.75C1.28587 7.875 0.840752 8.05937 0.512563 8.38756C0.184374 8.71575 0 9.16087 0 9.625L0 10.5C0 10.9641 0.184374 11.4092 0.512563 11.7374C0.840752 12.0656 1.28587 12.25 1.75 12.25H12.25C12.7141 12.25 13.1592 12.0656 13.4874 11.7374C13.8156 11.4092 14 10.9641 14 10.5V9.625C14 9.16087 13.8156 8.71575 13.4874 8.38756C13.1592 8.05937 12.7141 7.875 12.25 7.875H11.375V6.125H12.25C12.7141 6.125 13.1592 5.94063 13.4874 5.61244C13.8156 5.28425 14 4.83913 14 4.375V3.5C14 3.03587 13.8156 2.59075 13.4874 2.26256C13.1592 1.93437 12.7141 1.75 12.25 1.75H1.75ZM2.1875 4.375C2.07147 4.375 1.96019 4.32891 1.87814 4.24686C1.79609 4.16481 1.75 4.05353 1.75 3.9375C1.75 3.82147 1.79609 3.71019 1.87814 3.62814C1.96019 3.54609 2.07147 3.5 2.1875 3.5C2.30353 3.5 2.41481 3.54609 2.49686 3.62814C2.57891 3.71019 2.625 3.82147 2.625 3.9375C2.625 4.05353 2.57891 4.16481 2.49686 4.24686C2.41481 4.32891 2.30353 4.375 2.1875 4.375ZM3.9375 4.375C3.82147 4.375 3.71019 4.32891 3.62814 4.24686C3.54609 4.16481 3.5 4.05353 3.5 3.9375C3.5 3.82147 3.54609 3.71019 3.62814 3.62814C3.71019 3.54609 3.82147 3.5 3.9375 3.5C4.05353 3.5 4.16481 3.54609 4.24686 3.62814C4.32891 3.71019 4.375 3.82147 4.375 3.9375C4.375 4.05353 4.32891 4.16481 4.24686 4.24686C4.16481 4.32891 4.05353 4.375 3.9375 4.375ZM2.1875 10.5C2.07147 10.5 1.96019 10.4539 1.87814 10.3719C1.79609 10.2898 1.75 10.1785 1.75 10.0625C1.75 9.94647 1.79609 9.83519 1.87814 9.75314C1.96019 9.67109 2.07147 9.625 2.1875 9.625C2.30353 9.625 2.41481 9.67109 2.49686 9.75314C2.57891 9.83519 2.625 9.94647 2.625 10.0625C2.625 10.1785 2.57891 10.2898 2.49686 10.3719C2.41481 10.4539 2.30353 10.5 2.1875 10.5ZM3.9375 10.5C3.82147 10.5 3.71019 10.4539 3.62814 10.3719C3.54609 10.2898 3.5 10.1785 3.5 10.0625C3.5 9.94647 3.54609 9.83519 3.62814 9.75314C3.71019 9.67109 3.82147 9.625 3.9375 9.625C4.05353 9.625 4.16481 9.67109 4.24686 9.75314C4.32891 9.83519 4.375 9.94647 4.375 10.0625C4.375 10.1785 4.32891 10.2898 4.24686 10.3719C4.16481 10.4539 4.05353 10.5 3.9375 10.5ZM10.5 6.125V7.875H3.5V6.125H10.5Z" />
                        </g>
                    </svg>
                </div>
                <p>Finished</p>
                <div class="check">
                    <svg xmlns="http://www.w3.org/2000/svg" width="8" height="6" viewBox="0 0 8 6">
                        <path
                            d="M6.75646 0.191292C6.8947 0.0672398 7.07867 -0.00134136 7.26954 1.98831e-05C7.46041 0.00138113 7.64325 0.0725785 7.77949 0.198588C7.91573 0.324598 7.9947 0.495564 7.99974 0.675411C8.00479 0.855259 7.93551 1.02992 7.80653 1.16254L3.89085 5.77827C3.82352 5.84663 3.74226 5.90148 3.65192 5.93956C3.56158 5.97764 3.46403 5.99815 3.36509 5.99988C3.26614 6.00161 3.16785 5.98451 3.07608 5.94961C2.98431 5.91471 2.90094 5.86273 2.83097 5.79677L0.234262 3.34923C0.161948 3.28572 0.103946 3.20913 0.063718 3.12403C0.0234896 3.03893 0.00185823 2.94707 0.000114546 2.85392C-0.00162914 2.76077 0.0165507 2.66824 0.053569 2.58186C0.0905874 2.49547 0.145686 2.417 0.215578 2.35112C0.28547 2.28525 0.368724 2.23331 0.460372 2.19842C0.552021 2.16353 0.650187 2.14639 0.749014 2.14804C0.847841 2.14968 0.945304 2.17007 1.03559 2.20799C1.12588 2.24591 1.20713 2.30057 1.27452 2.36873L3.32951 4.30475L6.73782 0.211642C6.74395 0.204521 6.74952 0.197726 6.75646 0.191292Z" />
                    </svg>
                </div>
            </li>
        </ul>

        <div id="msform" class="installment-form">
            <fieldset class="installation">
                <form>
                    <div class="step-title mb-25">
                        <h3>Let's Start Installation for ready <br> Triprex Application!</h3>
                        <p>We understand the value of time and the importance of a seamless installation experience. Say
                            goodbye to complex setups and welcome a new era of convenience with our user-friendly
                            application installer.</p>
                    </div>
                    <div class="step-btn-group text-center">
                        <button type="button" class="next next-btn">Get Started
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                </g>
                            </svg>
                        </button>
                    </div>
                </form>
            </fieldset>



            <fieldset class="server-requirement">
                <form action="<?php echo e(route('install.requirement')); ?>">
                    <div class="step-title mb-25">
                        <h3>Server Requirements</h3>
                    </div>
                    <div class="alert alert-danger server-error-message"> </div>

                    <div class="requirement_html">

                        <?php if(isset($phpSupportInfo, $requirements)): ?>
                            <?php echo $__env->make('installer.requirements', [
                                'phpSupportInfo' => $phpSupportInfo,
                                'requirements' => $requirements,
                            ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>

                    </div>
                    <div class="step-btn-group">
                        <button class="prev previous-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M0.158627 11.6155L5.61304 6.161C5.80898 5.93221 6.15332 5.90554 6.38211 6.10152C6.6109 6.29746 6.63757 6.64179 6.44159 6.87059C6.42331 6.89191 6.40344 6.91183 6.38211 6.93007L1.86039 11.4573H23.4546C23.7558 11.4573 24 11.7015 24 12.0027C24 12.304 23.7558 12.5482 23.4546 12.5482H1.86039L6.38211 17.0699C6.6109 17.2658 6.63757 17.6102 6.44159 17.839C6.24561 18.0677 5.90132 18.0944 5.67253 17.8984C5.6512 17.8802 5.63128 17.8603 5.61304 17.839L0.158579 12.3845C-0.0528717 12.1718 -0.0528698 11.8282 0.158627 11.6155Z" />
                                </g>
                            </svg>
                            Previous
                        </button>
                        <button type="button" class="installer-btn next-btn">Next
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                </g>
                            </svg>
                        </button>
                    </div>
                </form>
            </fieldset>

            <fieldset class="permission">
                <form action="<?php echo e(route('install.permission')); ?>">

                    <div class="step-title mb-25">
                        <h3>Permission</h3>
                    </div>
                    <div class="alert alert-danger server-error-message"> </div>
                    <div class="permission-html">
                        <?php if(isset($permissions)): ?>
                            <?php echo $__env->make('installer.permission', ['permissions' => $permissions], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>
                    </div>
                    <div class="step-btn-group">
                        <button class="prev previous-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M0.158627 11.6155L5.61304 6.161C5.80898 5.93221 6.15332 5.90554 6.38211 6.10152C6.6109 6.29746 6.63757 6.64179 6.44159 6.87059C6.42331 6.89191 6.40344 6.91183 6.38211 6.93007L1.86039 11.4573H23.4546C23.7558 11.4573 24 11.7015 24 12.0027C24 12.304 23.7558 12.5482 23.4546 12.5482H1.86039L6.38211 17.0699C6.6109 17.2658 6.63757 17.6102 6.44159 17.839C6.24561 18.0677 5.90132 18.0944 5.67253 17.8984C5.6512 17.8802 5.63128 17.8603 5.61304 17.839L0.158579 12.3845C-0.0528717 12.1718 -0.0528698 11.8282 0.158627 11.6155Z" />
                                </g>
                            </svg>
                            Previous
                        </button>
                        <button type="button" class="installer-btn next-btn">Next
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                </g>
                            </svg>
                        </button>
                    </div>
                </form>
            </fieldset>
            <fieldset class="environment-settings">
                <form action="<?php echo e(route('install.environment')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="step-title mb-25">
                        <h3>Environment Settings</h3>
                    </div>
                    <div class="enviroment-wrap">
                        <div class="app-debug">
                            <h6>App debug :</h6>
                            <div class="form-check-wrap">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="true" name="app_debug"
                                        id="appDubugTrue" checked>
                                    <label class="form-check-label" for="appDubugTrue">
                                        True
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="app_debug" value="false"
                                        id="appDubugFalse">
                                    <label class="form-check-label" for="appDubugFalse">
                                        False
                                    </label>
                                </div>
                            </div>
                            <span class="text-danger error-text app_debug_err"></span>

                        </div>
                        <div class="form-input-area">
                            <div class="w-50">
                                <div class="form-inner">
                                    <label>App name</label>
                                    <input type="text" name="app_name">
                                </div>
                                <span class="text-danger error-text app_name_err"></span>
                            </div>

                            <div class="w-50">
                                <div class="form-inner">
                                    <label>App Environment</label>
                                    <select name="environment">
                                        <option value="local" selected="">Local</option>
                                        
                                    </select>

                                </div>
                                <span class="text-danger error-text environment_err"></span>
                            </div>
                            <div class="w-50">
                                <div class="form-inner">
                                    <label>App Log Level</label>
                                    <select name="app_log_level" id="app_log_level">
                                        <option value="debug" selected="">debug</option>
                                        <option value="info">info</option>
                                        <option value="notice">notice</option>
                                        <option value="warning">warning</option>
                                        <option value="error">error</option>
                                        <option value="critical">critical</option>
                                        <option value="alert">alert</option>
                                        <option value="emergency">emergency</option>
                                    </select>
                                </div>
                                <span class="text-danger error-text app_log_level_err"></span>
                            </div>

                            <div class="w-50">
                                <div class="form-inner">
                                    <label>App Url</label>
                                    <input type="url" name="app_url" id="app_url" value="<?php echo e(url('/')); ?>">
                                </div>
                                <span class="text-danger error-text app_url_err"></span>
                            </div>
                        </div>
                    </div>
                    <div class="step-btn-group">
                        <button class="prev previous-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M0.158627 11.6155L5.61304 6.161C5.80898 5.93221 6.15332 5.90554 6.38211 6.10152C6.6109 6.29746 6.63757 6.64179 6.44159 6.87059C6.42331 6.89191 6.40344 6.91183 6.38211 6.93007L1.86039 11.4573H23.4546C23.7558 11.4573 24 11.7015 24 12.0027C24 12.304 23.7558 12.5482 23.4546 12.5482H1.86039L6.38211 17.0699C6.6109 17.2658 6.63757 17.6102 6.44159 17.839C6.24561 18.0677 5.90132 18.0944 5.67253 17.8984C5.6512 17.8802 5.63128 17.8603 5.61304 17.839L0.158579 12.3845C-0.0528717 12.1718 -0.0528698 11.8282 0.158627 11.6155Z" />
                                </g>
                            </svg>
                            Previous
                        </button>
                        <button type="button" class="environment-btn next-btn">Next
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                </g>
                            </svg>
                        </button>
                    </div>
                </form>
            </fieldset>
            <fieldset class="server-requirement">
                <form action="<?php echo e(route('purchase.code')); ?>" method="POST">
                   <?php echo csrf_field(); ?>
                    <div class="step-title mb-25">
                        <h3>Verify Purchase Code </h3>
                    </div>
                    <div class="alert alert-danger error-message"> </div>

                    <div class="enviroment-wrap">
                        <div class="form-input-area">

                            <div class="w-100">
                                <div class="form-inner">
                                    <label> Email </label>
                                    <input type="text" name="email" >

                                </div>
                                <span class="text-danger error-text email_err"></span>
                            </div>
                            <div class="w-100">
                                <div class="form-inner">
                                    <label> Purchase Code</label>
                                    <input type="text" name="purchase_code">
                                    <input type="hidden" name="license_type" value="envato">
                                </div>
                                <span class="text-danger error-text purchase_code_err"></span>
                            </div>

                            <div class="w-100" style="text-align: left">

                                    <span> <strong style="color: red"> Note :  </strong>  If you can't find the purchase code then you can read this article. Click Here. <a href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code" target="_blank" rel="noopener noreferrer">Read More</a></span>


                            </div>

                        </div>


                        <div class="step-btn-group">
                            <button class="prev previous-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <g>
                                        <path
                                            d="M0.158627 11.6155L5.61304 6.161C5.80898 5.93221 6.15332 5.90554 6.38211 6.10152C6.6109 6.29746 6.63757 6.64179 6.44159 6.87059C6.42331 6.89191 6.40344 6.91183 6.38211 6.93007L1.86039 11.4573H23.4546C23.7558 11.4573 24 11.7015 24 12.0027C24 12.304 23.7558 12.5482 23.4546 12.5482H1.86039L6.38211 17.0699C6.6109 17.2658 6.63757 17.6102 6.44159 17.839C6.24561 18.0677 5.90132 18.0944 5.67253 17.8984C5.6512 17.8802 5.63128 17.8603 5.61304 17.839L0.158579 12.3845C-0.0528717 12.1718 -0.0528698 11.8282 0.158627 11.6155Z" />
                                    </g>
                                </svg>
                                Previous
                            </button>
                            <button type="button" class="environment-btn next-btn">Next
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <g>
                                        <path
                                            d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                    </g>
                                </svg>
                            </button>
                        </div>
                    </div>
                </form>
            </fieldset>
            <fieldset class="database">
                <form action="<?php echo e(route('install.database')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="step-title mb-25">
                        <h3>Database Connection</h3>
                    </div>
                    <div class="alert alert-danger error-message"> </div>
                    <div class="enviroment-wrap">
                        <div class="form-input-area">

                            <div class="w-50">
                                <div class="form-inner">
                                    <label>Database Connection</label>
                                    <select name="database_connection" id="database_connection">
                                        <option value="mysql" selected="">mysql</option>
                                    </select>
                                </div>
                                <span class="text-danger error-text app_url_err"></span>
                            </div>


                            <div class="w-50">
                                <div class="form-inner">
                                    <label>Database Host</label>
                                    <input type="text" name="database_hostname" id="database_hostname"
                                        value="127.0.0.1">
                                </div>
                                <span class="text-danger error-text database_hostname_err"></span>
                            </div>

                            <div class="w-50">
                                <div class="form-inner">
                                    <label>Database Port</label>
                                    <input type="number" name="database_port" id="database_port" value="3306">
                                </div>
                                <span class="text-danger error-text database_port_err"></span>
                            </div>

                            <div class="w-50">
                                <div class="form-inner">
                                    <label> Database Name</label>
                                    <input type="text" name="database_name" id="database_name">
                                </div>
                                <span class="text-danger error-text database_name_err"></span>
                            </div>


                            <div class="w-50">

                                <div class="form-inner">
                                    <label> Database User Name</label>
                                    <input type="text" name="database_username" id="database_username">
                                </div>
                                <span class="text-danger error-text database_username_err"></span>

                            </div>


                            <div class="w-50">

                                <div class="form-inner">
                                    <label> Database Password</label>
                                    <input type="password" name="database_password" id="database_password">
                                </div>
                                <span class="text-danger error-text database_password_err"></span>

                            </div>

                        </div>
                    </div>





                    <div class="step-btn-group">
                        <button class="prev previous-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M0.158627 11.6155L5.61304 6.161C5.80898 5.93221 6.15332 5.90554 6.38211 6.10152C6.6109 6.29746 6.63757 6.64179 6.44159 6.87059C6.42331 6.89191 6.40344 6.91183 6.38211 6.93007L1.86039 11.4573H23.4546C23.7558 11.4573 24 11.7015 24 12.0027C24 12.304 23.7558 12.5482 23.4546 12.5482H1.86039L6.38211 17.0699C6.6109 17.2658 6.63757 17.6102 6.44159 17.839C6.24561 18.0677 5.90132 18.0944 5.67253 17.8984C5.6512 17.8802 5.63128 17.8603 5.61304 17.839L0.158579 12.3845C-0.0528717 12.1718 -0.0528698 11.8282 0.158627 11.6155Z" />
                                </g>
                            </svg>
                            Previous
                        </button>
                        <button type="button" class="environment-btn next-btn">Next
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24">
                                <g>
                                    <path
                                        d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                </g>
                            </svg>
                        </button>
                    </div>

                </form>
            </fieldset>


            <fieldset class="demo-content <?php echo e(request()->is('install/import-demo') ? 'active' : ''); ?>">
                <form action="<?php echo e(route('install.demo')); ?>">

                    <div class="step-title mb-25">
                        <h3>Demo Content Import</h3>
                    </div>


                        <div class="form-input-area">

                            <div class="form-check">
                                <input class="form-check-input" checked type="checkbox" name="demo_import" id="flexCheckChecked" >
                                <label class="form-check-label" for="flexCheckChecked">
                                    You want to import demo content ?
                                </label>
                              </div>

                        </div>



                    <div class="step-btn-group-import">
                          <button type="submit" class="btn-process  next-btn" > Import   <span class="btn-ring"></span> </button>
                    </div>
                </form>
            </fieldset>

            <fieldset class="Finished <?php echo e(request()->is('install/final') ? 'active' : ''); ?> justify-centent-center">


                <div class="loader-installer <?php echo e(request()->is('install/final') ? 'installer-hide' : ''); ?>">
                    <div class="bar1"></div>
                </div>

                <?php if(request()->is('install/final')): ?>
                    <canvas id="custom_canvas"> </canvas>
                    <div class="info mb-3">
                        <h1>🎉 Congratulations!</h1>
                        <h2>You're All Set Up and Ready to Go!</h2>
                    </div>
                    <div class="congulation">
                        <p>Your <b><?php echo e(env('APP_NAME')); ?></b> setup is complete, and you're now ready to experience the
                            seamless
                            benefits it brings. We applaud you for taking this step, and we're thrilled to have you on
                            board.</p>
                    </div>


                    <div class="access-user">
                        <div>
                            <h2>Admin Access:</h2>
                            <p><b>Email:</b> <EMAIL></p>
                            <p><b>Password:</b> 123456789</p>
                        </div>

                        <div>
                            <h2>Merchant Access:</h2>
                            <p><b>Email:</b> <EMAIL></p>
                            <p><b>Password:</b> 123456789</p>
                        </div>

                        <div>
                            <h2>Customer Access:</h2>
                            <p><b>Email:</b> <EMAIL></p>
                            <p><b>Password:</b> 123456789</p>
                        </div>
                    </div>
                    <div class="step-btn-group">
                        <div class="step-btn-group">
                            <a href="<?php echo e(url('/')); ?>" target="_blank" class="next-btn">Frontend Url
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24">
                                    <g>
                                        <path
                                            d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                    </g>
                                </svg>
                            </a>
                        </div>
                        <div class="step-btn-group">
                            <a href="<?php echo e(url('/admin/login')); ?>" target="_blank" class="next-btn">Backend Url
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24">
                                    <g>
                                        <path
                                            d="M23.8414 11.6155L18.387 6.161C18.191 5.93221 17.8467 5.90554 17.6179 6.10152C17.3891 6.29746 17.3624 6.64179 17.5584 6.87059C17.5767 6.89191 17.5966 6.91183 17.6179 6.93007L22.1396 11.4573H0.545428C0.244214 11.4573 0 11.7015 0 12.0027C0 12.304 0.244214 12.5482 0.545428 12.5482H22.1396L17.6179 17.0699C17.3891 17.2658 17.3624 17.6102 17.5584 17.839C17.7544 18.0677 18.0987 18.0944 18.3275 17.8984C18.3488 17.8802 18.3687 17.8603 18.387 17.839L23.8414 12.3845C24.0529 12.1718 24.0529 11.8282 23.8414 11.6155Z" />
                                    </g>
                                </svg>
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </fieldset>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\triprex-app\resources\views/installer/index.blade.php ENDPATH**/ ?>