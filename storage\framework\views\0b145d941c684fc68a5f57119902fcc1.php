              <?php $__env->startSection('content'); ?>

                <div class="row mb-35 g-4">
                    <div class="col-md-4">
                        <div class="page-title text-md-start">
                            <h4><?php echo e($page_title ?? ''); ?></h4>
                        </div>
                    </div>
                    <div class="col-md-8 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
                        <!-- <form action="" method="get">
                            <div class="input-with-btn d-flex jusify-content-start align-items-strech">
                                <input type="text" name="search" placeholder="<?php echo e(translate('Search your agent')); ?>...">
                                <button type="submit"><i class="bi bi-search"></i></button>
                            </div>
                        </form> -->
                        <a href="<?php echo e(route('admin.create')); ?>" class="eg-btn btn--primary back-btn"><img src="<?php echo e(asset('backend/images/icons/add-icon.svg')); ?>" alt="<?php echo e(translate('Add New')); ?>"> <?php echo e(translate('Add New')); ?></a>
                    </div>
                </div>


    <div class="row">
        <?php if($admins->count() > 0): ?>
            <?php $__currentLoopData = $admins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $admins): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="eg-profile-card text-center">
                        <div class="profile-img">
                            <?php if($admins->image): ?>
                                <img class="rounded-circle" src="<?php echo e(asset('uploads/users/' . $admins->image)); ?>"
                                    alt="<?php echo e($admins->username); ?>">
                            <?php else: ?>
                                <img class="rounded-circle" src="<?php echo e(asset('uploads/users/user.png')); ?>"
                                    alt="<?php echo e($admins->username); ?>">
                            <?php endif; ?>

                            

                            <span id="statusBlock<?php echo e($admins->id); ?>">
                                <?php if($admins->status == 1): ?>
                                    <button class="eg-btn green-light--btn"><?php echo e(translate('Active')); ?></button>
                                <?php else: ?>
                                    <button class="eg-btn red-light--btn"><?php echo e(translate('Deactive')); ?></button>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="profile-bio">
                            <h4><?php echo e($admins->fname . ' ' . $admins->lname); ?></h3>
                                <!-- <h6>Marchant ID: <?php echo e($admins->custom_id); ?></h5> -->
                        </div>
                        <div class="card-action d-flex justify-content-sm-between">
                            <div
                                class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                <a href="<?php echo e(route('admin.edit', $admins->id)); ?>" title="<?php echo e(translate('Edit')); ?>"
                                    class="eg-btn add--btn"><i class="bi bi-pencil-square"></i></a>
                                <form method="POST" action="<?php echo e(route('admin.delete', $admins->id)); ?>">
                                    <?php echo csrf_field(); ?>
                                    <input name="_method" type="hidden" value="DELETE">
                                    <button type="submit" class="eg-btn delete--btn show_confirm" data-toggle="tooltip"
                                        title='Delete'><i class="bi bi-trash"></i></button>
                                </form>

                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input flexSwitchCheckStatus" type="checkbox"
                                    data-activations-status="<?php echo e($admins->status); ?>"
                                    data-id="<?php echo e($admins->id); ?>" data-type="merchant" id="flexSwitchCheckStatus<?php echo e($admins->id); ?>"
                                    <?php echo e($admins->status == 1 ? 'checked' : ''); ?>>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div class="col-lg-12 col-md-12 col-sm-12">
                <h1><?php echo e(translate('No Data Found')); ?></h1>
            </div>
        <?php endif; ?>
    </div>
    </div>

    <?php $__env->startPush('footer'); ?>

    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/dashboard/adminlist.blade.php ENDPATH**/ ?>