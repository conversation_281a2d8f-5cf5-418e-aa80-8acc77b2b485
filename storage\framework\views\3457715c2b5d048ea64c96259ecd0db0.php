<?php $__env->startSection('content'); ?>
<style>
.select2-container {
    z-index: 9999;
}
</style>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4><?php echo e($page_title ?? ''); ?></h4>
            <div class="btn-group">
                <div>
                    <a href="<?php echo e(route('advertisement.create')); ?>" class="eg-btn btn--primary back-btn"><img
                        src="<?php echo e(asset('backend/images/icons/add-new.svg')); ?>" alt="<?php echo e(translate('Add New')); ?>">
                    <?php echo e(translate('Add New')); ?></a>
                   
                </div>
            </div>
        </div>
    </div>
    <?php
        $locale = get_setting('DEFAULT_LANGUAGE', 'en');
    ?>
    <div class="row">
        <div class="col-12">
            <div class="table-wrapper">
                <table class="eg-table table category-table">
                    <thead>
                        <tr>
                            <th><?php echo e(translate('S.N')); ?></th>
                            <th><?php echo e(translate('Attributes')); ?></th>
                            <th><?php echo e(translate('Text')); ?></th>
                            <th><?php echo e(translate('Phone')); ?></th>
                            <th><?php echo e(translate('Image')); ?></th>
                            <th><?php echo e(translate('Option')); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if($advertisement->count() > 0): ?>
                        <?php $__currentLoopData = $advertisement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $advertisement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td data-label="S.N">
                                    <?php echo e($key + 1); ?>

                                </td>
                                <td data-label="Name"><?php echo e($advertisement->title); ?></td>
                                <td data-label="Slug"><?php echo e($advertisement->text); ?></td>
                                 <td data-label="Slug"><?php echo e($advertisement->phone); ?></td>
                                <td data-label="Date"><img src="<?php echo e(asset('uploads/advertisement/'.$advertisement->image)); ?>" alt="<?php echo e($advertisement->image); ?>"></td>
                               
                                <td data-label="Option">
                                    <div
                                        class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                        <a class="eg-btn add--btn"
                                            href="<?php echo e(route('advertisement.edit', ['id' => $advertisement->id, 'lang' => get_setting('DEFAULT_LANGUAGE', 'en')])); ?>"><i
                                                class="bi bi-pencil-square"></i></a>
                                        <form method="POST" action="<?php echo e(route('advertisement.delete', $advertisement->id)); ?>">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <input name="_method" type="hidden" value="DELETE">
                                            <button type="submit" class="eg-btn delete--btn show_confirm"
                                                data-toggle="tooltip" title='Delete'><i class="bi bi-trash"></i></button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                        <tr>
                            <td colspan="7" class="text-center"><?php echo e(translate('No Data Found')); ?></td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

  

    <?php echo $__env->make('backend.tours.category.modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/advertisement/index.blade.php ENDPATH**/ ?>