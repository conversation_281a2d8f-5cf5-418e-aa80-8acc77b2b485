<!-- Modal -->
<div class="modal fade" id="replyModal" tabindex="-1" aria-labelledby="replyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="replyModalLabel"><?php echo e(translate('Leave A Reply')); ?></h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo e(route('blog.comment')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="blog_id" value="<?php echo e($blog_details->id); ?>">
                <input type="hidden" name="parent_id" id="parent_id" value="">
                <div class="modal-body">
                    <div class="comment-form mt-0">
                        <div class="row">
                            <?php if(Auth::guest()): ?>
                                <div class="col-xl-6 col-lg-12 col-md-6">
                                    <div class="form-inner">
                                        <input type="text" name="user_name"
                                            placeholder="<?php echo e(translate('Your Name')); ?> :" required>
                                    </div>
                                </div>
                                <div class="col-xl-6 col-lg-12 col-md-6">
                                    <div class="form-inner">
                                        <input type="email" name="user_email"
                                            placeholder="<?php echo e(translate('Your Email')); ?> :" required>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="col-12">
                                <div class="form-inner">
                                    <textarea name="comment" placeholder="<?php echo e(translate('Write Reply')); ?> :" rows="6" required></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-bs-dismiss="modal"><?php echo e(translate('Close')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(translate('Reply')); ?></button>
            </form>
        </div>
    </div>
</div>
</div>
<?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/frontend/template-1/partials/reply-modal.blade.php ENDPATH**/ ?>