<?php $__env->startSection('content'); ?>
    <div class="row mb-35">
        <div class="page-title d-flex justify-content-between align-items-center">
            <h4><?php echo e($page_title ?? ''); ?></h4>
            <div class="language-changer">
                <span><?php echo e(translate('Language Translation')); ?>: </span>
                <?php $__currentLoopData = \App\Models\Language::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($lang == $language->code): ?>
                        <img src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>" class="mr-3" height="16">
                    <?php else: ?>
                        <a href="<?php echo e(route('blog.category.edit', ['id' => $categorySingle->id, 'lang' => $language->code])); ?>"><img
                                src="<?php echo e(asset('assets/img/flags/' . $language->code . '.png')); ?>" class="mr-3"
                                height="16"></a>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <a href="<?php echo e(route('blog.category.list')); ?>" class="eg-btn btn--primary back-btn"> <img
                    src="<?php echo e(asset('backend/images/icons/back.svg')); ?>" alt="<?php echo e(translate('Go Back')); ?>">
                <?php echo e(translate('Go Back')); ?></a>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="eg-card product-card">
                <form action="<?php echo e(route('blog.category.update', $categorySingle->id)); ?>" method="POST"
                    enctype="multipart/form-data">
                    <input name="_method" type="hidden" value="PATCH">
                    <input type="hidden" name="lang" value="<?php echo e($lang); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="form-inner mb-35 row">
                        <label class="col-md-3 col-form-label"><?php echo e(translate('Name')); ?> <span
                                class="text-danger">*</span></label>
                        <div class="col-md-9">
                            <input type="text" value="<?php echo e(old('name', $categorySingle->getTranslation('name', $lang))); ?>"
                                name="name" class="username-input" placeholder="<?php echo e(translate('Enter Name')); ?>">
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="error text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="form-inner mb-25 row">
                        <label class="col-md-3 col-form-label"><?php echo e(translate('Image')); ?>*</label>
                        <div class="col-md-9">
                            <input type="file" name="image" class="password" accept="image/*">
                            <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="error text-danger"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <img src="<?php echo e(asset('uploads/blog/' . $categorySingle->image)); ?>"
                                alt="<?php echo e($categorySingle->name); ?>" id="previewImage" width="100">
                        </div>
                    </div>



                    <div class="button-group mt-15 text-center  ">
                        <input type="submit" class="eg-btn btn--green back-btn me-3" value="<?php echo e(translate('Update')); ?>">
                    </div>


                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/backend/blog/category_edit.blade.php ENDPATH**/ ?>