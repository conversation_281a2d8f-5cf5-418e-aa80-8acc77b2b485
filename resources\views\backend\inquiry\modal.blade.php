<!-- modal for inquiry details -->
<div class="modal fade" id="editModal" aria-hidden="true" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close position-absolute top-0 end-0 m-3" data-bs-dismiss="modal"
                    aria-label="Close"></button>
                <div class="row g-2">
                    <div class="col-lg-12">
                        <div class="review-from-wrapper">

                            <div class="page-title text-md-start text-center">
                                <h4 class="main_name">{{ translate('Inquiry Details') }}</h4>
                            </div>
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td><strong>{{ translate('Name') }}</strong></td>
                                            <td>
                                                <div class="cus_name"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ translate('Email') }}</strong></td>
                                            <td>
                                                <div class="cus_email"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ translate('Phone') }}</strong></td>
                                            <td>
                                                <div class="cus_phone"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>{{ translate('Type') }}</strong></td>
                                            <td>
                                                <div class="type text-capitalize"></div>
                                            </td>
                                        </tr>
                                        @admin
                                        <tr>
                                            <td><strong>{{ translate('Author Name') }}</strong></td>
                                            <td>
                                                <div class="agent"></div>
                                            </td>
                                        </tr>
                                        @endadmin
                                        <tr class="visa_type">
                                            <td><strong>{{ translate('Visa Type') }}</strong></td>
                                            <td>
                                                <div class="visa_types"></div>
                                            </td>
                                        </tr>
                                        <tr class="visa_type">
                                            <td><strong>{{ translate('Country Name') }}</strong></td>
                                            <td>
                                                <div class="country"></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2">
                                                <strong>{{ translate('Message') }}</strong>
                                                <div class="message"></div>
                                            </td>
                                        </tr>

                                        <tr class="visa_type">
                                            <td colspan="2">
                                                <div id="image-container"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                
                                <!-- Reply Button -->
                                <div class="modal-footer border-0 pt-3 text-center">
                                    <button type="button" class="eg-btn btn--primary me-2" id="replyBtn">
                                        <i class="bi bi-envelope me-2"></i>{{ translate('Compose Reply') }}
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="copyEmailBtn">
                                        <i class="bi bi-clipboard me-2"></i>{{ translate('Copy Email') }}
                                    </button>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Service Selection Modal -->
<div class="modal fade" id="emailServiceModal" aria-hidden="true" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title">Choose Your Email Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <p class="text-muted mb-4">Select your preferred email service to compose a reply</p>
                <div class="row g-3">
                    <div class="col-4">
                        <div class="email-service-card" data-service="gmail">
                            <div class="service-icon mb-2">
                                <div class="gmail-logo">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M24 5.457v13.909c0 .904-.732 1.636-1.636 1.636h-3.819V11.73L12 16.64l-6.545-4.91v9.273H1.636A1.636 1.636 0 0 1 0 19.366V5.457c0-.904.732-1.636 1.636-1.636h.818L12 12.728l9.546-8.907h.818c.904 0 1.636.732 1.636 1.636z" fill="#EA4335"/>
                                        <path d="M0 5.457c0-.904.732-1.636 1.636-1.636h.818L12 12.728l9.546-8.907h.818c.904 0 1.636.732 1.636 1.636L12 12.728 0 5.457z" fill="#FBBC04"/>
                                        <path d="M18.545 11.73v9.273h3.819c.904 0 1.636-.732 1.636-1.636V5.457L12 12.728l6.545-4.91z" fill="#34A853"/>
                                        <path d="M1.636 21.003h3.819V11.73L12 16.64 0 5.457v13.909c0 .904.732 1.636 1.636 1.636z" fill="#EA4335"/>
                                    </svg>
                                </div>
                            </div>
                            <h6 class="service-name">Gmail</h6>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="email-service-card" data-service="yahoo">
                            <div class="service-icon mb-2">
                                <div class="yahoo-logo">
                                    <svg width="48" height="24" viewBox="0 0 100 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect width="100" height="40" rx="6" fill="#6001D2"/>
                                        <text x="50" y="28" font-family="Arial Black, Arial" font-size="16" font-weight="900" text-anchor="middle" fill="white">Yahoo!</text>
                                    </svg>
                                </div>
                            </div>
                            <h6 class="service-name">Yahoo</h6>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="email-service-card" data-service="outlook">
                            <div class="service-icon mb-2">
                                <div class="outlook-logo">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M7.88 12.04q0 .45-.11.87-.1.41-.33.74-.22.33-.58.52-.37.2-.87.2t-.85-.2q-.35-.21-.57-.55-.22-.33-.33-.75-.1-.42-.1-.83 0-.42.1-.84.11-.41.33-.74.22-.33.57-.52.35-.2.85-.2t.87.2q.36.19.58.52.22.33.33.74.11.42.11.84zm-3.12 0q0 .58.22.94.21.35.58.35.19 0 .36-.09.18-.08.3-.24.12-.15.19-.36.07-.2.07-.6 0-.4-.07-.6-.07-.21-.19-.36-.12-.16-.3-.24-.17-.09-.36-.09-.37 0-.58.35-.22.36-.22.94zM24 2v20c0 1.1-.9 2-2 2H2c-1.1 0-2-.9-2-2V2c0-1.1.9-2 2-2h20c1.1 0 2 .9 2 2z" fill="#0078D4"/>
                                        <path d="M22 4H8v16h14V4z" fill="#0078D4"/>
                                        <path d="M8 4H2v16h6V4z" fill="#1BA1E2"/>
                                        <circle cx="5" cy="12" r="2.5" fill="white"/>
                                        <text x="5" y="14" font-family="Arial" font-size="3" font-weight="bold" text-anchor="middle" fill="#0078D4">O</text>
                                    </svg>
                                </div>
                            </div>
                            <h6 class="service-name">Outlook</h6>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <small class="text-muted">Or you can copy the email information using the "Copy Email" button</small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.email-service-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.email-service-card:hover {
    border-color: #6C2EB9;
    box-shadow: 0 4px 12px rgba(108, 46, 185, 0.15);
    transform: translateY(-2px);
}

.email-service-card.selected {
    border-color: #6C2EB9;
    background-color: #f8f9fa;
}

.service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 48px;
}

.service-name {
    font-weight: 600;
    color: #495057;
    margin: 0;
    font-size: 14px;
}

.email-service-card:hover .service-name {
    color: #6C2EB9;
}
</style>
