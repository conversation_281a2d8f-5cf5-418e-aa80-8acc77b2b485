<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('frontend/css/blog-standard.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('frontend.template-'.$templateId.'.breadcrumb.breadcrumb', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<div class="blog-standard-section pt-120 mb-120">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <?php echo $__env->make('frontend.template-'.$templateId.'.partials.blog-standard', ['blogs' => $blogs], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
           <div class="col-lg-4">
                    <div class="sidebar-area">
                        <div class="single-widget mb-30">
                            <h5 class="widget-title"><?php echo e(translate('Search From Blog')); ?></h5>
                            <form action="<?php echo e(route('blog.page')); ?>" method="GET">
                                <div class="search-box">
                                    <input type="text" name="search" placeholder="<?php echo e(translate('Search Here')); ?>" value="<?php echo e(request('search')); ?>">
                                    <button type="submit"><i class="bx bx-search"></i></button>
                                </div>
                            </form>
                        </div>
                        <?php
                            $categories = App\Models\BlogCategory::where('status', 1)->withCount('blogs')->get();
                        ?>
                        <?php if($categories->count() > 0): ?>
                            <div class="single-widget mb-30">
                                <h5 class="widget-title"><?php echo e(translate('Post Categories')); ?></h5>
                                <ul class="category-list">
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li>
                                            <a href="<?php echo e(route('blog.category', $category->slug)); ?>"><?php echo e($category->getTranslation('name')); ?>

                                                <span>(<?php echo e($category->blogs_count ?? 0); ?>)</span>
                                            </a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        <div class="single-widget mb-30">
                            <h5 class="widget-title"><?php echo e(translate('Recent Post')); ?></h5>
                            <?php
                                $recentBlogs = App\Models\Blog::where('status', 1)->latest()->take(5)->get();
                            ?>
                            <?php $__currentLoopData = $recentBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recentBlog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="recent-post-widget mb-20">
                                    <div class="recent-post-img">
                                        <a href="<?php echo e(route('blog.details', $recentBlog->slug)); ?>">
                                            <?php if($recentBlog->image): ?>
                                                <img alt="<?php echo e($recentBlog->title); ?>"
                                                    src="<?php echo e(asset('uploads/blog/' . $recentBlog->image)); ?>">
                                            <?php else: ?>
                                                <img alt="<?php echo e($recentBlog->title); ?>"
                                                    src="<?php echo e(asset('uploads/placeholder.jpg')); ?>">
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="recent-post-content">
                                        <a
                                            href="<?php echo e(route('blog.details', $recentBlog->slug)); ?>"><?php echo e($recentBlog->created_at->format('F d, Y')); ?></a>
                                        <h6><a
                                                href="<?php echo e(route('blog.details', $recentBlog->slug)); ?>"><?php echo e($recentBlog->getTranslation('title')); ?></a>
                                        </h6>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.template-'.$templateId.'.partials.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/frontend/template-1/blog-page.blade.php ENDPATH**/ ?>