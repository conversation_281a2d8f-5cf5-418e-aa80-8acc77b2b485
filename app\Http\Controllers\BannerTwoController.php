<?php

namespace App\Http\Controllers;
use App\Models\User;
use App\Models\BannerTwo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
class BannerTwoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
        $page_title="Advertisement";
        $advertisement=BannerTwo::all();
        return view('backend.advertisement.index',compact('advertisement','page_title'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
         $page_title="Advertisement Create";
         return view('backend.advertisement.create',compact('page_title'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        // dd($request->all());
        $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'text' => 'required|max:255',
            'phone' => 'required|max:255',
            'page' => 'required|max:255',
            'features_image' => 'required|mimes:jpeg,png,jpg,gif,svg,webp',
        ]);
          if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
          $advertisement = new BannerTwo;
         if ($request->hasFile('features_image')) {
            $features_image = $request->file('features_image');
            if ($features_image != '') {
                $features_image_name = pathinfo($features_image->getClientOriginalName(), PATHINFO_FILENAME) . '-' . time() . '.' . $features_image->getClientOriginalExtension();
                $features_image->move(public_path('uploads/advertisement'), $features_image_name);
                $advertisement->image = $features_image_name;
            }
        }
        $advertisement->text = $request->text;
        $advertisement->phone = $request->phone;
        $advertisement->title = $request->page;
      

        $advertisement->save();
         return redirect()->route('advertisement')->with('success', translate('Advertise saved successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(BannerTwo $bannerTwo)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BannerTwo $bannerTwo,$id)
    {
        //
        $page_title="Advertisement Edit";
        $advertisement=BannerTwo::findorfail($id);
        return view('backend.advertisement.edit',compact('advertisement','page_title'));
      
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BannerTwo $bannerTwo,$id)
    {
        //
       $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'text' => 'required|max:255',
            'phone' => 'required|max:255',
            'page' => 'required|max:255',
           
        ]);
          if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
          $advertisement = BannerTwo::findOrFail($id);
          
               if ($request->hasFile('features_image')) {
            $features_image = $request->file('features_image');
            if ($features_image != '') {
                $features_image_name = pathinfo($features_image->getClientOriginalName(), PATHINFO_FILENAME) . '-' . time() . '.' . $features_image->getClientOriginalExtension();
                $features_image->move(public_path('uploads/advertisement'), $features_image_name);
                $advertisement->image = $features_image_name;
            }
        }
          $advertisement->text = $request->text;
        $advertisement->phone = $request->phone;
        $advertisement->title = $request->page;
        $advertisement->update();
         return redirect()->route('advertisement')->with('success', translate('Advertise updated successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BannerTwo $bannerTwo,$id)
    {
        //
        $advertisement = BannerTwo::findOrFail($id)->delete();
      
        return redirect()->route('advertisement')->with('success', translate('Advertise deleted successfully'));
    }
}
