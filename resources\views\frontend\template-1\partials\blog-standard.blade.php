{{-- Blog Standard Design --}}
<div class="blog-standard-wrapper">
    @if(isset($blogs) && $blogs->count() > 0)
        @foreach($blogs as $blog)
            <article class="blog-card-modern mb-60">
                {{-- Blog Image --}}
                <div class="blog-image-container">
                    @if($blog->image)
                        <img src="{{ asset('uploads/blog/' . $blog->image) }}" alt="{{ $blog->title }}" class="blog-featured-image">
                    @else
                        <div class="blog-image-placeholder">
                            <i class="bi bi-image"></i>
                            <span>{{ translate('No Image') }}</span>
                        </div>
                    @endif
                    
                    {{-- Date Badge --}}
                    <div class="date-badge">
                        <div class="date-day">{{ $blog->created_at->format('d') }}</div>
                        <div class="date-month">{{ $blog->created_at->format('M') }}</div>
                    </div>
                </div>
                
                {{-- Blog Content --}}
                <div class="blog-content-area">
                    {{-- Blog Stats --}}
                    <div class="blog-stats">
                        <span class="stat-item">
                            <i class="bi bi-eye"></i>
                            {{ rand(500, 2000) }} {{ translate('Views') }}
                        </span>
                        <span class="stat-separator">|</span>
                        <span class="stat-item">
                            <i class="bi bi-clock"></i>
                            {{ rand(1, 10) }} {{ translate('Min Read') }}
                        </span>
                        <span class="stat-separator">|</span>
                        <span class="stat-item">
                            <i class="bi bi-chat"></i>
                            @php
                                $commentCount = App\Models\BlogComment::where('blog_id', $blog->id)->count();
                            @endphp
                            ({{ $commentCount }}) {{ translate('Comments') }}
                        </span>
                    </div>
                    
                    {{-- Blog Title --}}
                    <h2 class="blog-title-modern">
                        <a href="{{ route('blog.details', $blog->slug) }}">
                            {{ $blog->getTranslation('title') }}
                        </a>
                    </h2>
                    
                    {{-- Blog Excerpt --}}
                    <div class="blog-excerpt-modern">
                        {{ Str::limit(strip_tags($blog->getTranslation('description')), 200, '...') }}
                    </div>
                    
                    {{-- Read More Link --}}
                    <div class="blog-read-more">
                        <a href="{{ route('blog.details', $blog->slug) }}" class="read-more-link">
                            {{ translate('View Post') }}
                            <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </article>
        @endforeach
        
        {{-- Pagination --}}
        @if($blogs->hasPages())
            <div class="blog-pagination">
                {{ $blogs->links() }}
            </div>
        @endif
    @else
        {{-- No Blogs Found --}}
        <div class="no-blogs-found">
            <div class="empty-state">
                <i class="bi bi-journal-x"></i>
                <h4>{{ translate('No Blogs Found') }}</h4>
                <p>{{ translate('There are no blog posts available at the moment.') }}</p>
            </div>
        </div>
    @endif
</div>
