@extends('backend.layouts.master')
              @section('content')

                <div class="row mb-35 g-4">
                    <div class="col-md-4">
                        <div class="page-title text-md-start">
                            <h4>{{$page_title ?? ''}}</h4>
                        </div>
                    </div>
                    <div class="col-md-8 text-md-end text-center d-flex justify-content-md-end justify-content-center flex-row align-items-center flex-wrap gap-4">
                        <!-- <form action="" method="get">
                            <div class="input-with-btn d-flex jusify-content-start align-items-strech">
                                <input type="text" name="search" placeholder="{{translate('Search your agent')}}...">
                                <button type="submit"><i class="bi bi-search"></i></button>
                            </div>
                        </form> -->
                        <a href="{{route('admin.create')}}" class="eg-btn btn--primary back-btn"><img src="{{asset('backend/images/icons/add-icon.svg')}}" alt="{{translate('Add New')}}"> {{translate('Add New')}}</a>
                    </div>
                </div>


    <div class="row">
        @if ($admins->count() > 0)
            @foreach ($admins as $admins)
                <div class="col-lg-3 col-md-6 col-sm-6">
                    <div class="eg-profile-card text-center">
                        <div class="profile-img">
                            @if ($admins->image)
                                <img class="rounded-circle" src="{{ asset('uploads/users/' . $admins->image) }}"
                                    alt="{{ $admins->username }}">
                            @else
                                <img class="rounded-circle" src="{{ asset('uploads/users/user.png') }}"
                                    alt="{{ $admins->username }}">
                            @endif

                            

                            <span id="statusBlock{{ $admins->id }}">
                                @if ($admins->status == 1)
                                    <button class="eg-btn green-light--btn">{{ translate('Active') }}</button>
                                @else
                                    <button class="eg-btn red-light--btn">{{ translate('Deactive') }}</button>
                                @endif
                            </span>
                        </div>
                        <div class="profile-bio">
                            <h4>{{ $admins->fname . ' ' . $admins->lname }}</h3>
                                <!-- <h6>Marchant ID: {{ $admins->custom_id }}</h5> -->
                        </div>
                        <div class="card-action d-flex justify-content-sm-between">
                            <div
                                class="d-flex flex-row justify-content-md-center justify-content-end align-items-center gap-2">
                                <a href="{{ route('admin.edit', $admins->id) }}" title="{{ translate('Edit') }}"
                                    class="eg-btn add--btn"><i class="bi bi-pencil-square"></i></a>
                                <form method="POST" action="{{ route('admin.delete', $admins->id) }}">
                                    @csrf
                                    <input name="_method" type="hidden" value="DELETE">
                                    <button type="submit" class="eg-btn delete--btn show_confirm" data-toggle="tooltip"
                                        title='Delete'><i class="bi bi-trash"></i></button>
                                </form>

                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input flexSwitchCheckStatus" type="checkbox"
                                    data-activations-status="{{ $admins->status }}"
                                    data-id="{{ $admins->id }}" data-type="merchant" id="flexSwitchCheckStatus{{ $admins->id }}"
                                    {{ $admins->status == 1 ? 'checked' : '' }}>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <div class="col-lg-12 col-md-12 col-sm-12">
                <h1>{{ translate('No Data Found') }}</h1>
            </div>
        @endif
    </div>
    </div>

    @push('footer')

    @endpush
@endsection
