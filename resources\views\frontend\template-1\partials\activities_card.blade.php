<div class="activity-card">
    @if ($item->feature_img)
        <img src="{{ asset('uploads/activities/features/' . $item->feature_img) }}" alt="{{ $item->title }}">
    @else
        <img src="{{ asset('uploads/placeholder.jpg') }}" alt="{{ $item->title }}">
    @endif
    <a href="{{ route('activities.details', $item->slug) }}" class="country-name">
        @if ($item->country_id)
            <img src="{{ asset('assets/img/flags/'.$item['countries']['country_code'].'.png') }}" alt="{{ $item->title }}">
        @else
            <img src="{{ asset('uploads/placeholder.jpg') }}" alt="{{ $item->title }}">
        @endif

        {{ $item['countries']['name'] }}
    </a>
    <div class="activity-card-content-wrapper">
        <div class="activity-card-content">
            <div class="icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 54 54">
                    <path
                        d="M53.9613 0.873077C53.9273 0.718694 53.8714 0.574945 53.7967 0.450038C53.722 0.325131 53.63 0.221513 53.5259 0.1451C53.4218 0.0686872 53.3077 0.0209766 53.1901 0.0046927C53.0725 -0.0115912 52.9537 0.00387037 52.8405 0.0501948L42.599 4.23978C37.5384 6.31095 32.2829 7.36289 26.9993 7.36225C25.8068 7.36225 24.6017 7.30441 23.4075 7.19657L23.353 6.97412C23.2933 6.72978 23.1787 6.51725 23.0255 6.36665C22.8722 6.21604 22.6882 6.13499 22.4993 6.13498H18.8993C18.6927 6.13512 18.4925 6.23225 18.3323 6.41004C15.9892 5.89338 13.6733 5.16846 11.3995 4.23993L1.1581 0.0503481C0.929457 -0.0432306 0.682922 -0.00912245 0.472725 0.145169C0.262528 0.299461 0.105887 0.561297 0.0372628 0.873077C-0.0313617 1.18486 -0.00634906 1.52104 0.106798 1.80767C0.219946 2.0943 0.411959 2.30791 0.640598 2.40148L10.8822 6.59106C13.0245 7.46641 15.2031 8.16689 17.4065 8.68877L17.1942 9.26788C17.1317 9.43831 17.0992 9.62624 17.0992 9.81678C17.0992 10.0073 17.1317 10.1953 17.1942 10.3657L17.9993 12.5611V19.2634L15.2993 24.7861V23.3167C15.538 23.3167 15.7669 23.1874 15.9357 22.9573C16.1045 22.7271 16.1993 22.4149 16.1993 22.0894V19.6349C16.1993 18.0075 15.7252 16.4467 14.8813 15.2959C14.0374 14.1451 12.8928 13.4986 11.6993 13.4986C10.5058 13.4986 9.36125 14.1451 8.51734 15.2959C7.67343 16.4467 7.19933 18.0075 7.19933 19.6349V22.0894C7.19933 22.4149 7.29415 22.7271 7.46293 22.9573C7.63171 23.1874 7.86063 23.3167 8.09932 23.3167V25.7712C8.10006 26.5677 8.24269 27.352 8.51492 28.0567C8.78716 28.7614 9.18087 29.3654 9.66217 29.8166C9.37833 30.2313 9.11967 30.6768 8.88873 31.1487C8.63703 31.6634 8.46711 32.2443 8.38992 32.8539C8.31272 33.4636 8.32995 34.0885 8.4405 34.6886C8.55105 35.2886 8.75247 35.8504 9.03181 36.3379C9.31115 36.8255 9.66222 37.2279 10.0624 37.5192C10.0924 37.5782 10.126 37.6335 10.163 37.6845L16.9449 46.9324C17.4452 47.6183 18.0402 48.1622 18.6956 48.5324C19.351 48.9026 20.0538 49.0919 20.7632 49.0893H20.8082C22.1162 49.0894 23.3798 48.442 24.3641 47.2674L29.6578 40.9508L35.3625 48.7299L38.9625 53.6389C39.046 53.7529 39.1453 53.8433 39.2545 53.905C39.3636 53.9667 39.4807 53.9984 39.5989 53.9984C39.7171 53.9984 39.8341 53.9667 39.9433 53.905C40.0525 53.8433 40.1517 53.7529 40.2353 53.6389L45.6353 46.2753C45.7188 46.1614 45.7851 46.0261 45.8304 45.8772C45.8756 45.7283 45.8989 45.5687 45.8989 45.4075C45.8989 45.2463 45.8756 45.0867 45.8304 44.9378C45.7851 44.7889 45.7188 44.6536 45.6353 44.5397L44.454 42.9289L45.8531 37.2054C45.9059 36.9892 45.9136 36.7571 45.8752 36.5353C45.8368 36.3135 45.7539 36.1106 45.6357 35.9494L43.8357 33.4949C43.7022 33.3127 43.53 33.1924 43.3435 33.1511C43.157 33.1098 42.9658 33.1496 42.7968 33.2648L39.5667 35.4666L32.3241 31.3157C31.5847 30.4136 30.6759 29.8096 29.6992 29.5713V25.7712C29.6993 25.5136 29.6398 25.2626 29.5294 25.0536C29.4189 24.8446 29.2631 24.6883 29.0839 24.6069L27.1376 23.7223C27.1376 23.7223 27.8993 18.4883 27.8993 18.4076V9.80558C33.0559 9.69391 38.1765 8.61225 43.1162 6.59122L53.3579 2.40148C53.4711 2.35516 53.5766 2.27888 53.6681 2.177C53.7597 2.07511 53.8357 1.94962 53.8917 1.8077C53.9478 1.66577 53.9827 1.51018 53.9947 1.34981C54.0066 1.18945 53.9952 1.02745 53.9613 0.873077ZM26.0993 9.80481V18.2861L25.8689 19.857L23.9556 12.2713L24.2042 11.5931C24.2571 11.4489 24.2886 11.292 24.297 11.1312C24.3053 10.9704 24.2904 10.809 24.253 10.6561L24.0209 9.70617C24.7133 9.75629 25.406 9.78917 26.0993 9.80481ZM12.8316 37.8523C13.8362 37.5387 14.7552 36.8439 15.4823 35.8482L17.9247 32.5177L20.3933 36.7255L16.1993 42.4446L12.8316 37.8523ZM10.3863 32.5102C10.7804 31.7031 11.2794 31.0011 11.8588 30.4391C11.8747 30.4246 11.8901 30.4093 11.9053 30.3931C12.271 30.046 12.6645 29.757 13.0782 29.5316L15.0825 28.4382L16.7265 30.6803L14.2095 34.1125C13.8669 34.5822 13.4595 34.9546 13.0106 35.2081C12.5618 35.4616 12.0806 35.5912 11.5948 35.5894C11.3319 35.5894 11.0739 35.4921 10.8484 35.3079C10.6228 35.1237 10.4382 34.8595 10.3142 34.5434C10.1901 34.2273 10.1313 33.8712 10.1441 33.5131C10.1568 33.1551 10.2404 32.8084 10.3863 32.5102ZM19.1275 30.6386L23.2316 22.8028C23.3407 22.5946 23.3993 22.3452 23.3993 22.0894V17.6122L24.9919 23.927L23.8967 24.6736C23.7473 24.7755 23.6215 24.9322 23.5337 25.1261C23.4458 25.3199 23.3993 25.5433 23.3993 25.7712V27.7952L20.4293 32.8576L19.1275 30.6386ZM19.6483 20.3159C19.7468 20.1142 19.7994 19.8772 19.7993 19.6349V12.2713C19.7993 12.0808 19.7667 11.8928 19.7042 11.7224L19.0055 9.81678L19.4555 8.58951H21.8506L21.8892 8.74722L21.8927 8.76164L22.4276 10.9499L21.6943 12.9497C21.6318 13.1201 21.5993 13.308 21.5993 13.4986V21.6963L17.8844 28.788L16.4574 26.842L19.6483 20.3159ZM8.99932 19.6349C8.99932 18.6584 9.28378 17.722 9.79013 17.0315C10.2965 16.341 10.9832 15.9531 11.6993 15.9531C12.4154 15.9531 13.1021 16.341 13.6085 17.0315C14.1148 17.722 14.3993 18.6584 14.3993 19.6349V20.8622H8.99932V19.6349ZM13.4993 23.3167V25.7712C13.4993 26.1035 13.4498 26.4323 13.3538 26.7377L12.4097 27.2526C11.9776 27.4884 11.562 27.7773 11.1682 28.1158C10.801 27.9605 10.4799 27.6493 10.2516 27.2275C10.0233 26.8057 9.89989 26.2955 9.89932 25.7712V23.3167H13.4993ZM29.1066 38.3475L23.179 45.4196C22.5229 46.2029 21.6805 46.6347 20.8085 46.6348H20.7635C20.2906 46.6365 19.8221 46.5104 19.3851 46.2635C18.9482 46.0167 18.5515 45.6542 18.218 45.1969L17.4721 44.1802L22.2068 37.7234L26.3243 33.0454C26.9714 32.3109 27.7865 31.9084 28.6285 31.9076H28.7354C29.2083 31.9058 29.6768 32.032 30.1137 32.2788C30.5507 32.5256 30.9474 32.8882 31.2809 33.3455L38.3264 42.953L35.9992 46.1264L30.3357 38.4033C30.1742 38.1832 29.9574 38.0549 29.7292 38.0445C29.501 38.0342 29.2784 38.1425 29.1066 38.3475ZM39.5992 51.0356L37.272 47.862L40.0854 44.0256L42.0561 43.1299L43.7265 45.4075L39.5992 51.0356ZM43.0215 35.8554L43.9695 37.1482L43.0305 40.9878L42.9352 40.8579C42.817 40.6967 42.6682 40.5836 42.5055 40.5312C42.3429 40.4788 42.1727 40.4893 42.0141 40.5613L40.4992 41.2501V37.5751L43.0215 35.8554ZM38.6992 39.99L35.7163 35.9224L38.6992 37.632V39.99ZM27.8993 29.5215C26.8954 29.7086 25.9503 30.2763 25.1715 31.1599L21.729 35.0718L21.5827 34.8219L25.0027 28.9924C25.1301 28.7746 25.1994 28.5043 25.1993 28.2258V26.5297L26.1683 25.8691L27.8993 26.6558V29.5215Z" />
                </svg>
            </div>

            <div class="content">
                <h6><a href="{{ route('activities.details', $item->slug) }}">{{ $item->getTranslation('title') }}</a></h6>
            </div>
        </div>
    </div>
</div>
