<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('frontend.template-' . $templateId . '.breadcrumb.breadcrumb', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="blog-details-section pt-120 mb-120">
        <div class="container">
            <div class="row g-lg-4 gy-5 justify-content-center">
                <div class="col-lg-8">
                    <div class="post-thumb mb-30">
                        <?php if(fileExists('uploads/blog/', $blog_details->image) != false && $blog_details->image != null): ?>
                            <img class="img-fluid" src="<?php echo e(asset('uploads/blog/' . $blog_details->image)); ?>"
                                alt="<?php echo e($blog_details->title); ?>">
                        <?php else: ?>
                            <img src="<?php echo e(asset('uploads/author-cover-placeholder.webp')); ?>" alt="<?php echo e($blog_details->title); ?>">
                        <?php endif; ?>
                    </div>
                    <div class="post-title mb-40">
                        <h1><?php echo e($blog_details->getTranslation('title')); ?></h1>
                    </div>
                    <div class="blog-meta two mb-50">
                        <div class="author-area">
                            <div class="author-img">
                                <?php if($blog_details->users?->image): ?>
                                    <img src="<?php echo e(asset('uploads/users/' . $blog_details->users?->image)); ?>"
                                        alt="<?php echo e($blog_details->users?->username); ?>">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('uploads/users/user.png')); ?>"
                                        alt="<?php echo e($blog_details->users?->username); ?>">
                                <?php endif; ?>
                            </div>
                            <div class="author-content">
                                <h6><?php echo e(translate('By')); ?>, <a href="#">
                                        <?php echo e($blog_details->users?->fname ? $blog_details->users?->fname . ' ' . $blog_details->users?->lname : ''); ?></a>
                                </h6>
                            </div>
                        </div>
                        <ul>
                            <li>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                                    <g>
                                        <path
                                            d="M8 0C3.60594 0 0 3.60594 0 8C0 12.3941 3.60594 16 8 16C12.3941 16 16 12.3941 16 8C16 3.60594 12.3941 0 8 0ZM11.646 3.69106C11.8291 3.508 12.1259 3.508 12.3089 3.69106C12.492 3.87413 12.492 4.17091 12.3089 4.35397C12.1259 4.53703 11.8291 4.53703 11.646 4.35397C11.463 4.17091 11.463 3.87413 11.646 3.69106ZM7.53125 2.375C7.53125 2.11591 7.74091 1.90625 8 1.90625C8.25909 1.90625 8.46875 2.11591 8.46875 2.375V3.3125C8.46875 3.57159 8.25909 3.78125 8 3.78125C7.74091 3.78125 7.53125 3.57159 7.53125 3.3125V2.375ZM2.375 8.46875C2.11591 8.46875 1.90625 8.25909 1.90625 8C1.90625 7.74091 2.11591 7.53125 2.375 7.53125H3.3125C3.57159 7.53125 3.78125 7.74091 3.78125 8C3.78125 8.25909 3.57159 8.46875 3.3125 8.46875H2.375ZM4.35397 12.3089C4.17091 12.492 3.87413 12.492 3.69106 12.3089C3.508 12.1259 3.508 11.8291 3.69106 11.646C3.87413 11.4629 4.17091 11.4629 4.35397 11.646C4.53703 11.8291 4.53703 12.1259 4.35397 12.3089ZM4.35397 4.35397C4.17091 4.53703 3.87413 4.53703 3.69106 4.35397C3.508 4.17091 3.508 3.87413 3.69106 3.69106C3.87413 3.508 4.17091 3.508 4.35397 3.69106C4.53703 3.87413 4.53703 4.17091 4.35397 4.35397ZM8.46875 13.625C8.46875 13.8841 8.25909 14.0938 8 14.0938C7.74091 14.0938 7.53125 13.8841 7.53125 13.625V12.6875C7.53125 12.4284 7.74091 12.2188 8 12.2188C8.25909 12.2188 8.46875 12.4284 8.46875 12.6875V13.625ZM11.1439 11.1439C10.9608 11.327 10.6642 11.327 10.4811 11.1439L7.66856 8.33141C7.58069 8.24353 7.53125 8.1245 7.53125 8V5.1875C7.53125 4.92841 7.74091 4.71875 8 4.71875C8.25909 4.71875 8.46875 4.92841 8.46875 5.1875V7.80591L11.1439 10.4811C11.327 10.6642 11.327 10.9608 11.1439 11.1439ZM12.3089 12.3089C12.1259 12.492 11.8291 12.492 11.646 12.3089C11.463 12.1259 11.463 11.8291 11.646 11.646C11.8291 11.4629 12.1259 11.4629 12.3089 11.646C12.492 11.8291 12.492 12.1259 12.3089 12.3089ZM14.0938 8C14.0938 8.25909 13.8841 8.46875 13.625 8.46875H12.6875C12.4284 8.46875 12.2188 8.25909 12.2188 8C12.2188 7.74091 12.4284 7.53125 12.6875 7.53125H13.625C13.8841 7.53125 14.0938 7.74091 14.0938 8Z" />
                                    </g>
                                </svg>
                                <?php echo e(calculate_read_time(strip_tags($blog_details->description))); ?>

                                <?php echo e(translate('Min Read')); ?>

                            </li>
                            <li>
                                <a href="#comment-area">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                        viewBox="0 0 16 16">
                                        <g>
                                            <path
                                                d="M14.5672 14.9619C14.5917 15.0728 14.5913 15.1878 14.5658 15.2986C14.5403 15.4093 14.4905 15.513 14.42 15.6021C14.3494 15.6912 14.2599 15.7635 14.158 15.8136C14.056 15.8638 13.9441 15.8906 13.8305 15.8922C13.7133 15.8916 13.5977 15.8643 13.4925 15.8124L12.1483 15.1555C10.8921 15.6143 9.51644 15.6277 8.2515 15.1936C6.98655 14.7595 5.90904 13.9042 5.19922 12.7708C6.15026 12.8941 7.11661 12.8159 8.03545 12.5413C8.95429 12.2667 9.80505 11.8018 10.5324 11.1768C11.2598 10.5518 11.8476 9.78079 12.2575 8.91379C12.6674 8.0468 12.8902 7.10326 12.9116 6.14449C12.9119 5.70944 12.8674 5.27551 12.7787 4.84961C13.6879 5.29062 14.4611 5.96909 15.0165 6.81329C15.572 7.65749 15.8891 8.63608 15.9342 9.64561C15.9643 10.4111 15.8346 11.1744 15.5535 11.887C15.2724 12.5996 14.846 13.2459 14.3014 13.7847L14.5672 14.9619Z" />
                                            <path
                                                d="M6.0757 0.216195C4.48484 0.198449 2.95187 0.812289 1.81293 1.92312C0.673981 3.03395 0.0220199 4.5511 1.29169e-06 6.1419C-0.000538167 6.94954 0.167902 7.74837 0.494497 8.48703C0.821091 9.22569 1.29861 9.88786 1.89638 10.431L1.65183 11.7365C1.63148 11.8461 1.63545 11.9588 1.66346 12.0668C1.69147 12.1747 1.74285 12.2751 1.81395 12.361C1.88505 12.4469 1.97414 12.5161 2.07493 12.5638C2.17572 12.6114 2.28575 12.6364 2.39724 12.6368C2.52333 12.6366 2.64739 12.6052 2.75837 12.5453L4.19679 11.7726C4.8041 11.9674 5.43791 12.067 6.0757 12.068C7.66662 12.0857 9.19965 11.4718 10.3386 10.3609C11.4776 9.25002 12.1295 7.73277 12.1514 6.1419C12.1294 4.5511 11.4774 3.03395 10.3385 1.92312C9.19953 0.812289 7.66656 0.198449 6.0757 0.216195ZM3.79731 7.05136C3.64711 7.05136 3.50027 7.00681 3.37538 6.92336C3.25049 6.83991 3.15314 6.7213 3.09566 6.58253C3.03818 6.44375 3.02314 6.29105 3.05244 6.14373C3.08175 5.99641 3.15408 5.86109 3.26029 5.75487C3.36651 5.64866 3.50183 5.57633 3.64915 5.54702C3.79647 5.51772 3.94917 5.53276 4.08795 5.59024C4.22672 5.64772 4.34533 5.74507 4.42878 5.86996C4.51223 5.99485 4.55678 6.14169 4.55678 6.29189C4.55678 6.49332 4.47676 6.68649 4.33433 6.82891C4.19191 6.97134 3.99874 7.05136 3.79731 7.05136ZM6.0757 7.05136C5.92549 7.05136 5.77866 7.00681 5.65377 6.92336C5.52887 6.83991 5.43153 6.7213 5.37405 6.58253C5.31657 6.44375 5.30153 6.29105 5.33083 6.14373C5.36013 5.99641 5.43247 5.86109 5.53868 5.75487C5.64489 5.64866 5.78022 5.57633 5.92754 5.54702C6.07486 5.51772 6.22756 5.53276 6.36633 5.59024C6.50511 5.64772 6.62372 5.74507 6.70717 5.86996C6.79062 5.99485 6.83516 6.14169 6.83516 6.29189C6.83516 6.49332 6.75515 6.68649 6.61272 6.82891C6.47029 6.97134 6.27712 7.05136 6.0757 7.05136ZM8.35409 7.05136C8.20388 7.05136 8.05704 7.00681 7.93215 6.92336C7.80726 6.83991 7.70992 6.7213 7.65244 6.58253C7.59495 6.44375 7.57991 6.29105 7.60922 6.14373C7.63852 5.99641 7.71085 5.86109 7.81707 5.75487C7.92328 5.64866 8.0586 5.57633 8.20592 5.54702C8.35324 5.51772 8.50595 5.53276 8.64472 5.59024C8.78349 5.64772 8.90211 5.74507 8.98556 5.86996C9.06901 5.99485 9.11355 6.14169 9.11355 6.29189C9.11355 6.49332 9.03354 6.68649 8.89111 6.82891C8.74868 6.97134 8.55551 7.05136 8.35409 7.05136Z" />
                                        </g>
                                    </svg>
                                    <?php echo e($blog_details->comments?->count() < 9 && $blog_details->comments?->count() != 0 ? '0' . $blog_details->comments?->count() : $blog_details->comments?->count()); ?>

                                    <?php echo e($blog_details->comments?->count() > 1 ? 'Comments' : 'Comment'); ?>

                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="blog-content">
                        <?php
                            $description = prelaceScript(
                                html_entity_decode($blog_details->getTranslation('description')),
                            );
                        ?>
                        <?php echo clean($description); ?>

                    </div>
                    <?php
                        $tags = explode(',', $blog_details->tags);
                    ?>
                    <div class="tag-and-social-area mb-30">
                        <?php if($tags): ?>
                            <div class="bolg-tag">
                                <ul class="tag-list">
                                    <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li>
                                            <a href="<?php echo e(url('blog/tag/' . $tag)); ?>"><?php echo e($tag); ?></a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        <div class="social-area">
                            <h6>Share On:</h6>
                            <ul class="social-link">
                                <li>
                                    <a
                                        href="https://www.facebook.com/sharer.php?u=<?php echo e(url('blog/' . $blog_details->slug)); ?>"><i
                                            class="bx bxl-facebook"></i></a>
                                </li>
                                <li>
                                    <a
                                        href="https://twitter.com/intent/tweet?url=<?php echo e(url('blog/' . $blog_details->slug)); ?>&text=<?php echo e($blog_details->getTranslation('name')); ?>"><svg
                                            xmlns="http://www.w3.org/2000/svg" width="12" height="12"
                                            fill="currentColor" class="bi bi-twitter-x" viewBox="0 0 16 16">
                                            <path
                                                d="M12.6.75h2.454l-5.36 6.142L16 15.25h-4.937l-3.867-5.07-4.425 5.07H.316l5.733-6.57L0 .75h5.063l3.495 4.633L12.601.75Zm-.86 13.028h1.36L4.323 2.145H2.865l8.875 11.633Z" />
                                        </svg></a>
                                </li>
                                <li>
                                    <a
                                        href="https://www.linkedin.com/shareArticle?url=<?php echo e(url('blog/' . $blog_details->slug)); ?>&title=<?php echo e($blog_details->getTranslation('name')); ?>"><i
                                            class="bx bxl-linkedin"></i></a>
                                </li>
                                <li>
                                    <a
                                        href="https://www.pinterest.com/pin/create/bookmarklet/?url==<?php echo e(url('blog/' . $blog_details->slug)); ?>"><i
                                            class='bx bxl-pinterest-alt'></i></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <div class="row mb-100">
                            <div class="col-lg-12">
                                <div class="details-navigation">
                                    <div class="single-navigation">
                                        <a class="arrow" href="#blog-details">
                                            <svg width="9" height="15" viewBox="0 0 8 13" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0 6.50008L8 0L2.90909 6.50008L8 13L0 6.50008Z"></path>
                                            </svg>
                                        </a>
                                        <div class="content">
                                            <a href="blog-details">Prev Post</a>
                                            <h6><a href="blog-details">Consulting vs. In-House Expertise: Finding the Right Balance</a></h6>
                                        </div>
                                    </div>
                                    <div class="single-navigation two text-end">
                                        <div class="content">
                                            <a href="blog-details">Next Post</a>
                                            <h6><a href="blog-details">Consulting Industry Adapts to the Changing Business Landscape</a></h6>
                                        </div>
                                        <a class="arrow" href="blog-details">
                                            <svg width="9" height="15" viewBox="0 0 8 13" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M8 6.50008L0 0L5.09091 6.50008L0 13L8 6.50008Z"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                    <?php echo $__env->make('frontend.template-1.partials.blog-comment', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>
                <div class="col-lg-4">
                    <div class="sidebar-area">
                        <div class="single-widget mb-30">
                            <h5 class="widget-title"><?php echo e(translate('Search From Blog')); ?></h5>
                            <form action="<?php echo e(route('blog.page')); ?>" method="GET">
                                <div class="search-box">
                                    <input type="text" name="search" placeholder="<?php echo e(translate('Search Here')); ?>">
                                    <button type="submit"><i class="bx bx-search"></i></button>
                                </div>
                            </form>
                        </div>
                        <?php if($categories->count() > 0): ?>
                            <div class="single-widget mb-30">
                                <h5 class="widget-title"><?php echo e(translate('Post Categories')); ?></h5>
                                <ul class="category-list">
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li>
                                            <a href="<?php echo e(url('blog/category/' . $category->slug)); ?>"><?php echo e($category->getTranslation('name')); ?>

                                                <span>(<?php echo e($category->blogs->count() ?? 0); ?>)</span>
                                            </a>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        <div class="single-widget mb-30">
                            <h5 class="widget-title"><?php echo e(translate('Recent Post')); ?></h5>
                            <?php $__currentLoopData = $recentBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $recentBlog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="recent-post-widget mb-20">
                                    <div class="recent-post-img">
                                        <a href="<?php echo e(url('blog/' . $recentBlog->slug)); ?>">
                                            <?php if($recentBlog->image): ?>
                                                <img alt="<?php echo e($recentBlog->title); ?>"
                                                    src="<?php echo e(asset('uploads/blog/' . $recentBlog->image)); ?>">
                                            <?php else: ?>
                                                <img alt="<?php echo e($recentBlog->title); ?>"
                                                    src="<?php echo e(asset('uploads/placeholder.jpg')); ?>">
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                    <div class="recent-post-content">
                                        <a
                                            href="<?php echo e(url('blog/' . $recentBlog->slug)); ?>"><?php echo e(date('F d, Y', strtotime($recentBlog->created_at))); ?></a>
                                        <h6><a
                                                href="<?php echo e(url('blog/' . $recentBlog->slug)); ?>"><?php echo e($recentBlog->getTranslation('title', $lang)); ?></a>
                                        </h6>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('frontend.template-1.partials.reply-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.template-' . $templateId . '.partials.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\triprex-apps\resources\views/frontend/template-1/blog-details.blade.php ENDPATH**/ ?>